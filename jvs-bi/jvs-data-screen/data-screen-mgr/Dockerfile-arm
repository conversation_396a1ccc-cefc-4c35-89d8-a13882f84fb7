FROM registry.cn-hangzhou.aliyuncs.com/rkqf/jdk1.8-arm:latest
MAINTAINER guojing <<EMAIL>>

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone
ADD ./target/data-screen-mgr.jar /app/app.jar
ENV skyname="data-screen-mgr"
ENV JAVA_OPTS=""
ENV skyip="localhost:11800"
ENV authentication=""
ENV skywalkingPath=""
ENV nacosAddr="cloud-nacos:8848"
ENV namespace=""
ENTRYPOINT ["sh","-c","java $skywalkingPath  -Dskywalking.agent.service_name=$skyname -Dskywalking.agent.authentication=$authentication -Dskywalking.collector.backend_service=$skyip -Dspring.cloud.nacos.discovery.server-addr=$nacosAddr -Dspring.cloud.nacos.discovery.namespace=$namespace  $JAVA_OPTS -jar /app/app.jar"]