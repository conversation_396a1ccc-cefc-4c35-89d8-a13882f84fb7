package cn.bctools.report.data.impl.fill;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.report.data.DataParserFactory;
import cn.bctools.report.data.FillDataExecute;
import cn.bctools.report.data.constant.Constant;
import cn.bctools.report.data.impl.TableFormulaDataNode;
import cn.bctools.report.data.util.CellFunctionUtil;
import cn.bctools.report.data.util.FillCellDataUtils;
import cn.bctools.report.data.util.SortUtil;
import cn.bctools.report.dto.ConditionDto;
import cn.bctools.report.dto.DynamicCacheDto;
import cn.bctools.report.dto.DynamicCellMergeDto;
import cn.bctools.report.dto.SubtotalConfigurationDto;
import cn.bctools.report.enums.CellIdentifierEnum;
import cn.bctools.data.factory.enums.DataFieldTypeClassifyEnum;
import cn.bctools.report.enums.SortEnums;
import cn.bctools.report.enums.StatisticalMethodEnum;
import cn.bctools.report.po.CellData;
import cn.bctools.report.po.ReportDataPo;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 动态属性节点 与横向拓展 竖向拓展分组配合
 */
@Slf4j
@Component
public class FillDynamicTableDataNode implements FillDataExecute {

    private static final Map<String,Object> ct = new HashMap<>();
    static {
        ct.put("fa","General");
        ct.put("t","n");
    }

    @Override
    public void generatedData(CellData cell, ReportDataPo reportDataPo) {
        Map<String, List<Map<String, Object>>> sourceData = DataParserFactory.TABLE_DATA_CACHE.get();

        //1.获取主数据源数据
        String dataSource = FillCellDataUtils.getDataSource(cell);
        List<Map<String, Object>> mainSource = sourceData.getOrDefault(dataSource, Collections.emptyList());
        if(CollectionUtil.isEmpty(mainSource)){
            cell.setExecList(Collections.emptyList());
            return;
        }

        //2.获取分组条件以及需要渲染的动态数据
        CellData.DynamicGroup dynamicGroup = cell.getDynamicGroup();
        if(CollectionUtil.isEmpty(dynamicGroup.getLeftGroup()) || CollectionUtil.isEmpty(dynamicGroup.getTopGroup())){
            cell.setExecList(Collections.emptyList());
            return;
        }

        List<CellData> dynamicData = Optional.ofNullable( dynamicGroup.getDynamicData()).orElse(new ArrayList<>());
        CellData copy = BeanCopyUtil.copy(cell, CellData.class);
        dynamicData.add(0,copy);

        DynamicCacheDto dynamicCacheDto = new DynamicCacheDto().setDynamicSize(dynamicData.size());

        //3.获取顶部分组数据
        List<CellData> topGroup = dynamicGroup.getTopGroup();
        ListUtil.reverse(topGroup);

        //4. 获取横向
        List<CellData> topCells = getGroup(mainSource, topGroup, true,dynamicCacheDto);
        //4.1 横向条件
        LinkedHashMap<Integer, List<CellData>> topCondition = topCells.stream().collect(Collectors.groupingBy(CellData::getC, LinkedHashMap::new, Collectors.toList()));
        //4.2 横向分组条件需要根据动态属性个数进行扩容 expansion
        topCells = expansionCross(topCells,dynamicCacheDto);

        //5. 纵向条件
        List<CellData> leftGroup = dynamicGroup.getLeftGroup();
        ListUtil.reverse(leftGroup);
        List<CellData> leftCells = getGroup(mainSource, leftGroup, false,dynamicCacheDto);
        //Map<Integer, List<CellData>> leftCondition = leftCells.stream().collect(Collectors.groupingBy(CellData::getR));
        LinkedHashMap<Integer, List<CellData>> leftCondition = leftCells.stream().collect(Collectors.groupingBy(CellData::getR, LinkedHashMap::new, Collectors.toList()));

        //6.动态数据
        dynamicData.forEach(e -> e.setExecList(null).setDynamicGroup(null));

        //6.1创建容器
        List<List<List<CellData>>> table = new ArrayList<>();
        AtomicInteger portraitOffset = new AtomicInteger(0);
        AtomicInteger lateralOffset = new AtomicInteger(0);

        List<StatisticGenerateDto> statisticGenerateCondition = new ArrayList<>();
        //6.2循环所有的动态属性 并填入相应的值
        for (CellData dynamic : dynamicData) {
            dynamic.setDynamicGroup(null);
            String fieldKey = FillCellDataUtils.getFieldKey(dynamic);
            List<List<CellData>> row = new ArrayList<>();
            //初始化行
            lateralOffset.set(0);
            topCondition.values().forEach(e -> {
                //6.3.1 创建左侧过滤条件
                List<ConditionDto> leftPredicateItemList = e.stream()
                        .map(v -> new ConditionDto().setKey(FillCellDataUtils.getFieldKey(v)).setValue(FillCellDataUtils.getCellValue(v)))
                        .collect(Collectors.toList());
                Predicate<Map<String, Object>> leftPredicate = buildPredicate(leftPredicateItemList);
                List<CellData> column = new ArrayList<>();
                //初始化行
                portraitOffset.set(0);
                leftCondition.values().forEach(v -> {

                    if (dynamic.getIsFunction()) {
                        String f = dynamic.getV().get(Constant.CELL_FUNCTION_VALUE_KEY).toString();
                        String replace = ReUtil.getGroup0("\\(.*?\\)", f);

                        List<String> split = StrUtil.split(replace, ",");
                        String collect = split.stream().map(o -> {
                            if (StrUtil.contains(o, ":")) {
                                return StrUtil.split(o, ":").stream().map(o1 -> cellFormulaLocation(o1, lateralOffset, portraitOffset)).collect(Collectors.joining(":"));
                            }
                            return cellFormulaLocation(o, lateralOffset, portraitOffset);
                        }).collect(Collectors.joining(","));

                        String scope = StrUtil.format("({})", collect);
                        f = ReUtil.replaceAll(f,"\\(.*?\\)",scope);

                        CellData cellData = buildCellData(dynamic, null,lateralOffset.get(),portraitOffset.get());
                        cellData.getV().put(Constant.CELL_FUNCTION_VALUE_KEY,f);
                        column.add(cellData);
                    }else{
                        //6.3.2 创建顶部过滤条件
                        List<ConditionDto> topPredicateItemList = v.stream()
                                .map(o -> new ConditionDto().setKey(FillCellDataUtils.getFieldKey(o)).setValue(FillCellDataUtils.getCellValue(o)))
                                .collect(Collectors.toList());
                        Predicate<Map<String, Object>> topPredicate = buildPredicate(topPredicateItemList);
                        List<Map<String, Object>> collect = mainSource.stream().filter(topPredicate).filter(leftPredicate).collect(Collectors.toList());
                        Object o = "";
                        if(CollectionUtil.isNotEmpty(collect)){
                            Map<String, Object> first = CollectionUtil.getFirst(collect);
                            o = first.get(fieldKey);
                        }
                        CellData cellData = buildCellData(dynamic, o,lateralOffset.get(),portraitOffset.get());
                        column.add(cellData);
                    }
                    portraitOffset.getAndIncrement();
                });
                row.add(column);
                //列数+1
                lateralOffset.getAndAdd(dynamicData.size());
            });
            table.add(row);
            List<Integer> collect = row.stream().flatMap(Collection::stream).map(CellData::getC).distinct().collect(Collectors.toList());
            statisticGenerateCondition.add(new StatisticGenerateDto().setInitC(dynamic.getC()).setInitR(dynamic.getR()).setColumnIndex(collect));
        }

        List<CellData> dynamicCellList = table.stream().flatMap(Collection::stream).flatMap(Collection::stream).collect(Collectors.toList());

        //7.特殊单元格 小计设置
        List<CellData> specialCellList = fillSpecialCell(leftCells, dynamicCellList);
        //8.静态标题
        List<CellData> title = dynamicGroup.getTitle();
        List<CellData> statisticTitle = fillStatisticTitle(statisticGenerateCondition, title);


        List<CellData> collect2 = Stream.of(topCells, leftCells,dynamicCellList,specialCellList,statisticTitle).flatMap(Collection::stream).collect(Collectors.toList());

        cell.setExecList(collect2);

        merge(cell,dynamicCacheDto);
    }

    /**
     * 生成分组数据
     * @param source 数据
     * @param settings 设置
     * @param direction 拓展方向
     * @return 单元格数据
     */
    private List<CellData> getGroup(List<Map<String,Object>> source,List<CellData> settings, boolean direction,DynamicCacheDto dynamicCacheDto){
        GroupDto root = new GroupDto().setKey("root");
        groupIterator(source,settings,0,root);

        //缓存分组数据 用于合并

        List<GroupCache> groupCaches = dynamicCacheDto.getGroup();
        if(ObjectUtil.isNull(groupCaches)){
            groupCaches = new ArrayList<>();
        }
        if(direction) groupCaches.add(new GroupCache().setType("cross").setGroupDto(root));
        if(!direction) groupCaches.add(new GroupCache().setType("longitudinal").setGroupDto(root));
        dynamicCacheDto.setGroup(groupCaches);

        List<GroupDto> children = root.getChildren();
        List<CellData> group = new ArrayList<>();
        generateCellData(children,group,direction);
        return group;
    }

    private void groupIterator(List<Map<String,Object>> source,List<CellData> settings,int index,GroupDto root){
        if(index<settings.size()){
            if(CollectionUtil.isEmpty(source)){
                return;
            }
            CellData setting = settings.get(index);

            String fieldKey = FillCellDataUtils.getFieldKey(setting);
            Map<Object, List<Map<String, Object>>> collect = source.stream().collect(Collectors.groupingBy(e -> Optional.ofNullable(e.get(fieldKey)).orElse("")));
            List<GroupDto> group = collect
                    .keySet()
                    .stream()
                    .map(e -> new GroupDto().setSetting(setting).setKey(fieldKey).setValue(e).setSource(collect.get(e)))
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(e -> StrUtil.toString(e.getValue())))), ArrayList::new));
            //排序
            sort(setting,group);
            //判断是否需要设置合计
            if(setting.getSubtotalConfiguration().getBasis()){
                group.add(new GroupDto().setSetting(setting).setValue("合计").setIdentifier(CellIdentifierEnum.合计));
            }

            index++;
            int finalIndex = index;
            group.forEach(e -> groupIterator(e.getSource(),settings, finalIndex,e) );
            root.setChildren(group);
        }
    }

    /**
     *
     * @param dtoList
     * @param direction 拓展方向 true横向 false纵向
     * @param
     * @return
     */
    private void generateCellData(List<GroupDto> dtoList,List<CellData> cellDataList, boolean direction){
        //列偏移
        AtomicInteger lateralOffset = new AtomicInteger(0);
        //行偏移
        AtomicInteger portraitOffset = new AtomicInteger(0);
        for (GroupDto groupDto : dtoList) {
            CellData setting = groupDto.getSetting();
            //这一个条件有多少列
            int size = getRowSize(groupDto);
            for (int i = 0; i < size; i++) {
                CellData cellData = buildCellData(setting, groupDto.getValue(), lateralOffset.get(), portraitOffset.get());
                cellData.setIdentifier(groupDto.getIdentifier());
                cellDataList.add(cellData);
                //如果是顶部分组 则只需要对列数进行偏移;如果是左侧分组 则只需要对行数偏移
                if(direction){
                    lateralOffset.getAndIncrement();
                }else{
                    portraitOffset.getAndIncrement();
                }
            }
        }
        List<GroupDto> collect = dtoList.stream().map(GroupDto::getChildren).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(collect)){
            generateCellData(collect,cellDataList, direction);
        }
    }

    private Integer getRowSize(GroupDto dto){
        int size = 1;
        List<GroupDto> children = dto.getChildren();
        while (CollectionUtil.isNotEmpty(children)){
            if(CollectionUtil.isNotEmpty(children)){
                size = children.size();
            }
            children = children.stream().map(GroupDto::getChildren).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        }
        return size;
    }

    /**
     * 创建单元格数据
     * @param setting 单元格设置
     * @param value 单元格值
     * @param lateralOffset 列 偏移
     * @param portraitOffset 行 偏移
     * @return
     */
    private CellData buildCellData(CellData setting,Object value,Integer lateralOffset,Integer portraitOffset){
        CellData copy = BeanCopyUtil.copy(setting, CellData.class);
        //去除合并设置
        copy.getV().remove(Constant.MC_KEY);
        //设置原始行列
        copy.setOriginC(setting.getC()).setOriginR(setting.getR());
        copy.setR(copy.getR() + portraitOffset);
        copy.setC(copy.getC() + lateralOffset);
        Map<String, Object> v = copy.getV();
        v.put("m", value);
        v.put("v", value);
        copy.setV(v);
        return copy;
    }

    /**
     * 创建单元格数据
     * @param setting 单元格设置
     * @param value 单元格值
     * @param r 行号
     * @param c 列号
     * @return
     */
    private CellData buildCellDataSpecifyLocation(CellData setting,Object value,Integer r,Integer c){
        CellData copy = BeanCopyUtil.copy(setting, CellData.class);
        //去除合并设置
        copy.getV().remove(Constant.MC_KEY);
        copy.setR(r);
        copy.setC(c);
        Map<String, Object> v = copy.getV();
        v.put("m", value);
        v.put("v", value);
        v.put("ct",ct);

        copy.setV(v);
        return copy;
    }

    private Predicate<Map<String,Object>> buildPredicate(List<ConditionDto> list){
        return row -> list.stream().allMatch(e -> StrUtil.equals(StrUtil.toString(row.get(e.getKey())),e.getValue()));
    }

    /**
     * 排序
     * @param setting
     * @param list
     */
    private void sort(CellData setting,List<GroupDto> list){

        DataFieldTypeClassifyEnum fieldType = FillCellDataUtils.getFieldType(setting);
        SortEnums sortType = FillCellDataUtils.getSortType(setting);

        Comparator<GroupDto> comparator;

        switch (fieldType){
            case 数字:
                comparator = Comparator.nullsLast(Comparator.comparingLong(t1 -> NumberUtil.parseNumber(StrUtil.toString(t1.value)).longValue()));
                break;
            case 时间:
                Function<GroupDto, Date> mapDateFunction = obj -> {
                    String format = Optional.ofNullable(setting.getV().get("format")).map(StrUtil::toStringOrNull).orElse("yyyy-MM-dd HH:mm:ss");
                    if(ObjectUtil.isEmpty(obj.getValue())){
                        return SortUtil.convert2Date("1970-01-01 00:00:00",format);
                    }
                    String dateCharSequence = StrUtil.toString(obj.getValue());
                    return SortUtil.convert2Date(dateCharSequence,format);
                };
                comparator = Comparator.nullsLast(Comparator.comparing(mapDateFunction));
                break;
            default:
                comparator = Comparator.comparing(obj -> {
                    Object o = obj.getValue();
                    if (o == null) {
                        return "";
                    }
                    return StrUtil.toString(obj);
                });

        }
        if(SortEnums.desc.equals(sortType)){
            comparator = comparator.reversed();
        }
        list.sort(comparator);
    }

    /**
     * 小计设置
     * @param leftCells 左侧分组 单元格数据
     * @param dynamicCellList 动态属性单元格数据
     * @return
     */
    private List<CellData> fillSpecialCell(List<CellData> leftCells,List<CellData> dynamicCellList){
        if(CollectionUtil.isEmpty(dynamicCellList)){
            return Collections.emptyList();
        }
        //过滤特殊标识字段
        List<CellData> collect = leftCells.stream().filter(e -> CellIdentifierEnum.合计.equals(e.getIdentifier())).collect(Collectors.toList());

        if(CollectionUtil.isEmpty(collect)){
            return Collections.emptyList();
        }

        //得到合计范围
        Map<Integer, List<CellData>> collect1 = collect.stream().collect(Collectors.groupingBy(CellData::getC));
        Integer minR = dynamicCellList.stream().map(CellData::getR).min(Integer::compare).orElseThrow(() -> new BusinessException("未找到动态属性"));
        List<ScopeDto> scopeList = collect1.values().stream().map(e -> {
            List<ScopeDto> scopeDtoList = new ArrayList<>();
            for (int i = 0; i < e.size(); i++) {
                CellData current = e.get(i);
                if (i == 0) {
                    scopeDtoList.add(new ScopeDto().setSetting(current).setStart(minR).setEnd(current.getR()));
                    continue;
                }
                CellData prefix = e.get(i - 1);
                scopeDtoList.add(new ScopeDto().setSetting(current).setStart(prefix.getR() + 1).setEnd(current.getR()));
            }
            return scopeDtoList;
        }).flatMap(Collection::stream).collect(Collectors.toList());

        //需要合计或其他操作的动态属性 按列分组
        Map<Integer, List<CellData>> collect3 = dynamicCellList
                .stream()
                .filter(e -> !StatisticalMethodEnum.无.equals(e.getSubtotalConfiguration().getStatisticalMethodEnum()) || e.getIsFunction())
                .collect(Collectors.groupingBy(CellData::getC));

        return scopeList.stream().map(e ->
                collect3.keySet().stream().map(v -> {
                    List<CellData> cellData = collect3.get(v);
                    //返回内的cell
                    List<CellData> scopeCellList = cellData.stream().filter(o -> o.getR() >= e.getStart() && o.getR() < e.getEnd()).collect(Collectors.toList());
                    return installSubtotal(e.getSetting(), scopeCellList, e.getSetting().getR(), v);
                }).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 注入小计
     * @param setting
     * @param column
     */
    private CellData installSubtotal(CellData setting,List<CellData> column,Integer r,Integer c){
        if(CollectionUtil.isEmpty(column)){
            return null;
        }
        CellData first = CollectionUtil.getFirst(column);
        if(first.getIsFunction()){
            Integer minR = column.stream().map(CellData::getR).min(Integer::compare).orElseThrow(() -> new BusinessException("单元格为空"));
            Integer maxR = column.stream().map(CellData::getR).max(Integer::compare).orElseThrow(() -> new BusinessException("单元格为空"));
            String cStr = CellFunctionUtil.intToAlphabet(c + 1);

            String scope1 = cStr+(minR+1);
            String scope2 = cStr+(maxR+1);

            String scope = StrUtil.format("({}:{})", scope1, scope2);
            return CellFunctionUtil.buildFunctionCellData(first,scope,c,r);
        }
        StatisticalMethodEnum statisticalMethodEnum = Optional.of(first).map(CellData::getSubtotalConfiguration).map(SubtotalConfigurationDto::getStatisticalMethodEnum).orElse(StatisticalMethodEnum.无);
        if(StatisticalMethodEnum.无.equals(statisticalMethodEnum)){
            return null;
        }
        BigDecimal exec = SpringContextUtil.getBean(statisticalMethodEnum.getAClass()).exec(setting, column);
        return buildCellDataSpecifyLocation(setting, exec.doubleValue(),r , c);
    }

    /**
     * 生成静态标题
     * @return
     */
    private List<CellData> fillStatisticTitle(List<StatisticGenerateDto> statisticGenerateCondition,List<CellData> title){
        if(CollectionUtil.isEmpty(title) || CollectionUtil.isEmpty(statisticGenerateCondition)){
            return Collections.emptyList();
        }
        Map<Integer, List<Integer>> collect = statisticGenerateCondition.stream().collect(Collectors.toMap(StatisticGenerateDto::getInitC, StatisticGenerateDto::getColumnIndex));
        Integer startC = collect.keySet().stream().min(Integer::compare).orElseThrow(() -> new BusinessException("静态标题为空"));
        List<List<CellData>> collect1 = title.stream().filter(e -> e.getC()>=startC).map(e -> {
            String cellValue = FillCellDataUtils.getCellValue(e);
            List<Integer> list = collect.get(e.getC());
            return list.stream().map(v -> buildCellDataSpecifyLocation(e, cellValue, e.getR(), v)).collect(Collectors.toList());
        }).collect(Collectors.toList());
        return collect1.stream().flatMap(Collection::stream).collect(Collectors.toList());

    }

    /**
     * 扩容
     * @return
     */
    private List<CellData> expansionCross(List<CellData> list,DynamicCacheDto dynamicCacheDto){
        if(CollectionUtil.isEmpty(list)){
            return list;
        }
        Integer dynamicSize = dynamicCacheDto.getDynamicSize();

        if(dynamicSize.equals(BigDecimal.ROUND_DOWN)){
            return list;
        }
        Integer initC = list.stream().map(CellData::getC).min(Integer::compare).get();

        Map<Integer, List<CellData>> collect = list.stream().collect(Collectors.groupingBy(CellData::getR));

        return collect.values().stream().map(e ->{
            LinkedList<CellData> linkedList = new LinkedList<>();
            for (CellData cellData : e) {
                for (int i = 0; i < dynamicSize ; i++) {
                    CellData copy = BeanCopyUtil.copy(cellData, CellData.class);
                    copy.setC(initC);
                    linkedList.add(copy);
                }
            }
            //重新设置列数
            AtomicInteger c = new AtomicInteger(0);
            return linkedList.stream().peek(v -> v.setC(v.getC() + c.getAndIncrement())).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 生成合并设置
     */
    private void merge(CellData currentCell,DynamicCacheDto dynamicCacheDto){
        List<GroupCache> groupCaches = dynamicCacheDto.getGroup();
        List<DynamicCellMergeDto> collect = groupCaches.stream().map(e -> {
            LinkedHashMap<Integer, List<Integer>> mergeSettingMap = new LinkedHashMap<>();
            GroupDto groupDto = e.getGroupDto();
            List<GroupDto> children = groupDto.getChildren();
            AtomicInteger index = new AtomicInteger(0);
            switch (e.getType()) {
                case "cross":
                    mergeIterate(children, index, mergeSettingMap, true,dynamicCacheDto.getDynamicSize());
                    break;
                default:
                    mergeIterate(children, index, mergeSettingMap, false,dynamicCacheDto.getDynamicSize());
            }
            return new DynamicCellMergeDto().setType(e.getType()).setMergeSetting(mergeSettingMap);
        }).collect(Collectors.toList());
        currentCell.getDynamicGroup().setMergeSetting(collect);

    }

    private void mergeIterate(List<GroupDto> group,AtomicInteger index,Map<Integer,List<Integer>> mergeSettingMap, boolean direction ,int dynamicSize){
        List<Integer> collect = group.stream().map(e -> staticFoliage(e.getChildren())).collect(Collectors.toList());
        mergeSettingMap.put(index.getAndIncrement(),collect);
        List<GroupDto> children = group.stream().map(GroupDto::getChildren).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(children)){
            mergeIterate(children,index,mergeSettingMap,direction,dynamicSize);
        }
    }

    /**
     * 统计 最大分支数量
     * @param children
     * @return
     */
    private Integer staticFoliage(List<GroupDto> children){
        if(children==null){
            return 1;
        }
        return children.stream().map(e -> {
            if (CollectionUtil.isEmpty(e.getChildren())) {
                return 1;
            }
            return staticFoliage(e.getChildren());
        }).mapToInt(Integer::intValue).sum();
    }

    /**
     * 单元格 简写
     * @param cellReference
     * @param lateralOffset
     * @param portraitOffset
     * @return
     */
    private String cellFormulaLocation(String cellReference,AtomicInteger lateralOffset,AtomicInteger portraitOffset){
        TableFormulaDataNode.CellLocation location = CellFunctionUtil.getLocation(cellReference);
        int c = location.getC() + lateralOffset.get();
        int r = location.getR() + portraitOffset.get();
        String prefix = CellFunctionUtil.intToAlphabet(c + 1);
        return prefix + (r + 1);
    }

    @Data
    @Accessors(chain = true)
    public static class GroupDto{

        private CellData setting;

        private String key;

        private Object value;

        private CellIdentifierEnum identifier = CellIdentifierEnum.无;

        private List<GroupDto> children;

        private List<Map<String,Object>> source;
    }

    @Data
    @Accessors(chain = true)
    public static class ScopeDto{

        private CellData setting;

        private Integer start = 0;

        private Integer end = 0;
    }

    @Data
    @Accessors(chain = true)
    public static class StatisticGenerateDto{

        private Integer initR;

        private Integer initC;

        private List<Integer> columnIndex;
    }


    @Data
    @Accessors(chain = true)
    public static class GroupCache{

        private String type;

        private GroupDto groupDto;
    }


}
