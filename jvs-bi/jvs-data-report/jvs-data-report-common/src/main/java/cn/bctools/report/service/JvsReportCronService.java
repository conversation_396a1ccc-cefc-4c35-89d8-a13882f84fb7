package cn.bctools.report.service;

import cn.bctools.report.dto.TaskCronDto;
import cn.bctools.report.entity.JvsReport;

public interface JvsReportCronService {

    /**
     * 新增一个定时任务设置
     * @param dto
     */
    void enable(JvsReport jvsReport,TaskCronDto dto);

    /**
     * 删除定时任务
     * @param jvsReport
     */
    void remove(JvsReport jvsReport);

    void removeCronById(Long taskCronId);
}
