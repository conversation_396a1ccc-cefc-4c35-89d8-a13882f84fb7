package cn.bctools.report.dto;

import cn.bctools.report.enums.StatisticalMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("报表 - 小计设置")
public class SubtotalConfigurationDto {

    @ApiModelProperty("是否为依据")
    private Boolean basis = Boolean.FALSE;

    @ApiModelProperty("统计方式")
    private StatisticalMethodEnum statisticalMethodEnum = StatisticalMethodEnum.无;

}
