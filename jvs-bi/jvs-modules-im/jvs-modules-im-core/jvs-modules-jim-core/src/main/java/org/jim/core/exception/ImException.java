package org.jim.core.exception;

/**
 * @ClassName ImException
 * @Description Im异常类
 * <AUTHOR>
* @Version 1.0
 **/
public class ImException extends Exception{

    /**
     * <AUTHOR>
     * @Description //TODO
     * @param
     * @return
     **/
    public ImException() {
    }

    /**
     * <AUTHOR>
     * @Description //TODO
     * @param message
     * @return
     **/
    public ImException(String message) {
        super(message);

    }

    /**
     * <AUTHOR>
     * @Description //TODO
     * @param message, cause
     * @return
     **/
    public ImException(String message, Throwable cause) {
        super(message, cause);

    }

    /**
     * <AUTHOR>
     * @Description //TODO
     * @param message, cause, enableSuppression, writableStackTrace
     * @return
     **/
    public ImException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);

    }

    /**
     * <AUTHOR>
     * @Description //TODO
     * @param cause
     * @return
     **/
    public ImException(Throwable cause) {
        super(cause);

    }
}
