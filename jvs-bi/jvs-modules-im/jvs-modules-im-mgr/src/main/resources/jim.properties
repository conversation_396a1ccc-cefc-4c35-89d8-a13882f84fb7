##J-IM\u57FA\u7840\u914D\u7F6E
#ip
jim.bind.ip = 0.0.0.0
#\u7AEF\u53E3
#jim.port = 8888
#\u5FC3\u8DF3\u8D85\u65F6\u65F6\u957F(ms)\uFF0C \u5927\u4E8E0\uFF0C\u5F00\u542F\u5FC3\u8DF3\u76D1\u6D4B\u3002 \u5B9E\u73B0\u903B\u8F91\u5728ServerTioConfig\u7684init\u65B9\u6CD5\u4E2D\u7684\u7EBF\u7A0B
jim.heartbeat.timeout = 0
#\u662F\u5426\u5F00\u542F\u6D88\u606F\u6301\u4E45\u5316(on:\u5F00\u542F,off:\u4E0D\u5F00\u542F)
jim.store = on
#\u662F\u5426\u5F00\u542F\u96C6\u7FA4(on:\u5F00\u542F,off:\u4E0D\u5F00\u542F)
jim.cluster = on
#\u662F\u5426\u5F00\u542FSSL(on:\u5F00\u542F,off:\u4E0D\u5F00\u542F)
jim.ssl = off
#JKS\u8BC1\u4E66\u5730\u5740
jim.key.store.path = classpath:ssl/keystore.jks
#JKS\u8BC1\u4E66\u5BC6\u7801
jim.key.store.pwd = 214323428310224

##http\u534F\u8BAE \u914D\u7F6E
#html/css/js\u7B49\u7684\u6839\u76EE\u5F55\uFF0C\u652F\u6301classpath:\u4E5F\u652F\u6301\u7EDD\u5BF9\u8DEF\u5F84
jim.http.page = pages
#http mvc\u626B\u63CF\u5305\u8DEF\u5F84
jim.http.scan.packages = org.jim.server.demo.ImServerDemoStart
#http\u8D44\u6E90\u7F13\u5B58\u65F6\u957F
jim.http.max.live.time = 0

##ws\u534F\u8BAE\u914D\u7F6E



##Redis\u76F8\u5173\u914D\u7F6E
#\u8FDE\u63A5\u6C60\u8FDE\u63A5\u4E0D\u591F\u7528\u65F6,\u91CD\u8BD5\u83B7\u53D6\u8FDE\u63A5\u6B21\u6570
jim.redis.retrynum = 100
#\u53EF\u7528\u8FDE\u63A5\u5B9E\u4F8B\u7684\u6700\u5927\u6570\u76EE\uFF0C\u9ED8\u8BA4\u503C\u4E3A8\uFF1B
jim.redis.maxactive = -1
#\u63A7\u5236\u4E00\u4E2Apool\u6700\u591A\u6709\u591A\u5C11\u4E2A\u72B6\u6001\u4E3Aidle(\u7A7A\u95F2\u7684)\u7684jedis\u5B9E\u4F8B\uFF0C\u9ED8\u8BA4\u503C\u4E5F\u662F8\u3002
jim.redis.maxidle = 20
#\u7B49\u5F85\u53EF\u7528\u8FDE\u63A5\u7684\u6700\u5927\u65F6\u95F4\uFF0C\u5355\u4F4D\u6BEB\u79D2\uFF0C\u9ED8\u8BA4\u503C\u4E3A-1\uFF0C\u8868\u793A\u6C38\u4E0D\u8D85\u65F6\u3002
jim.redis.maxwait = 5000
jim.redis.timeout = 2000
#redis\u6240\u5728\u673A\u5668ip  TODO Redis\u914D\u7F6E\u4F7F\u7528bootstrap\u91CC\u9762\u7684\u914D\u7F6E\uFF0C\u9700\u8981\u4FEE\u6539
#jim.redis.host = **********
jim.redis.host = localhost
#redis\u7AEF\u53E3\u53F7
jim.redis.port = 6379
#redis\u5BC6\u7801
jim.redis.auth =