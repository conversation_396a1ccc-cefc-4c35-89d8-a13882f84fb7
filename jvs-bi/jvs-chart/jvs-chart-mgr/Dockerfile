FROM registry.cn-shenzhen.aliyuncs.com/edf/sky-agent:8.8.0
MAINTAINER edf

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone
ADD ./target/jvs-chart-mgr.jar /app/app.jar
ENV skyname="jvs-chart-mgr"
ENV JAVA_OPTS=""
ENV skyip="localhost:11800"
ENV authentication=""
ENV skywalkingPath=""
ENV nacosAddr="cloud-nacos:8848"
ENV namespace=""
ENTRYPOINT ["sh","-c","java $skywalkingPath  -Dskywalking.agent.service_name=$skyname -Dskywalking.agent.authentication=$authentication -Dskywalking.collector.backend_service=$skyip -Dspring.cloud.nacos.discovery.server-addr=$nacosAddr -Dspring.cloud.nacos.discovery.namespace=$namespace  $JAVA_OPTS -jar /app/app.jar"]