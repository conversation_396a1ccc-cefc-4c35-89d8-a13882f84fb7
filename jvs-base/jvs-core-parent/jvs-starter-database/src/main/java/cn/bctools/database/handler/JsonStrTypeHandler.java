package cn.bctools.database.handler;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * json对象，和json数组都可以，字符串，通用存储，只存储简单值，不支持复杂值
 *
 * <AUTHOR>
 */
@Slf4j
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JsonStrTypeHandler extends AbstractJsonTypeHandler<Object> {

    @Override
    protected Object parse(String json) {
        if (JSONUtil.isTypeJSONArray(json)) {
            return JSONArray.parseArray(json);
        } else if (JSONUtil.isTypeJSON(json)) {
            return JSONObject.parseObject(json);
        }
        return json;
    }

    @Override
    protected String toJson(Object obj) {
        if (obj instanceof String) {
            return obj.toString();
        }
        return JSONObject.toJSONString(obj);
    }
}
