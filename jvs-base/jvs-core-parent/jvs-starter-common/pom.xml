<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-core-parent</artifactId>
        <version>2.2</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-starter-common</artifactId>

    <properties>
        <poi.version>5.2.3</poi.version>
    </properties>

    <dependencies>

        <!-- 中文拼音工具 -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.taobao.arthas</groupId>-->
        <!--            <artifactId>arthas-spring-boot-starter</artifactId>-->
        <!--            <version>3.4.5</version>-->
        <!--        </dependency>-->

        <dependency>
            <!-- 阿里巴巴EasyExcel-->
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
            <exclusions>
                <!-- https://lists.apache.org/thread/66n2tvh2h995op9bcw32k0x2bvwdnhc7  -->
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.21</version>
        </dependency>
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>
        <!-- Apache POI -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>


        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.12.2</version>
        </dependency>

        <!--        &lt;!&ndash;链路日志追踪   日志追踪&ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-trace</artifactId>-->
        <!--            <version>8.8.0</version>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; 自定义功能相关, 比如自定义tag &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-opentracing</artifactId>-->
        <!--            <version>8.8.0</version>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash;链路日志追踪&ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
        <!--            <version>8.8.0</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.27</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>7.0.2.Final</version>
        </dependency>
        <!--json取值-->
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>