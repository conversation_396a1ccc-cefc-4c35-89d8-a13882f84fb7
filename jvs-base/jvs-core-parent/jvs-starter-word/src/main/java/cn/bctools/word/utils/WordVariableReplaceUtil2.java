package cn.bctools.word.utils;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.IdGenerator;
import cn.bctools.common.utils.JvsJsonPath;
import cn.bctools.common.utils.ObjectNull;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.io.Files;
import ink.rayin.htmladapter.base.PdfGenerator;
import ink.rayin.htmladapter.openhtmltopdf.service.PdfBoxGenerator;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.docx4j.TraversalUtil;
import org.docx4j.XmlUtils;
import org.docx4j.convert.in.xhtml.XHTMLImporterImpl;
import org.docx4j.finders.ClassFinder;
import org.docx4j.model.datastorage.migration.VariablePrepare;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import javax.xml.bind.JAXBElement;
import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Project: jvs-backend
 * @Package: cn.bctools.word.utils
 * @Description: WORD变量替换增强版
 * @Author: libo
 * @Email: <EMAIL>
 * @Date: 2024/3/1 1:00
 */
public class WordVariableReplaceUtil2 {
    /**
     * 数据类型
     */
    public enum DataType {
        /**
         * 文本
         */
        TEXT,
        /**
         * 表格
         */
        TABLE,
        /**
         * 网页
         */
        HTML,
        /**
         * 图片
         */
        IMAGE
    }

    /**
     * 表格字段KEY符号
     */
    public static final String TABLE_KEY_SYMBOL = "##";

    /**
     * 多行文本KEY符号
     */
    public static final String MULTILINE_KEY_SYMBOL = "###";

    /**
     * 富文本KEY符号
     */
    public static final String HTML_KEY_SYMBOL = "####";

    /**
     * 字段类型分割符
     */
    public static final String TYPE_SYMBOL = "#";

    /**
     * 定位分割符
     * 表格：@变量行@
     * 多行文本：@第几个表格-变量行@
     */
    public static final String LOCATION_SYMBOL = "@";

    /**
     * start符号
     */
    public static final String PREFIX = "$";

    /**
     * 中包含
     */
    public static final String LEFT_BRACE = "{";

    /**
     * 结尾
     */
    public static final String RIGHT_BRACE = "}";
    /**
     * 多个字段连接符号
     */
    public static final String MULTIL_KEY_CONNECT_SYMBOL = "&";

    public static PdfGenerator pdfGenerator;

    static {
        try {
            pdfGenerator = new PdfBoxGenerator();
//            pdfGenerator.init();
//            pdfGenerator的init方法局限性，无法加载全部的字体位置
            String osName = System.getProperty("os.name");
            if (osName.startsWith("Windows")) {
                pdfGenerator.init("C:\\Windows\\FONTS");
            } else if (osName.startsWith("Mac")) {
                pdfGenerator.init("/Library/Fonts/");
            } else {
                pdfGenerator.init("/usr/share/fonts/zh_CN");
            }
        } catch (Exception e) {
            System.out.printf(e.getMessage());
        }
    }

    /**
     * 字段数据替换
     *
     * @param bytes   模板
     * @param varsMap Map<数据类型, 数据>
     * @return
     */
    @SneakyThrows
    public static WordprocessingMLPackage template(byte[] bytes, Map<String, Object> varsMap) {
        WordprocessingMLPackage template = WordPdfUtil.loadTemplate(bytes);
        // 替换数据
        replaceData(template, varsMap, null);
        return template;
    }

    /**
     * 字段数据替换
     *
     * @param bytes    模板
     * @param varsMap  Map<数据类型, 数据>
     * @param basePath 资源基本路径
     * @return
     */
    @SneakyThrows
    public static WordprocessingMLPackage template(byte[] bytes, Map<String, Object> varsMap, String basePath) throws Exception {
        WordprocessingMLPackage template = WordPdfUtil.loadTemplate(bytes);
        // 替换数据
        replaceData(template, varsMap, basePath);
        return template;
    }

    /**
     * 字段数据替换
     *
     * @param mlPackage
     * @param varsMap   变量数据
     */
    @SneakyThrows
    public static void replaceData(WordprocessingMLPackage mlPackage, Map<String, Object> varsMap, String basePath) {
        if (MapUtils.isEmpty(varsMap)) {
            return;
        }
        MainDocumentPart mainDocumentPart = mlPackage.getMainDocumentPart();
        VariablePrepare.prepare(mlPackage);
        Docx4jClearUtil.cleanDocumentPart(mainDocumentPart);
        //替换多行文本数据
        multiLineTextReplace(mlPackage, varsMap, basePath);
        // 替换列表数据
        tableReplace(mlPackage, varsMap);
        // 替换单属性值
        textReplace(mlPackage, varsMap, basePath);
    }

    /**
     * 获取有变量的text元素集合
     *
     * @param object
     * @return
     */
    private static List<Object> getVariableTextElement(Object object) {
        List<Object> texts = Tool.getAllElementFromObject(object, Text.class);
        if (CollectionUtils.isEmpty(texts)) {
            return Collections.emptyList();
        }
        String prefix = PREFIX + LEFT_BRACE;

        return texts.stream().filter(text -> {
            Text t = (Text) text;
            return t.getValue().contains(prefix);
        }).collect(Collectors.toList());
    }

    /**
     * 获取有变量的多行text元素集合
     *
     * @param object
     * @return
     */
    private static List<Object> getMultiLineVariableTextElement(Object object) {
        List<Object> texts = Tool.getAllElementFromObject(object, Text.class);
        if (CollectionUtils.isEmpty(texts)) {
            return Collections.emptyList();
        }
        String prefix = MULTILINE_KEY_SYMBOL;
        return texts.stream().filter(text -> {
            Text t = (Text) text;
            return t.getValue().contains(prefix);
        }).collect(Collectors.toList());
    }


    /**
     * 获取富文本元素集合
     *
     * @param object
     * @return
     */
    private static List<Object> getHtmlVariableTextElement(Object object) {
        List<Object> texts = Tool.getAllElementFromObject(object, Text.class);
        if (CollectionUtils.isEmpty(texts)) {
            return Collections.emptyList();
        }
        String prefix = HTML_KEY_SYMBOL;
        return texts.stream().filter(text -> {
            Text t = (Text) text;
            return t.getValue().contains(prefix);
        }).collect(Collectors.toList());
    }

    /**
     * 变量替换
     * <p>
     * docx4j提供了替换简单变量的方法： mainDocumentPart.variableReplace(dataMap); 但该方法不支持变量替换图片
     *
     * @param mlPackage
     * @param varsMap
     */
    private static void textReplace(WordprocessingMLPackage mlPackage, Map<String, Object> varsMap, String basePath) {
        if (MapUtils.isEmpty(varsMap)) {
            return;
        }
        List<Object> texts = getVariableTextElement(mlPackage.getMainDocumentPart());
        for (Object text : texts) {
            Text textElement = (Text) text;
            String variableDataType = getVariableDataType(textElement.getValue());
            String variableKey = getPureKey(textElement.getValue());
            String param = null;
            if (variableKey.contains("?")) {
                param = variableKey.substring(variableKey.indexOf("?") + 1);
            }
            Object dataValue = null;
            if (variableKey.contains(MULTIL_KEY_CONNECT_SYMBOL)
                    && !Objects.equals(DataType.IMAGE.name(), variableDataType)) {
                String[] keys = variableKey.split(MULTIL_KEY_CONNECT_SYMBOL);
                dataValue = "";
                for (String key : keys) {
                    Object value = getDataStr(key, varsMap);
                    if (null == value) {
                        continue;
                    }
                    dataValue = dataValue + String.valueOf(value);
                }
            } else if (DataType.IMAGE.name().equals(variableDataType)) {
                //处理图片
                //去除宽高设置，获取属性值
                String pureKey = variableKey.substring(0, Objects.isNull(param)
                        ? variableKey.length()
                        : variableKey.indexOf("?"));
                dataValue = JvsJsonPath.read(varsMap, pureKey);
            } else {
                dataValue = getDataStr(variableKey, varsMap);
                if (null == dataValue) {
                    dataValue = "";
                }
            }
            if (WordVariableReplaceUtil.DataType.IMAGE.name().equals(variableDataType)) {
                WordImageUtil.ImgParam imgParam = WordVariableReplaceUtil.parseImgParam(param);
                List<R> imageRs = WordImageUtil.createImageRs(mlPackage, dataValue, imgParam);
                WordImageUtil.replaceImage(textElement, imageRs);
            } else if (WordVariableReplaceUtil.DataType.HTML.name().equals(variableDataType)) {
                R parent = (R) textElement.getParent();
                P p = (P) parent.getParent();
                Tc tc = (Tc) p.getParent();
                List<Object> contentList = tc.getContent();
                String fixContent = "";
                if (contentList.size() > 1) {
                    //默认第2行为固定的HTML内容
                    P p1 = (P) contentList.get(1);
                    List<Object> rList = p1.getContent();
                    for (Object rObj : rList) {
                        R r1 = (R) rObj;
                        List<Object> jaxbElementList = r1.getContent();
                        for (Object object : jaxbElementList) {
                            JAXBElement jaxbElement = (JAXBElement) object;
                            Text fixTextElement = (Text) jaxbElement.getValue();
                            fixContent = fixContent + fixTextElement.getValue();
                        }
                    }
                    tc.getContent().remove(p1);
                }
                String replaceData = getStrData(dataValue);
                if ("".equals(replaceData)) {
                    textElement.setValue("");
                    continue;
                }
                File tmpDir = null;
                try {
                    // 富文本需要转成html格式
                    replaceData = setHtml(replaceData, fixContent, basePath);
                    tmpDir = Files.createTempDir();
                    String idStr = IdGenerator.getIdStr();
                    //html->pdf
                    File pdfOutput = new File(tmpDir, idStr + ".pdf");
                    String pdfOutputDir = pdfOutput.getAbsolutePath();
                    pdfGenerator.generatePdfFileByHtmlStr(replaceData, pdfOutputDir);
                    //pdf->image
                    String desFileName = idStr + "_pdf";
                    String desFilePath = tmpDir.getAbsolutePath();
                    String imageType = "png";
                    Pair<Boolean, Object> pair = Pdf2ImageUtil.pdfToImage(pdfOutput.getAbsolutePath(), desFilePath, desFileName, imageType);
//                    System.out.println("PDF文档转化为图片结果：" + pair.getLeft());
                    if (!pair.getLeft()) {
                        throw new BusinessException("" + pair.getRight());
                    } else {
                        List<String> fileList = (List<String>) pair.getRight();
//                        System.out.println("转化的文件的内容：");
//                        fileList.forEach(System.out::println);
                        //imgBytes->R
                        List<R> imageRs = new ArrayList<>();
                        for (String imgSrc : fileList) {
                            byte[] imageBytes = Pdf2ImageUtil.image2Bytes(imgSrc);
                            R r = WordImageUtil.newImageR(mlPackage, imageBytes);
                            imageRs.add(r);
                        }
                        //replace
                        WordImageUtil.replaceImage2(textElement, imageRs);
                    }
                } catch (Exception e) {
                    throw new BusinessException(e.getMessage());
                } finally {
                    if (null != tmpDir) {
                        tmpDir.delete();
                    }
                }
            } else {
                // 默认为文本
                String replaceData = ObjectNull.isNull(dataValue) ? "" : String.valueOf(dataValue);
                textElement.setValue(textElement.getValue().replace(buildVariableKey(variableKey), replaceData));
            }
        }
    }

    /**
     * 替换多行文本变量
     *
     * @param mlPackage
     * @param varsMap
     */
    @SneakyThrows
    private static void multiLineTextReplace(WordprocessingMLPackage mlPackage, Map<String, Object> varsMap, String basePath) {
        if (MapUtils.isEmpty(varsMap)) {
            return;
        }
        //如果同一个表格有多个多行表格，需要动态修改变量行
        Map<Integer, Integer> tableLocation = new HashMap<>();

        ClassFinder find = new ClassFinder(Tbl.class);
        new TraversalUtil(mlPackage.getMainDocumentPart().getContent(), find);
        List<Object> multiLineTexts = getMultiLineVariableTextElement(mlPackage.getMainDocumentPart());
        for (Object multiLineText : multiLineTexts) {
            List<Integer> location = getMultiLineDynamicDataLocation((Text) multiLineText);
            String multiLineVariableKey = getMultiLineKey((Text) multiLineText);
            if (StringUtils.isBlank(multiLineVariableKey)) {
                continue;
            }
            //表明是第几个表格
            Tbl table = (Tbl) find.results.get(location.get(0));
            int dynamicRow = location.get(1);
            if (tableLocation.containsKey(location.get(0))) {
                dynamicRow = dynamicRow + tableLocation.get(location.get(0));
            }
            // 得到变量行
            Tr dynamicTr = (Tr) table.getContent().get(dynamicRow);
            // 得到表格数据
            List<Map<String, Object>> tableDataList = getTableDataList(multiLineVariableKey, varsMap);
            if (CollectionUtils.isEmpty(tableDataList)) {
                //如果为空，删掉变量行
                table.getContent().remove(dynamicRow);
                continue;
            }
            List<Object> texts = Tool.getAllElementFromObject(dynamicTr, Text.class);
            if (CollectionUtils.isEmpty(texts)) {
                continue;
            }
            String dynamicTrXml = XmlUtils.marshaltoString(dynamicTr);
            // 解析后的变量 key-模板变量，List[0]-类型，List[1]-变量key
            Map<String, List<String>> variableMap = new HashMap<>(8);
            for (Object rowText : texts) {
                Text content = (Text) rowText;
                String variableDataType = getVariableDataType(content.getValue());
                String variableKey = getPureKey(content.getValue());
                variableMap.put(content.getValue(), Arrays.asList(variableDataType, variableKey));
            }
            // 遍历数据填充表格
            // 获取填充行的位置
            int startLine = dynamicRow;
            for (Map<String, Object> dataMap : tableDataList) {
                Tr newTr = (Tr) XmlUtils.unmarshalString(dynamicTrXml);
                List<Object> newTexts = getVariableTextElement(newTr);
                for (Object text : newTexts) {
                    Text textElement = (Text) text;
                    List<String> vs = variableMap.get(textElement.getValue());
                    if (CollectionUtils.isEmpty(vs)) {
                        continue;
                    }
                    Object data = dataMap.get(variableMap.get(textElement.getValue()).get(1));
                    if (WordVariableReplaceUtil.DataType.IMAGE.name().equals(variableMap.get(textElement.getValue()).get(0))) {
                        List<R> imageRs = WordImageUtil.createImageRs(mlPackage, data);
                        WordImageUtil.replaceImage(textElement, imageRs);
                    } else if (WordVariableReplaceUtil.DataType.HTML.name().equals(variableMap.get(textElement.getValue()).get(0))) {
                        Object dataValue = data;
                        R parent = (R) textElement.getParent();
                        P p = (P) parent.getParent();
                        Tc tc = (Tc) p.getParent();
                        List<Object> contentList = tc.getContent();
                        String fixContent = "";
                        if (contentList.size() > 1) {
                            //默认第2行为固定的HTML内容
                            P p1 = (P) contentList.get(1);
                            List<Object> rList = p1.getContent();
                            for (Object rObj : rList) {
                                R r1 = (R) rObj;
                                List<Object> jaxbElementList = r1.getContent();
                                for (Object object : jaxbElementList) {
                                    JAXBElement jaxbElement = (JAXBElement) object;
                                    Text fixTextElement = (Text) jaxbElement.getValue();
                                    fixContent = fixContent + fixTextElement.getValue();
                                }
                            }
                            tc.getContent().remove(p1);
                        }
                        String replaceData = getStrData(dataValue);
                        if ("".equals(replaceData)) {
                            textElement.setValue("");
                            continue;
                        }
                        File tmpDir = null;
                        try {
                            // 富文本需要转成html格式
                            replaceData = setHtml(replaceData, fixContent, basePath);
                            tmpDir = Files.createTempDir();
                            String idStr = IdGenerator.getIdStr();
                            //html->pdf
                            File pdfOutput = new File(tmpDir, idStr + ".pdf");
                            String pdfOutputDir = pdfOutput.getAbsolutePath();
                            pdfGenerator.generatePdfFileByHtmlStr(replaceData, pdfOutputDir);
                            //pdf->image
                            String desFileName = idStr + "_pdf";
                            String desFilePath = tmpDir.getAbsolutePath();
                            String imageType = "png";
                            Pair<Boolean, Object> pair = Pdf2ImageUtil.pdfToImage(pdfOutput.getAbsolutePath(), desFilePath, desFileName, imageType);
//                    System.out.println("PDF文档转化为图片结果：" + pair.getLeft());
                            if (!pair.getLeft()) {
                                throw new BusinessException("" + pair.getRight());
                            } else {
                                List<String> fileList = (List<String>) pair.getRight();
//                        System.out.println("转化的文件的内容：");
//                        fileList.forEach(System.out::println);
                                //imgBytes->R
                                List<R> imageRs = new ArrayList<>();
                                for (String imgSrc : fileList) {
                                    byte[] imageBytes = Pdf2ImageUtil.image2Bytes(imgSrc);
                                    R r = WordImageUtil.newImageR(mlPackage, imageBytes);
                                    imageRs.add(r);
                                }
                                //replace
                                WordImageUtil.replaceImage2(textElement, imageRs);
                            }
                        } catch (Exception e) {
                            throw new BusinessException(e.getMessage());
                        } finally {
                            if (null != tmpDir) {
                                tmpDir.delete();
                            }
                        }
                    } else {

                        // 默认为文本
                        textElement.setValue(ObjectNull.isNull(data) ? "" : String.valueOf(data));
                    }
                }
                if (startLine == dynamicRow) {
                    //替换变量行
                    table.getContent().set(startLine++, newTr);
                } else {
                    //从指定位置开始添加
                    table.getContent().add(startLine++, newTr);
                }
            }
            if (!tableLocation.containsKey(location.get(0))) {
                tableLocation.put(location.get(0), tableDataList.size());
            } else {
                tableLocation.put(location.get(0), tableLocation.get(location.get(0)) + tableDataList.size());
            }
        }
    }

    /**
     * 替换表格变量
     *
     * @param mlPackage
     * @param varsMap
     */
    @SneakyThrows
    private static void tableReplace(WordprocessingMLPackage mlPackage, Map<String, Object> varsMap) {
        if (MapUtils.isEmpty(varsMap)) {
            return;
        }
        //这里会查找所有的表格，包括嵌套的表格
        ClassFinder find = new ClassFinder(Tbl.class);
        new TraversalUtil(mlPackage.getMainDocumentPart().getContent(), find);
        for (int i = 0; i < find.results.size(); i++) {
            Tbl table = (Tbl) find.results.get(i);
            // 获取变量行的位置
            List<Integer> location = getTableDynamicDataLocation(table);
            int dynamicRow = location.get(0);

            // 得到表格字段Key
            String tableKey = getTableKey(table);
            if (StringUtils.isBlank(tableKey)) {
                continue;
            }
            // 得到表格数据
            List<Map<String, Object>> tableDataList = getTableDataList(tableKey, varsMap);
            if (CollectionUtils.isEmpty(tableDataList)) {
                table.getContent().remove(dynamicRow);
                continue;
            }
            // 得到变量行
            Tr dynamicTr = (Tr) table.getContent().get(dynamicRow);
            List<Object> texts = Tool.getAllElementFromObject(dynamicTr, Text.class);
            if (CollectionUtils.isEmpty(texts)) {
                continue;
            }
            String dynamicTrXml = XmlUtils.marshaltoString(dynamicTr);
            // 解析后的变量 key-模板变量，List[0]-类型，List[1]-变量key
            Map<String, List<String>> variableMap = new HashMap<>(8);
            for (Object text : texts) {
                Text content = (Text) text;
                String variableDataType = getVariableDataType(content.getValue());
                String variableKey = getPureKey(content.getValue());
                variableMap.put(content.getValue(), Arrays.asList(variableDataType, variableKey));
            }
            // 遍历数据填充表格
            // 获取填充行的位置
            int startLine = dynamicRow + 1;
            for (Map<String, Object> dataMap : tableDataList) {
                Tr newTr = (Tr) XmlUtils.unmarshalString(dynamicTrXml);
                List<Object> newTexts = getVariableTextElement(newTr);
                for (Object text : newTexts) {
                    Text textElement = (Text) text;
                    List<String> vs = variableMap.get(textElement.getValue());
                    if (CollectionUtils.isEmpty(vs)) {
                        continue;
                    }
                    String key = variableMap.get(textElement.getValue()).get(1);
                    if (key.contains(MULTIL_KEY_CONNECT_SYMBOL)) {
                        String dataValue = Arrays.stream(key.split(MULTIL_KEY_CONNECT_SYMBOL))
                                .map(dataMap::get)
                                .filter(Objects::nonNull)
                                .map(Object::toString)
                                .collect(Collectors.joining());
                        textElement.setValue(dataValue);
                    } else {
                        Object data = dataMap.get(key);
                        if (WordVariableReplaceUtil.DataType.IMAGE.name().equals(variableMap.get(textElement.getValue()).get(0))) {
                            List<R> imageRs = WordImageUtil.createImageRs(mlPackage, data);
                            WordImageUtil.replaceImage(textElement, imageRs);
                        } else {
                            // 默认为文本
                            textElement.setValue(ObjectNull.isNull(data) || "null".equals(data) ? "" : String.valueOf(data));
                        }
                    }

                }
                //插入指定位置
                table.getContent().add(startLine++, newTr);
            }
            table.getContent().remove(dynamicRow);
        }
    }


    /**
     * 得到表格key
     * 表格key在表格第一行第一列，以 ##表格key## 表示
     *
     * @param table 表格
     * @return 表格名
     */
    @SneakyThrows
    private static String getTableKey(Tbl table) {
        Tr dynamicTr = (Tr) table.getContent().get(0);
        String dynamicTrXml = XmlUtils.marshaltoString(dynamicTr);
//        int startIndex = dynamicTrXml.indexOf(TABLE_KEY_SYMBOL, 0);
//        if (startIndex == -1) {
//            return "";
//        }
//        int endIndex = dynamicTrXml.indexOf(TABLE_KEY_SYMBOL, startIndex + 2);
//        if (endIndex == -1) {
//            return "";
//
//        }
        int firstHashIndex = dynamicTrXml.indexOf("#");
        if (firstHashIndex == -1) {
            return "";
        }

        int secondHashIndex = dynamicTrXml.indexOf("#", firstHashIndex + 1);
        if (secondHashIndex == -1) {
            return "";
        }

        int thirdHashIndex = dynamicTrXml.indexOf("#", secondHashIndex + 1);
        if (thirdHashIndex == -1) {
            return "";
        }

        int fourthHashIndex = dynamicTrXml.indexOf("#", thirdHashIndex + 1);
        if (fourthHashIndex == -1) {
            return "";
        }
        // 得到表格key
        String tableKey = dynamicTrXml.substring(secondHashIndex + 1, thirdHashIndex);
        //由于word文档没被成功解析，导致tableKey 拿错了
        //判断tableKey 是否存在文档标签，有存在，需要尝试重新拿到真正的表格key
        if (tableKey.contains("<w:") || tableKey.contains("</w:")) {
            tableKey = extractTableKey(tableKey);
        }
        // 移除表格key占位字符，这个方法调用要在获取位置后面调用，不然位置信息会被清掉
        StringBuilder str = new StringBuilder().append(dynamicTrXml.substring(0, firstHashIndex)).append(dynamicTrXml.substring(thirdHashIndex + 2));
        Tr newTr = (Tr) XmlUtils.unmarshalString(str.toString());
        table.getContent().set(0, newTr);
        // 移除位置占位字符
        if (tableKey.contains(LOCATION_SYMBOL)) {
            tableKey = tableKey.substring(0, tableKey.indexOf(LOCATION_SYMBOL, 0));
        }
        return tableKey;
    }

    /**
     * 获取动态数据的位置信息数组,第一个元素为变量行
     *
     * @param table
     * @return
     */
    @SneakyThrows
    private static List<Integer> getTableDynamicDataLocation(Tbl table) {
        Tr dynamicTr = (Tr) table.getContent().get(0);
        String dynamicTrXml = XmlUtils.marshaltoString(dynamicTr);
        List<Integer> locationList = getDynamicDataLocation(dynamicTrXml);
        if (CollectionUtils.isEmpty(locationList)) {
            locationList = new ArrayList<>();
            //兼容旧的模板，默认变量行为第1行，表示数据在变量行后面
            locationList.add(1);
            return locationList;
        } else if (locationList.size() != 1) {
            throw new BusinessException("打印模板异常");
        } else {
            return locationList;
        }
    }

    /**
     * 获取表格的数据
     *
     * @param tableKey
     * @param varsMap
     * @return
     */
    private static List<Map<String, Object>> getTableDataList(String tableKey, Map<String, Object> varsMap) {
        try {
            //根据“.”判断层级
            String[] keyArr = tableKey.split("[.]");
            int length = keyArr.length;

            int start = 0;
            while (start < length - 1) {
                varsMap = (Map<String, Object>) varsMap.get(keyArr[start]);
                start++;
            }
            List<Map<String, Object>> list = (List) varsMap.get(keyArr[start]);
            if (null == list || CollectionUtils.isEmpty(list)) {
                return new ArrayList<>();
            }
            //是否需要按序号升序排序
            boolean isReOrderByItemOrder = false;
            //是否需要按时间升序排序
            boolean isReOrderByCreateTime = false;
            //添加序号
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> map = list.get(i);
                if (map.containsKey("item_order")) {
                    isReOrderByItemOrder = true;
                    break;
                }
                if (map.containsKey("createTime")) {
                    //特殊处理“基地”打印的经费问题
                    if (tableKey.equals("jx_cost")) {
                        isReOrderByCreateTime = false;
                    } else {
                        isReOrderByCreateTime = true;
                    }
                }
                break;
            }
            //按序号升序排序
            if (isReOrderByItemOrder) {
                list = list.stream().sorted((t1, t2) -> Integer.valueOf(t1.get("item_order").toString()).compareTo(Integer.valueOf(t2.get("item_order").toString()))).collect(Collectors.toList());
            }
            //按创建时间升序排序
            if (isReOrderByCreateTime) {
                try {
                    list = list.stream().sorted((t1, t2) -> DateUtil.parse(t1.get("createTime").toString(), DatePattern.NORM_DATETIME_FORMATTER).compareTo(DateUtil.parse(t2.get("createTime").toString(), DatePattern.NORM_DATETIME_FORMATTER))).collect(Collectors.toList());
                } catch (Exception e) {
                    //时间格式转换异常暂时不进行处理
                }
            }
            //添加序号
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> map = list.get(i);
                map.put("row", i + 1);
                //处理数字科学计数法转换问题，将数字1.0E-4转换成字符串0.0001
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    entry.setValue(formatData(entry.getKey(), entry.getValue()));
                }
            }
            return list;
        } catch (Exception e) {
            throw new BusinessException("数据解析异常，key：" + tableKey);
        }
    }

    /**
     * 获取表格的数据
     *
     * @param tableKey
     * @param varsMap
     * @return
     */
    private static Object getDataStr(String tableKey, Map<String, Object> varsMap) {
        try {
            //根据“.”判断层级
            String[] keyArr = tableKey.split("[.]");
            int length = keyArr.length;

            int start = 0;
            while (start < length - 1) {
                Object obj = varsMap.get(keyArr[start]);
                if (null == obj) {
                    return "";
                }
                varsMap = (Map<String, Object>) obj;
                start++;
            }
            Object dataObj = varsMap.get(keyArr[start]);
            if (null == dataObj || "".equals(dataObj) || "null".equals(dataObj)) {
                return "";
            } else if (dataObj instanceof JSONArray) {
                String result = "";
                List<String> dataList = JSONArray.parseArray(dataObj.toString(), String.class);
                for (int i = 0; i < dataList.size(); i++) {
                    //处理特殊数据，有的是空数组，有的是null的数组
                    if (null == dataList.get(0) || "null".equals(dataList.get(0))) {
                        return null;
                    }
                    result = result + dataList.get(i);
                    if (i != dataList.size() - 1) {
                        result = result + "、";
                    }

                }
                return result;
            } else if (dataObj instanceof ArrayList) {
                String result = "";
                List<String> dataList = (List<String>) dataObj;
                for (int i = 0; i < dataList.size(); i++) {
                    //处理特殊数据，有的是空数组，有的是null的数组
                    if (null == dataList.get(0) || "null".equals(dataList.get(0))) {
                        return null;
                    }
                    result = result + dataList.get(i);
                    if (i != dataList.size() - 1) {
                        result = result + "、";
                    }
                }
                return result;
            } else {
                return formatData(tableKey, dataObj);
            }
        } catch (Exception e) {
            throw new BusinessException("数据解析异常，key：" + tableKey);
        }
    }

    /**
     * 得到多行文本key
     * 多行文本key在文本第一行第一列，以 ##多行文本key## 表示
     *
     * @param textElement 文本
     * @return key
     */
    @SneakyThrows
    private static String getMultiLineKey(Text textElement) {
        String text = textElement.getValue();
        int startIndex = text.indexOf(MULTILINE_KEY_SYMBOL, 0);
        if (startIndex == -1) {
            return "";
        }
        int endIndex = text.indexOf(MULTILINE_KEY_SYMBOL, startIndex + 3);
        if (endIndex == -1) {
            return "";
        }
        // 得到多行文本key
        String multiLineKey = text.substring(startIndex + 3, endIndex);
        // 移除多行文本key占位字符
        // 移除多行文本key占位字符
        textElement.setValue(textElement.getValue().substring(endIndex + 3));
        // 移除位置占位字符
        if (multiLineKey.contains(LOCATION_SYMBOL)) {
            multiLineKey = multiLineKey.substring(0, multiLineKey.indexOf(LOCATION_SYMBOL, 0));
        }
        return multiLineKey;
    }


    /**
     * 得到富文本key
     * 富文本key在表格第一行第一列，以 ####富文本key#### 表示
     *
     * @param table 表格
     * @return 表格名
     */
    @SneakyThrows
    private static String getHtmlKey(Tbl table) {
        Tr dynamicTr = (Tr) table.getContent().get(0);
        String dynamicTrXml = XmlUtils.marshaltoString(dynamicTr);
        int startIndex = dynamicTrXml.indexOf(HTML_KEY_SYMBOL, 0);
        if (startIndex == -1) {
            return "";
        }
        int endIndex = dynamicTrXml.indexOf(HTML_KEY_SYMBOL, startIndex + 4);
        if (endIndex == -1) {
            return "";
        }
        // 得到富文本格key
        String tableKey = dynamicTrXml.substring(startIndex + 4, endIndex);
        // 移除富文本key占位字符
        StringBuilder str = new StringBuilder().append(dynamicTrXml.substring(0, startIndex)).append(dynamicTrXml.substring(endIndex + 4));
        Tr newTr = (Tr) XmlUtils.unmarshalString(str.toString());
        table.getContent().set(0, newTr);
        // 移除位置占位字符
        if (tableKey.contains(LOCATION_SYMBOL)) {
            tableKey = tableKey.substring(0, tableKey.indexOf(LOCATION_SYMBOL, 0));
        }
        return tableKey;
    }

    /**
     * 获取动态数据的位置信息数组,第一个元素为数据在第几个表格，第二个元素为变量行
     *
     * @param textElement
     * @return
     */
    private static List<Integer> getMultiLineDynamicDataLocation(Text textElement) {
        String text = textElement.getValue();
        List<Integer> locationList = getDynamicDataLocation(text);
        if (CollectionUtils.isEmpty(locationList)) {
            throw new BusinessException("打印模板异常，" + text);
        } else if (locationList.size() != 2) {
            throw new BusinessException("打印模板异常，" + text);
        } else {
            return locationList;
        }
    }

    /**
     * 获取动态数据的添加位置
     * 仅对多行文本和表格有效
     *
     * @param text
     * @return
     */
    private static List<Integer> getDynamicDataLocation(String text) {
        List<Integer> locationList = new ArrayList<>();
        if (StringUtils.isBlank(text)) {
            return locationList;
        }
        int startIndex = text.indexOf(LOCATION_SYMBOL, 0);
        if (startIndex == -1) {
            return locationList;
        }
        int endIndex = text.indexOf(LOCATION_SYMBOL, startIndex + 1);
        if (endIndex == -1) {
            return locationList;
        }
        // 得到多行文本key
        String location = text.substring(startIndex + 1, endIndex);
        String[] locationStrArr = location.split("-");
        if (locationStrArr.length == 0) {
            return locationList;
        }
        for (int i = 0; i < locationStrArr.length; i++) {
            locationList.add(Integer.valueOf(locationStrArr[i]));
        }
        return locationList;
    }

    /**
     * TODO 替换表格数据 (输出的富文本没有样式、若有图片会报错)
     *
     * @param mainDocumentPart
     * @param varsMap
     */
    @SneakyThrows
    private static void htmlReplace(MainDocumentPart mainDocumentPart, Map<String, Object> varsMap) {
        if (ObjectNull.isNull(varsMap)) {
            return;
        }

    }

    /**
     * 变量替换富文本（非表格）
     *
     * @param mainDocumentPart
     * @param htmlDatas
     */
    @SneakyThrows
    private static void replaceHtmlNoTable(MainDocumentPart mainDocumentPart, Map<String, Object> htmlDatas) {
        List<Object> o1 = mainDocumentPart.getContent();
        // 要替换的content集合 Map<下标, 内容>
        Map<Integer, List<Object>> replaceContentMap = new HashMap<>(o1.size());
        for (int i = 0; i < o1.size(); i++) {
            Object obj = o1.get(i);
            if (obj instanceof P) {
                P p = (P) obj;
                ArrayListWml<R> rElement = (ArrayListWml) p.getContent();
                for (R r : rElement) {
                    ArrayListWml<JAXBElement> elements = (ArrayListWml) r.getContent();
                    for (JAXBElement element : elements) {
                        if (element.getValue() instanceof Text) {
                            Text text = (Text) element.getValue();
                            Optional<Map.Entry<String, Object>> htmlDataOptional = htmlDatas.entrySet().stream().filter(d -> text.getValue().contains(d.getKey())).findAny();
                            if (htmlDataOptional.isPresent()) {
                                String htmlData = String.valueOf(htmlDataOptional.get().getValue());
                                WordprocessingMLPackage mlp = WordprocessingMLPackage.createPackage();
                                mlp.setFontMapper(FontUtil.fontMapper);
                                XHTMLImporterImpl importer = new XHTMLImporterImpl(mlp);
                                List<Object> o = importer.convert(htmlData, null);
                                for (int oi = 0; oi < o.size(); oi++) {
                                    P newP = (P) o.get(oi);
                                    RFonts fonts = new RFonts();
                                    fonts.setAscii("宋体");
                                    fonts.setHAnsi("宋体");
                                    fonts.setEastAsia("宋体");
                                    fonts.setCs("宋体");
                                    List<Object> pContents = newP.getContent();
                                    for (int pi = 0; pi < pContents.size(); pi++) {
                                        Object rObj = pContents.get(0);
                                        if (rObj instanceof R) {
                                            R nr = (R) rObj;
                                            nr.getRPr().setRFonts(fonts);
                                        }
                                    }
                                }
                                replaceContentMap.put(i, o);
                            }
                        }
                    }
                }
            }
        }
        buildNewContent(mainDocumentPart, replaceContentMap);
    }

    /**
     * 重新构造content
     *
     * @param mainDocumentPart
     * @param replaceContentMap
     */
    private static void buildNewContent(MainDocumentPart
                                                mainDocumentPart, Map<Integer, List<Object>> replaceContentMap) {
        if (MapUtils.isEmpty(replaceContentMap)) {
            return;
        }
        int index = 0;
        int offset = 0;
        for (Map.Entry<Integer, List<Object>> e : replaceContentMap.entrySet()) {
            index = e.getKey() + offset;
            int valueSize = e.getValue().size();
            List<Object> objects = mainDocumentPart.getContent();
            List<Object> first = objects.subList(0, index);
            List<Object> last = objects.subList(index + 1, objects.size());
            List<Object> newObjects = new ArrayList<>();
            newObjects.addAll(first);
            newObjects.add(e.getValue().get(0));
            if (valueSize > 1) {
                offset = offset + valueSize - 1;
                newObjects.addAll(e.getValue().subList(1, valueSize));
            }
            newObjects.addAll(last);
            mainDocumentPart.getContent().clear();
            mainDocumentPart.getContent().addAll(newObjects);
        }
    }

    /**
     * 构造表格key
     *
     * @param key
     * @return
     */
    public static String buildTableKey(String key) {
        return TABLE_KEY_SYMBOL + key + TABLE_KEY_SYMBOL;
    }


    /**
     * 得到纯粹的表格变量
     *
     * @param key
     * @return 去除各种符号后的变量。 如：##tableName## 返回tableName
     */
    public static String getPureTableKey(String key) {
        return key.replace(TABLE_KEY_SYMBOL, "");
    }

    /**
     * 构造变量key
     *
     * @param key
     * @return
     */
    public static String buildVariableKey(String key) {
        return buildVariableKey(null, key);
    }

    /**
     * 构造变量key
     *
     * @param dataType
     * @param key
     * @return
     */
    public static String buildVariableKey(WordVariableReplaceUtil.DataType dataType, String key) {
        String type = dataType == null ? "" : dataType.name() + TYPE_SYMBOL;
        return PREFIX + LEFT_BRACE + type + key + RIGHT_BRACE;
    }

    /**
     * 得到纯粹的变量
     *
     * @param variable
     * @return 去除各种符号后的变量。 如：${IMAGE#name} 返回name
     */
    public static String getPureKey(String variable) {
        String dataType = getVariableDataType(variable) + TYPE_SYMBOL;
        int startIndex = variable.indexOf(PREFIX + LEFT_BRACE, 0);
        if (startIndex == -1) {
            return "";
        }
        int endIndex = variable.indexOf(RIGHT_BRACE, startIndex + 2);
        if (endIndex == -1) {
            return "";
        }
        String variableKey = variable.substring(startIndex + 2, endIndex);
        return variableKey.replace(dataType, "");
    }

    /**
     * 得到变量的类型字符串
     *
     * @param variable
     * @return
     */
    private static String getVariableDataType(String variable) {
        int startIndex = variable.indexOf(PREFIX + LEFT_BRACE, 0);
        if (startIndex == -1) {
            return "";
        }
        int endIndex = variable.indexOf(TYPE_SYMBOL, startIndex + 2);
        if (endIndex == -1) {
            return "";
        }
        return variable.substring(startIndex + 2, endIndex);
    }


    /**
     * 编辑器输出或者生成的 HTML 都是纯标签，没有内联样式。所以，显示 HTML 时需要自定义样式。
     *
     * @param html
     * @return
     * @throws IOException
     */
    private static String setHtml(String html, String fixContent, String basePath) throws IOException {
        if (!html.contains("<html")) {
            String head = "<!DOCTYPE html>\n" + "<html lang=\"zh-CN\" xmlns=\"http://www.w3.org/1999/xhtml\">\n";
            String style = "<style>\n" + "@page {\n" + "    margin: 10px;\n" + "}" + "  body {\n" + "    font-family: FangSong, HanaMinB;\n" + "    line-height: 1.0;\n" + "    font-size: 22px;\n" + "  }\n" + " img { max-width: 90% } " + "hr {\n" +
                    "border-top: 0.5px solid gray;\n" +
                    "}" + "</style>";
            html = head + style + fixContent + html + "</html>";
        }
        //todo 需要剔除其他字体
        html = html.replaceAll("宋体", "黑体");
        Document document = Jsoup.parse(html);
        //替换图片访问路径为完整路径
        //<img src=\"/jvs-public/jvs-auth-mgr/2_1_8/1/xxx
        html = document.html().replace("<img src=\"/", "<img src=\"" + basePath + "/");
        return html;
    }

    /**
     * 处理数字科学计数法转换问题，将数字1.0E-4转换成字符串0.0001
     *
     * @param value
     * @return
     */
    public static String formatDouble(Object value) {
        DecimalFormat df = new DecimalFormat("####.######");
        return df.format(value);
    }

    /**
     * 特殊处理时间类的字段
     * 2024-03-14 01:55:00 转成 2024年03月14日
     *
     * @param value
     * @return
     */
    public static final String formatTime(String value) {
        return DateUtil.format(DateUtil.parse(value, DatePattern.NORM_DATETIME_FORMAT), DatePattern.CHINESE_DATE_FORMAT);
    }

    /**
     * 处理地区，将“四级区划行政字典/广东省/广州市/荔湾区/沙面街道”转换成“广东省广州市荔湾区沙面街道”
     *
     * @param value
     * @return
     */
    public static final String formatRegion(String value) {
        if (value.startsWith("四级区划行政字典/")) {
            value = value.replace("四级区划行政字典/", "");
            value = value.replace("/", "");
        }
        return value;
    }

    public static final String formatData(String key, Object value) {
        //特殊处理时间类的字段
        //2024-03-14 01:55:00 转成 2024年03月14日
        Pair<Boolean, String> isDateTime = getChineseDateFormatStr(getStrData(value));
        if (isDateTime.getLeft()) {
            return isDateTime.getRight();
        }
        if (value instanceof Double) {
            return formatDouble(value);
        }
        if (value instanceof String) {
            if (value.toString().startsWith("四级区划行政字典/")) {
                return formatRegion(value.toString());
            }
        }
        return getStrData(value);
    }

    public static final String getStrData(Object data) {
        return ObjectNull.isNull(data) || "null".equals(data) ? "" : String.valueOf(data);
    }

    public static final Pair<Boolean, String> getChineseDateFormatStr(String data) {
        if (StringUtils.isBlank(data)) {
            return Pair.of(false, data);
        }
        try {
            DateTime dateTime;
            if (data.length() == 10) {
                //yyyy-MM-dd
                dateTime = DateUtil.parse(data, DatePattern.NORM_DATE_FORMAT);
            } else {
                //yyyy-MM-dd HH:mm:ss
                dateTime = DateUtil.parse(data, DatePattern.NORM_DATETIME_FORMAT);
            }
            //转成yyyy年MM月dd日
            return Pair.of(true, DateUtil.format(dateTime, DatePattern.CHINESE_DATE_FORMAT));
        } catch (Exception e) {
            return Pair.of(false, data);
        }
    }

    private static String extractTableKey(String tableKey) {
        StringBuilder extractTableKeyStr = new StringBuilder();

        Pattern pattern = Pattern.compile("<w:t>(.*?)</w:t>|<w:t>([^<]*)");
        Matcher matcher = pattern.matcher(tableKey.trim());

        while (matcher.find()) {
            for (int i = 1; i <= matcher.groupCount(); i++) {
                String match = matcher.group(i);
                if (match != null) {
                    extractTableKeyStr.append(match);
                }
            }
        }
        if (extractTableKeyStr.length() > 0) {
            return extractTableKeyStr.toString().trim();
        }
        return tableKey;
    }
}