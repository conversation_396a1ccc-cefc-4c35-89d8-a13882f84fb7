<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-base</artifactId>
        <version>2.2</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-gateway</artifactId>
    <version>2.2</version>


    <dependencies>

        <!--        <dependency>-->
        <!--            <groupId>net.logstash.logback</groupId>-->
        <!--            <artifactId>logstash-logback-encoder</artifactId>-->
        <!--            <version>6.2</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-dingding</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-gateway-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--gateway 网关依赖,内置webflux 依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
            <!--            <exclusions>-->
            <!--                <exclusion>-->
            <!--                    <groupId>org.apache.tomcat.embed</groupId>-->
            <!--                    <artifactId>tomcat-embed-core</artifactId>-->
            <!--                </exclusion>-->
            <!--                <exclusion>-->
            <!--                    <groupId>org.apache.tomcat.embed</groupId>-->
            <!--                    <artifactId>tomcat-embed-el</artifactId>-->
            <!--                </exclusion>-->
            <!--            </exclusions>-->
        </dependency>

        <!--knife4j是为Java MVC框架集成Swagger生成Api文档的增强-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-micro-spring-boot-starter</artifactId>
            <version>3.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>3.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oauth2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>cn.bctools</groupId>
                    <artifactId>jvs-starter-knife4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-redis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-gray</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        </dependency>
        <!-- 验证码-->
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
            <version>2.3.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <!-- 配置信息 -->
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
