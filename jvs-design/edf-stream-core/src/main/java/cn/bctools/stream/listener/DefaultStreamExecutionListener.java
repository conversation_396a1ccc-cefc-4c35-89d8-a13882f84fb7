package cn.bctools.stream.listener;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import cn.bctools.stream.manager.StreamOutputManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

/**
 * 默认流式执行监听器
 * 提供基础的日志输出功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnMissingBean(StreamExecutionListener.class)
public class DefaultStreamExecutionListener implements StreamExecutionListener {

    @Autowired(required = false)
    private StreamOutputManager streamOutputManager;

    @Override
    public void onWorkflowStarted(String executionId, int totalNodes) {
        log.info("{} - 执行ID: {}, 总节点数: {}",
                StreamConstants.LogMessages.WORKFLOW_STARTED, executionId, totalNodes);

        // 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendWorkflowStarted(executionId, totalNodes);
        }
    }
    
    @Override
    public void onNodeStarted(StreamNodeExecutionDto nodeExecution) {
        log.info("{} - 节点: {} ({}), 执行ID: {}",
                StreamConstants.LogMessages.NODE_STARTED,
                nodeExecution.getNodeName(),
                nodeExecution.getFunctionName(),
                nodeExecution.getExecutionId());

        // 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendNodeStarted(nodeExecution);
        }
    }

    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        log.info("{} - 节点: {} ({}), 耗时: {}ms, 执行ID: {}",
                StreamConstants.LogMessages.NODE_COMPLETED,
                nodeExecution.getNodeName(),
                nodeExecution.getFunctionName(),
                nodeExecution.getDuration(),
                nodeExecution.getExecutionId());

        // 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendNodeCompleted(nodeExecution);
        }
    }

    @Override
    public void onNodeFailed(StreamNodeExecutionDto nodeExecution) {
        log.error("{} - 节点: {} ({}), 错误: {}, 执行ID: {}",
                StreamConstants.LogMessages.NODE_FAILED,
                nodeExecution.getNodeName(),
                nodeExecution.getFunctionName(),
                nodeExecution.getErrorMessage(),
                nodeExecution.getExecutionId());

        // 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendNodeFailed(nodeExecution);
        }
    }

    @Override
    public void onWorkflowCompleted(String executionId, Object result) {
        log.info("{} - 执行ID: {}, 结果类型: {}",
                StreamConstants.LogMessages.WORKFLOW_COMPLETED,
                executionId,
                result != null ? result.getClass().getSimpleName() : "null");

        // 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendWorkflowCompleted(executionId, result);
        }
    }

    @Override
    public void onWorkflowFailed(String executionId, String errorMessage) {
        log.error("{} - 执行ID: {}, 错误: {}",
                StreamConstants.LogMessages.WORKFLOW_FAILED,
                executionId,
                errorMessage);

        // 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendWorkflowFailed(executionId, errorMessage);
        }
    }

    @Override
    public void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
        log.info("📊 进度更新 - 执行ID: {}, 进度: {}% ({}/{})",
                executionId, progress, executedNodes, totalNodes);

        // 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendProgressUpdate(executionId, progress, executedNodes, totalNodes);
        }
    }

}