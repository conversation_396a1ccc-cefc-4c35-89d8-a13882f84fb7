package cn.bctools.stream.config;

import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 工作流流式输出执行参数
 */
@ConfigurationProperties("edf.stream")
@Component
@Getter
public class StreamExecutionProperties {

    //上下文过期时间
    private Integer contextExpireMinutes;

    //过期任务清理周期
    private Integer cleanupIntervalMinutes;
}