package cn.bctools.stream.strategy;

import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import cn.bctools.stream.enums.StreamEventType;
import cn.bctools.stream.enums.StreamOutputType;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * WebSocket流式输出策略
 * 通过WebSocket实时推送执行进度给前端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WebSocketStreamOutputStrategy implements StreamOutputStrategy {

    /**
     * 存储WebSocket会话，按执行ID分组
     */
    private final ConcurrentHashMap<String, CopyOnWriteArraySet<WebSocketSession>> sessionMap = new ConcurrentHashMap<>();

    @Override
    public StreamOutputType getOutputType() {
        return StreamOutputType.WEBSOCKET;
    }

    @Override
    public void initialize(StreamExecutionContext context) {
        log.info("🚀 初始化WebSocket流式输出 - 执行ID: {}", context.getExecutionId());
        // WebSocket会话通过addSession方法添加
    }

    /**
     * 添加WebSocket会话
     *
     * @param executionId 执行ID
     * @param session     WebSocket会话
     */
    public void addSession(String executionId, WebSocketSession session) {
        sessionMap.computeIfAbsent(executionId, k -> new CopyOnWriteArraySet<>()).add(session);
        log.info("WebSocket会话已添加 - 执行ID: {}, 会话ID: {}", executionId, session.getId());
    }

    /**
     * 移除WebSocket会话
     *
     * @param executionId 执行ID
     * @param session     WebSocket会话
     */
    public void removeSession(String executionId, WebSocketSession session) {
        CopyOnWriteArraySet<WebSocketSession> sessions = sessionMap.get(executionId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                sessionMap.remove(executionId);
            }
        }
        log.info("WebSocket会话已移除 - 执行ID: {}, 会话ID: {}", executionId, session.getId());
    }

    @Override
    public void onWorkflowStarted(StreamExecutionContext context, int totalNodes) {
        JSONObject event = createEvent(StreamEventType.WORKFLOW_STARTED,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("totalNodes", totalNodes)
                        .fluentPut("status", "started")
                        .fluentPut("timestamp", System.currentTimeMillis()));

        sendToWebSocket(context.getExecutionId(), event);
        log.info("🚀 [WebSocket] 工作流开始执行 - 执行ID: {}, 总节点数: {}", context.getExecutionId(), totalNodes);
    }

    @Override
    public void onNodeStarted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        JSONObject event = createEvent(StreamEventType.NODE_STARTED, nodeExecution);
        sendToWebSocket(context.getExecutionId(), event);
        log.info("▶️ [WebSocket] 节点开始执行 - 节点: {} ({})", nodeExecution.getNodeName(), nodeExecution.getNodeId());
    }

    @Override
    public void onNodeRunning(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        JSONObject event = createEvent(StreamEventType.NODE_RUNNING, nodeExecution);
        sendToWebSocket(context.getExecutionId(), event);
        log.debug("⏳ [WebSocket] 节点执行中 - 节点: {} ({})", nodeExecution.getNodeName(), nodeExecution.getNodeId());
    }

    @Override
    public void onNodeCompleted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        JSONObject event = createEvent(StreamEventType.NODE_COMPLETED, nodeExecution);
        sendToWebSocket(context.getExecutionId(), event);
        log.info("✅ [WebSocket] 节点执行完成 - 节点: {} ({}), 耗时: {}ms",
                nodeExecution.getNodeName(), nodeExecution.getNodeId(), nodeExecution.getDuration());
    }

    @Override
    public void onNodeFailed(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        JSONObject event = createEvent(StreamEventType.NODE_FAILED, nodeExecution);
        sendToWebSocket(context.getExecutionId(), event);
        log.error("❌ [WebSocket] 节点执行失败 - 节点: {} ({}), 错误: {}",
                nodeExecution.getNodeName(), nodeExecution.getNodeId(), nodeExecution.getErrorMessage());
    }

    @Override
    public void onWorkflowCompleted(StreamExecutionContext context, Object finalResult) {
        JSONObject event = createEvent(StreamEventType.WORKFLOW_COMPLETED,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("finalResult", finalResult)
                        .fluentPut("stats", context.getStats())
                        .fluentPut("timestamp", System.currentTimeMillis()));

        sendToWebSocket(context.getExecutionId(), event);
        // 工作流完成后清理会话
        sessionMap.remove(context.getExecutionId());
        log.info("🎉 [WebSocket] 工作流执行完成 - 执行ID: {}", context.getExecutionId());
    }

    @Override
    public void onWorkflowFailed(StreamExecutionContext context, String errorMessage) {
        JSONObject event = createEvent(StreamEventType.WORKFLOW_FAILED,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("errorMessage", errorMessage)
                        .fluentPut("stats", context.getStats())
                        .fluentPut("timestamp", System.currentTimeMillis()));

        sendToWebSocket(context.getExecutionId(), event);
        // 工作流失败后清理会话
        sessionMap.remove(context.getExecutionId());
        log.error("💥 [WebSocket] 工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), errorMessage);
    }

    @Override
    public void onProgressUpdate(StreamExecutionContext context, int progress, int executedNodes, int totalNodes) {
        JSONObject event = createEvent(StreamEventType.PROGRESS_UPDATE,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("progress", progress)
                        .fluentPut("executedNodes", executedNodes)
                        .fluentPut("totalNodes", totalNodes)
                        .fluentPut("timestamp", System.currentTimeMillis()));

        sendToWebSocket(context.getExecutionId(), event);
        log.info("📊 [WebSocket] 进度更新 - 执行ID: {}, 进度: {}% ({}/{})",
                context.getExecutionId(), progress, executedNodes, totalNodes);
    }

    @Override
    public void onEvent(StreamExecutionContext context, StreamEventType eventType, Object data) {
        JSONObject event = createEvent(eventType, data);
        sendToWebSocket(context.getExecutionId(), event);
        log.debug("📡 [WebSocket] 通用事件 - 类型: {}, 数据: {}", eventType.getCode(), data);
    }

    @Override
    public void cleanup(StreamExecutionContext context) {
        sessionMap.remove(context.getExecutionId());
        log.info("🧹 [WebSocket] 清理完成 - 执行ID: {}", context.getExecutionId());
    }

    @Override
    public boolean supports(StreamExecutionContext context) {
        return context.getOutputType() == StreamOutputType.WEBSOCKET;
    }

    /**
     * 创建标准事件格式
     */
    private JSONObject createEvent(StreamEventType eventType, Object data) {
        return new JSONObject()
                .fluentPut("event", eventType.getCode())
                .fluentPut("data", data)
                .fluentPut("timestamp", System.currentTimeMillis());
    }

    /**
     * 发送消息到WebSocket
     *
     * @param executionId 执行ID
     * @param event       事件数据
     */
    private void sendToWebSocket(String executionId, Object event) {
        CopyOnWriteArraySet<WebSocketSession> sessions = sessionMap.get(executionId);
        if (sessions == null || sessions.isEmpty()) {
            log.warn("没有找到执行ID对应的WebSocket会话: {}", executionId);
            return;
        }

        String messageText = JSONObject.toJSONString(event);

        // 发送给所有相关会话
        sessions.removeIf(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(messageText));
                    // 保留会话
                    return false;
                } else {
                    log.warn("WebSocket会话已关闭，移除会话: {}", session.getId());
                    // 移除会话
                    return true;
                }
            } catch (IOException e) {
                log.error("发送WebSocket消息失败 - 会话ID: {}, 错误: {}", session.getId(), e.getMessage());
                // 移除有问题的会话
                return true;
            }
        });
    }
}