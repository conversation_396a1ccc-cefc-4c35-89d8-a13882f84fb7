package cn.bctools.stream.dto;

import cn.bctools.stream.constants.StreamConstants;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流式节点执行结果DTO
 * 仿照Dify工作流，每个组件执行都流式返回
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class StreamNodeExecutionDto {
    
    /**
     * 执行ID
     */
    private String executionId;
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型
     */
    private String nodeType;
    
    /**
     * 功能名称
     */
    private String functionName;
    
    /**
     * 执行状态：waiting, running, completed, failed, skipped
     */
    private String status = StreamConstants.NodeStatus.WAITING;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 节点输入参数
     */
    private Map<String, Object> inputs;
    
    /**
     * 节点输出结果
     */
    private Object outputs;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 执行进度百分比
     */
    private Integer progress;
    
    /**
     * 总节点数
     */
    private Integer totalNodes;
    
    /**
     * 已执行节点数
     */
    private Integer executedNodes;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> metadata;
    
    /**
     * 计算执行耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }
    
    /**
     * 设置为运行状态
     */
    public StreamNodeExecutionDto setRunning() {
        this.status = StreamConstants.NodeStatus.RUNNING;
        if (this.startTime == null) {
            this.startTime = LocalDateTime.now();
        }
        return this;
    }
    
    /**
     * 设置为完成状态
     */
    public StreamNodeExecutionDto setCompleted() {
        this.status = StreamConstants.NodeStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        calculateDuration();
        return this;
    }
    
    /**
     * 设置为失败状态
     */
    public StreamNodeExecutionDto setFailed(String errorMessage) {
        this.status = StreamConstants.NodeStatus.FAILED;
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
        calculateDuration();
        return this;
    }
    
    /**
     * 设置为跳过状态
     */
    public StreamNodeExecutionDto setSkipped() {
        this.status = StreamConstants.NodeStatus.SKIPPED;
        this.endTime = LocalDateTime.now();
        return this;
    }
    
    /**
     * 检查是否已完成（成功或失败）
     */
    public boolean isFinished() {
        return StreamConstants.NodeStatus.COMPLETED.equals(status) || 
               StreamConstants.NodeStatus.FAILED.equals(status) ||
               StreamConstants.NodeStatus.SKIPPED.equals(status);
    }
    
    /**
     * 检查是否成功完成
     */
    public boolean isSuccessful() {
        return StreamConstants.NodeStatus.COMPLETED.equals(status);
    }
    
    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return StreamConstants.NodeStatus.FAILED.equals(status);
    }
    
    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return StreamConstants.NodeStatus.RUNNING.equals(status);
    }
}
