package cn.bctools.stream.manager;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import cn.bctools.stream.enums.StreamOutputType;
import cn.bctools.common.utils.SpringContextUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 流式输出管理器
 * 真正实现SSE和Flux的数据输出
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StreamOutputManager {

    /**
     * SSE发射器缓存
     */
    private final Map<String, SseEmitter> sseEmitters = new ConcurrentHashMap<>();

    /**
     * Flux发射器缓存
     */
    private final Map<String, FluxSink<Object>> fluxSinks = new ConcurrentHashMap<>();

    /**
     * 创建SSE连接
     *
     * @param executionId 执行ID
     * @param timeoutMs   超时时间
     * @return SSE发射器
     */
    public SseEmitter createSseConnection(String executionId, long timeoutMs) {
        SseEmitter emitter = new SseEmitter(timeoutMs);

        // 设置回调
        emitter.onCompletion(() -> {
            sseEmitters.remove(executionId);
            log.info("🚫 SSE连接完成 - 执行ID: {}", executionId);
        });

        emitter.onTimeout(() -> {
            sseEmitters.remove(executionId);
            log.warn("⏰ SSE连接超时 - 执行ID: {}", executionId);
        });

        emitter.onError(throwable -> {
            sseEmitters.remove(executionId);
            log.error("❌ SSE连接错误 - 执行ID: {}, 错误: {}", executionId, throwable.getMessage());
        });

        sseEmitters.put(executionId, emitter);

        // 发送连接成功消息
        try {
            emitter.send(SseEmitter.event()
                    .name(StreamConstants.EventTypeCodes.CONNECTED)
                    .data("SSE连接已建立 - 执行ID: " + executionId));
        } catch (IOException e) {
            log.error("发送SSE连接消息失败 - 执行ID: {}", executionId, e);
        }

        log.info("🔗 SSE连接已建立 - 执行ID: {}", executionId);
        return emitter;
    }

    /**
     * 创建Flux连接
     *
     * @param executionId 执行ID
     * @return Flux流
     */
    public Flux<Object> createFluxConnection(String executionId) {
        return Flux.create(sink -> {
            fluxSinks.put(executionId, sink);

            // 设置清理回调
            sink.onDispose(() -> {
                fluxSinks.remove(executionId);
                log.info("🚫 Flux连接已关闭 - 执行ID: {}", executionId);
            });

            // 发送连接成功消息
            sink.next(createEventData(StreamConstants.EventTypeCodes.CONNECTED,
                    "Flux连接已建立 - 执行ID: " + executionId));

            log.info("🔗 Flux连接已建立 - 执行ID: {}", executionId);
        });
    }

    /**
     * 发送工作流开始事件
     *
     * @param executionId 执行ID
     * @param totalNodes  总节点数
     */
    public void sendWorkflowStarted(String executionId, int totalNodes) {
        Map<String, Object> data = JSONObject.of(
                "executionId", executionId,
                "totalNodes", totalNodes,
                "timestamp", System.currentTimeMillis()
        );

        sendEvent(executionId, StreamConstants.EventTypeCodes.WORKFLOW_STARTED, data);
    }

    /**
     * 发送节点开始事件
     *
     * @param nodeExecution 节点执行信息
     */
    public void sendNodeStarted(StreamNodeExecutionDto nodeExecution) {
        sendEvent(nodeExecution.getExecutionId(), StreamConstants.EventTypeCodes.NODE_STARTED, nodeExecution);
    }

    /**
     * 发送节点完成事件
     *
     * @param nodeExecution 节点执行信息
     */
    public void sendNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        sendEvent(nodeExecution.getExecutionId(), StreamConstants.EventTypeCodes.NODE_COMPLETED, nodeExecution);
    }

    /**
     * 发送节点失败事件
     *
     * @param nodeExecution 节点执行信息
     */
    public void sendNodeFailed(StreamNodeExecutionDto nodeExecution) {
        sendEvent(nodeExecution.getExecutionId(), StreamConstants.EventTypeCodes.NODE_FAILED, nodeExecution);
    }

    /**
     * 发送工作流完成事件
     *
     * @param executionId 执行ID
     * @param result      执行结果
     */
    public void sendWorkflowCompleted(String executionId, Object result) {
        Map<String, Object> data = JSONObject.of(
                "executionId", executionId,
                "result", result != null ? result : "null",
                "timestamp", System.currentTimeMillis()
        );

        sendEvent(executionId, StreamConstants.EventTypeCodes.WORKFLOW_COMPLETED, data);

        // 完成后关闭连接
        closeConnection(executionId);
    }

    /**
     * 发送工作流失败事件
     *
     * @param executionId  执行ID
     * @param errorMessage 错误信息
     */
    public void sendWorkflowFailed(String executionId, String errorMessage) {
        Map<String, Object> data = JSONObject.of(
                "executionId", executionId,
                "errorMessage", errorMessage,
                "timestamp", System.currentTimeMillis()
        );

        sendEvent(executionId, StreamConstants.EventTypeCodes.WORKFLOW_FAILED, data);

        // 失败后关闭连接
        closeConnection(executionId);
    }

    /**
     * 发送进度更新事件
     *
     * @param executionId   执行ID
     * @param progress      进度百分比
     * @param executedNodes 已执行节点数
     * @param totalNodes    总节点数
     */
    public void sendProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
        Map<String, Object> data = JSONObject.of(
                "executionId", executionId,
                "progress", progress,
                "executedNodes", executedNodes,
                "totalNodes", totalNodes,
                "timestamp", System.currentTimeMillis()
        );

        sendEvent(executionId, StreamConstants.EventTypeCodes.PROGRESS_UPDATE, data);
    }

    /**
     * 发送事件到所有连接
     *
     * @param executionId 执行ID
     * @param eventType   事件类型
     * @param data        数据
     */
    private void sendEvent(String executionId, String eventType, Object data) {
        Object eventData = createEventData(eventType, data);

        // 发送到SSE
        SseEmitter sseEmitter = sseEmitters.get(executionId);
        if (sseEmitter != null) {
            try {
                sseEmitter.send(SseEmitter.event().name(eventType).data(eventData));
            } catch (IOException e) {
                log.error("发送SSE事件失败 - 执行ID: {}, 事件: {}, 错误: {}", executionId, eventType, e.getMessage());
                sseEmitters.remove(executionId);
            }
        }

        // 发送到Flux
        FluxSink<Object> fluxSink = fluxSinks.get(executionId);
        if (fluxSink != null && !fluxSink.isCancelled()) {
            try {
                fluxSink.next(eventData);
            } catch (Exception e) {
                log.error("发送Flux事件失败 - 执行ID: {}, 事件: {}, 错误: {}", executionId, eventType, e.getMessage());
                fluxSinks.remove(executionId);
            }
        }

        // 发送到WebSocket
        sendToWebSocket(executionId, eventType, data);
    }

    /**
     * 发送到WebSocket
     */
    private void sendToWebSocket(String executionId, String eventType, Object data) {
        try {
            // 通过Spring上下文获取WebSocket处理器
            Object webSocketHandler = SpringContextUtil.getApplicationContext().getBean("workflowWebSocketHandler");
            // 未引入，使用反射调用sendToExecution方法
            webSocketHandler.getClass()
                    .getMethod("sendToExecution", String.class, String.class, Object.class)
                    .invoke(webSocketHandler, executionId, eventType, data);
        } catch (Exception e) {
            log.error("发送WebSocket事件失败 - 执行ID: {}, 事件: {}, 错误: {}", executionId, eventType, e.getMessage());
        }
    }

    /**
     * 创建事件数据
     *
     * @param eventType 事件类型
     * @param data      数据
     * @return 事件数据
     */
    private Object createEventData(String eventType, Object data) {
        return JSONObject.of(
                "event", eventType,
                "data", data,
                "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * 关闭连接
     *
     * @param executionId 执行ID
     */
    private void closeConnection(String executionId) {
        // 关闭SSE连接
        SseEmitter sseEmitter = sseEmitters.remove(executionId);
        if (sseEmitter != null) {
            sseEmitter.complete();
        }

        // 关闭Flux连接
        FluxSink<Object> fluxSink = fluxSinks.remove(executionId);
        if (fluxSink != null && !fluxSink.isCancelled()) {
            fluxSink.complete();
        }
    }

    /**
     * 检查是否有活跃连接
     *
     * @param executionId 执行ID
     * @return 是否有活跃连接
     */
    public boolean hasActiveConnection(String executionId) {
        return sseEmitters.containsKey(executionId) || fluxSinks.containsKey(executionId);
    }

    /**
     * 获取活跃连接数
     *
     * @return 活跃连接数
     */
    public int getActiveConnectionCount() {
        return sseEmitters.size() + fluxSinks.size();
    }
}