package cn.bctools.stream.factory;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import cn.bctools.stream.enums.StreamEventType;
import cn.bctools.stream.enums.StreamOutputType;
import cn.bctools.stream.strategy.ConsoleStreamOutputStrategy;
import cn.bctools.stream.strategy.StreamOutputStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 流式输出策略工厂
 * 使用工厂模式管理不同的流式输出策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StreamOutputStrategyFactory {

    /**
     * 策略映射表
     */
    private final Map<StreamOutputType, StreamOutputStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 默认策略
     */
    private StreamOutputStrategy defaultStrategy;

    /**
     * 自动注入所有策略实现
     */
    @Autowired
    private List<StreamOutputStrategy> strategies;

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        log.info("🏭 初始化流式输出策略工厂...");

        // 注册所有策略
        for (StreamOutputStrategy strategy : strategies) {
            StreamOutputType outputType = strategy.getOutputType();
            strategyMap.put(outputType, strategy);
            log.info("📝 注册流式输出策略: {} -> {}", outputType.getCode(), strategy.getClass().getSimpleName());
        }

        // 设置默认策略为控制台输出
        defaultStrategy = strategyMap.computeIfAbsent(StreamOutputType.FLUX,
                k -> new ConsoleStreamOutputStrategy());

        log.info("✅ 流式输出策略工厂初始化完成，共注册 {} 个策略", strategyMap.size());
        log.info("🎯 默认策略: {}", defaultStrategy.getClass().getSimpleName());
    }

    /**
     * 根据输出类型获取策略
     *
     * @param outputType 输出类型
     * @return 对应的策略实现
     */
    public StreamOutputStrategy getStrategy(StreamOutputType outputType) {
        StreamOutputStrategy strategy = strategyMap.get(outputType);
        if (strategy == null) {
            log.warn("⚠️ 未找到输出类型 {} 对应的策略，使用默认策略", outputType.getCode());
            return defaultStrategy;
        }
        return strategy;
    }

    /**
     * 根据执行上下文获取最佳策略
     *
     * @param context 执行上下文
     * @return 最佳策略实现
     */
    public StreamOutputStrategy getBestStrategy(StreamExecutionContext context) {
        StreamOutputType outputType = context.getOutputType();

        // 如果没有指定输出类型，根据上下文自动选择
        if (outputType == null) {
            outputType = determineOutputType(context);
            context.setOutputType(outputType);
        }

        StreamOutputStrategy strategy = getStrategy(outputType);

        // 检查策略是否支持当前上下文
        if (!strategy.supports(context)) {
            log.warn("⚠️ 策略 {} 不支持当前上下文，使用默认策略", strategy.getClass().getSimpleName());
            return defaultStrategy;
        }

        return strategy;
    }

    /**
     * 获取多个策略（支持组合输出）
     *
     * @param outputTypes 输出类型列表
     * @return 策略列表
     */
    public List<StreamOutputStrategy> getStrategies(List<StreamOutputType> outputTypes) {
        return outputTypes.stream()
                .map(this::getStrategy)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有可用的策略
     *
     * @return 所有策略的映射
     */
    public Map<StreamOutputType, StreamOutputStrategy> getAllStrategies() {
        return new HashMap<>(strategyMap);
    }

    /**
     * 获取所有支持的输出类型
     *
     * @return 支持的输出类型列表
     */
    public List<StreamOutputType> getSupportedOutputTypes() {
        return new ArrayList<>(strategyMap.keySet());
    }

    /**
     * 注册自定义策略
     *
     * @param strategy 自定义策略实现
     */
    public void registerStrategy(StreamOutputStrategy strategy) {
        StreamOutputType outputType = strategy.getOutputType();
        strategyMap.put(outputType, strategy);
        log.info("📝 注册自定义流式输出策略: {} -> {}", outputType.getCode(), strategy.getClass().getSimpleName());
    }

    /**
     * 移除策略
     *
     * @param outputType 输出类型
     */
    public void removeStrategy(StreamOutputType outputType) {
        StreamOutputStrategy removed = strategyMap.remove(outputType);
        if (removed != null) {
            log.info("🗑️ 移除流式输出策略: {} -> {}", outputType.getCode(), removed.getClass().getSimpleName());
        }
    }

    /**
     * 检查是否支持指定的输出类型
     *
     * @param outputType 输出类型
     * @return 是否支持
     */
    public boolean isSupported(StreamOutputType outputType) {
        return strategyMap.containsKey(outputType);
    }

    /**
     * 根据上下文自动确定输出类型
     *
     * @param context 执行上下文
     * @return 推荐的输出类型
     */
    private StreamOutputType determineOutputType(StreamExecutionContext context) {
        // 智能选择策略的逻辑

        // 1. 如果有FluxSink，优先使用Flux
        if (context.getFluxSink() != null) {
            return StreamOutputType.FLUX;
        }

        // 2. 如果有SseEmitter，使用SSE
        if (context.getSseEmitter() != null) {
            return StreamOutputType.SSE;
        }

        // 3. 如果有WebSocketSession，使用WebSocket
        if (context.getWebSocketSession() != null) {
            return StreamOutputType.WEBSOCKET;
        }

        // 4. 检查HTTP请求头，判断客户端期望的输出类型
        if (context.getRequest() != null) {
            String accept = context.getRequest().getHeader("Accept");
            if (accept != null) {
                if (accept.contains("text/event-stream")) {
                    return StreamOutputType.SSE;
                }
                if (accept.contains("application/stream+json")) {
                    return StreamOutputType.FLUX;
                }
            }

            // 检查自定义头
            String streamType = context.getRequest().getHeader("X-Stream-Type");
            if (streamType != null) {
                try {
                    return StreamOutputType.fromCode(streamType);
                } catch (IllegalArgumentException e) {
                    log.warn("⚠️ 无效的流式输出类型: {}", streamType);
                }
            }
        }

        // 5. 检查上下文属性
        String preferredType = context.getAttribute(StreamConstants.AttributeNames.PREFERRED_OUTPUT_TYPE);
        if (preferredType != null) {
            try {
                return StreamOutputType.fromCode(preferredType);
            } catch (IllegalArgumentException e) {
                log.warn("⚠️ 无效的首选输出类型: {}", preferredType);
            }
        }

        // 6. 默认使用控制台输出
        return StreamOutputType.CONSOLE;
    }

    /**
     * 创建组合策略（支持同时输出到多个目标）
     *
     * @param outputTypes 输出类型列表
     * @return 组合策略
     */
    public StreamOutputStrategy createCompositeStrategy(List<StreamOutputType> outputTypes) {
        List<StreamOutputStrategy> strategies = getStrategies(outputTypes);
        return new CompositeStreamOutputStrategy(strategies);
    }

    /**
     * 组合策略实现
     * 允许同时使用多个输出策略
     */
    private static class CompositeStreamOutputStrategy implements StreamOutputStrategy {

        private final List<StreamOutputStrategy> strategies;

        public CompositeStreamOutputStrategy(List<StreamOutputStrategy> strategies) {
            this.strategies = new ArrayList<>(strategies);
        }

        @Override
        public StreamOutputType getOutputType() {
            return StreamOutputType.COMPOSITE;
        }

        @Override
        public void initialize(StreamExecutionContext context) {
            strategies.forEach(strategy -> strategy.initialize(context));
        }

        @Override
        public void onWorkflowStarted(StreamExecutionContext context, int totalNodes) {
            strategies.forEach(strategy -> strategy.onWorkflowStarted(context, totalNodes));
        }

        @Override
        public void onNodeStarted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
            strategies.forEach(strategy -> strategy.onNodeStarted(context, nodeExecution));
        }

        @Override
        public void onNodeRunning(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
            strategies.forEach(strategy -> strategy.onNodeRunning(context, nodeExecution));
        }

        @Override
        public void onNodeCompleted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
            strategies.forEach(strategy -> strategy.onNodeCompleted(context, nodeExecution));
        }

        @Override
        public void onNodeFailed(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
            strategies.forEach(strategy -> strategy.onNodeFailed(context, nodeExecution));
        }

        @Override
        public void onWorkflowCompleted(StreamExecutionContext context, Object finalResult) {
            strategies.forEach(strategy -> strategy.onWorkflowCompleted(context, finalResult));
        }

        @Override
        public void onWorkflowFailed(StreamExecutionContext context, String errorMessage) {
            strategies.forEach(strategy -> strategy.onWorkflowFailed(context, errorMessage));
        }

        @Override
        public void onProgressUpdate(StreamExecutionContext context, int progress, int executedNodes, int totalNodes) {
            strategies.forEach(strategy -> strategy.onProgressUpdate(context, progress, executedNodes, totalNodes));
        }

        @Override
        public void onEvent(StreamExecutionContext context, StreamEventType eventType, Object data) {
            strategies.forEach(strategy -> strategy.onEvent(context, eventType, data));
        }

        @Override
        public void cleanup(StreamExecutionContext context) {
            strategies.forEach(strategy -> strategy.cleanup(context));
        }
    }
}