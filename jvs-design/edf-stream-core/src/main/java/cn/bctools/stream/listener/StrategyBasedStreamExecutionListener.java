package cn.bctools.stream.listener;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import cn.bctools.stream.enums.StreamOutputType;
import cn.bctools.stream.factory.StreamOutputStrategyFactory;
import cn.bctools.stream.manager.StreamExecutionManager;
import cn.bctools.stream.strategy.StreamOutputStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于策略的流式执行监听器
 * 将原有的StreamOutputStrategyFactory与新的监听器模式结合
 *
 * <AUTHOR>
 */
@Slf4j
@Component("primaryStreamExecutionListener")
@RequiredArgsConstructor
public class StrategyBasedStreamExecutionListener implements StreamExecutionListener {

    private final StreamOutputStrategyFactory strategyFactory;

    private final StreamExecutionManager executionManager;

    /**
     * 执行上下文缓存
     */
    private final Map<String, StreamExecutionContext> contextCache = new ConcurrentHashMap<>();

    @Override
    public void onWorkflowStarted(String executionId, int totalNodes) {
        log.info("{} - 执行ID: {}, 总节点数: {}",
                StreamConstants.LogMessages.WORKFLOW_STARTED, executionId, totalNodes);

        // 创建执行上下文并缓存
        try {
            StreamExecutionContext context = executionManager.getContext(executionId);
            if (context != null) {
                contextCache.put(executionId, context);

                // 使用策略开始执行
                StreamOutputStrategy strategy = strategyFactory.getStrategy(context.getOutputType());
                if (strategy != null) {
                    strategy.onWorkflowStarted(context, totalNodes);
                }
            }
        } catch (Exception e) {
            log.error("处理工作流开始事件失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
        }
    }

    @Override
    public void onNodeStarted(StreamNodeExecutionDto nodeExecution) {
        log.info("{} - 节点: {} ({}), 执行ID: {}",
                StreamConstants.LogMessages.NODE_STARTED,
                nodeExecution.getNodeName(),
                nodeExecution.getFunctionName(),
                nodeExecution.getExecutionId());

        try {
            StreamExecutionContext context = contextCache.get(nodeExecution.getExecutionId());
            if (context != null) {
                StreamOutputStrategy strategy = strategyFactory.getStrategy(context.getOutputType());
                if (strategy != null) {
                    strategy.onNodeStarted(context, nodeExecution);
                }
            }
        } catch (Exception e) {
            log.error("处理节点开始事件失败 - 执行ID: {}, 节点: {}, 错误: {}",
                    nodeExecution.getExecutionId(), nodeExecution.getNodeName(), e.getMessage());
        }
    }

    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        log.debug("{} - 节点: {} ({}), 耗时: {}ms, 执行ID: {}",
                StreamConstants.LogMessages.NODE_COMPLETED,
                nodeExecution.getNodeName(),
                nodeExecution.getFunctionName(),
                nodeExecution.getDuration(),
                nodeExecution.getExecutionId());

        try {
            StreamExecutionContext context = contextCache.get(nodeExecution.getExecutionId());
            if (context != null) {
                StreamOutputStrategy strategy = strategyFactory.getStrategy(context.getOutputType());
                if (strategy != null) {
                    strategy.onNodeCompleted(context, nodeExecution);
                }
            }
        } catch (Exception e) {
            log.error("处理节点完成事件失败 - 执行ID: {}, 节点: {}, 错误: {}",
                    nodeExecution.getExecutionId(), nodeExecution.getNodeName(), e.getMessage());
        }
    }

    @Override
    public void onNodeFailed(StreamNodeExecutionDto nodeExecution) {
        log.error("{} - 节点: {} ({}), 错误: {}, 执行ID: {}",
                StreamConstants.LogMessages.NODE_FAILED,
                nodeExecution.getNodeName(),
                nodeExecution.getFunctionName(),
                nodeExecution.getErrorMessage(),
                nodeExecution.getExecutionId());

        try {
            StreamExecutionContext context = contextCache.get(nodeExecution.getExecutionId());
            if (context != null) {
                StreamOutputStrategy strategy = strategyFactory.getStrategy(context.getOutputType());
                if (strategy != null) {
                    strategy.onNodeFailed(context, nodeExecution);
                }
            }
        } catch (Exception e) {
            log.error("处理节点失败事件失败 - 执行ID: {}, 节点: {}, 错误: {}",
                    nodeExecution.getExecutionId(), nodeExecution.getNodeName(), e.getMessage());
        }
    }

    @Override
    public void onWorkflowCompleted(String executionId, Object result) {
        log.info("{} - 执行ID: {}, 结果类型: {}",
                StreamConstants.LogMessages.WORKFLOW_COMPLETED,
                executionId,
                result != null ? result.getClass().getSimpleName() : "null");

        try {
            StreamExecutionContext context = contextCache.get(executionId);
            if (context != null) {
                StreamOutputStrategy strategy = strategyFactory.getStrategy(context.getOutputType());
                if (strategy != null) {
                    strategy.onWorkflowCompleted(context, result);
                }

                // 清理缓存
                contextCache.remove(executionId);
            }
        } catch (Exception e) {
            log.error("处理工作流完成事件失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
        }
    }

    @Override
    public void onWorkflowFailed(String executionId, String errorMessage) {
        log.error("{} - 执行ID: {}, 错误: {}",
                StreamConstants.LogMessages.WORKFLOW_FAILED,
                executionId,
                errorMessage);

        try {
            StreamExecutionContext context = contextCache.get(executionId);
            if (context != null) {
                StreamOutputStrategy strategy = strategyFactory.getStrategy(context.getOutputType());
                if (strategy != null) {
                    strategy.onWorkflowFailed(context, errorMessage);
                }

                // 清理缓存
                contextCache.remove(executionId);
            }
        } catch (Exception e) {
            log.error("处理工作流失败事件失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
        }
    }

    @Override
    public void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
        log.debug("📊 进度更新 - 执行ID: {}, 进度: {}% ({}/{})",
                executionId, progress, executedNodes, totalNodes);

        try {
            StreamExecutionContext context = contextCache.get(executionId);
            if (context != null) {
                StreamOutputStrategy strategy = strategyFactory.getStrategy(context.getOutputType());
                if (strategy != null) {
                    strategy.onProgressUpdate(context, progress, executedNodes, totalNodes);
                }
            }
        } catch (Exception e) {
            log.error("处理进度更新事件失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
        }
    }

    /**
     * 获取支持的输出类型列表
     *
     * @return 支持的输出类型
     */
    public List<StreamOutputType> getSupportedOutputTypes() {
        return strategyFactory != null ? strategyFactory.getSupportedOutputTypes() : new ArrayList<>();
    }
}