package cn.bctools.ai.stream.strategy.impl;

import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.rule.dto.StreamNodeExecutionDto;
import cn.bctools.ai.stream.enums.StreamEventType;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.stream.strategy.StreamOutputStrategy;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE (Server-Sent Events) 流式输出策略
 * 通过SSE单向推送执行状态给客户端
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SseStreamOutputStrategy implements StreamOutputStrategy {
    
    /**
     * 存储SSE发射器的映射
     */
    private final ConcurrentHashMap<String, SseEmitter> sseEmitterMap = new ConcurrentHashMap<>();
    
    @Override
    public StreamOutputType getOutputType() {
        return StreamOutputType.SSE;
    }
    
    @Override
    public void initialize(StreamExecutionContext context) {
        log.info("🚀 初始化SSE流式输出 - 执行ID: {}", context.getExecutionId());
        
        // 创建SSE发射器
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        sseEmitterMap.put(context.getExecutionId(), emitter);
        context.setSseEmitter(emitter);
        
        // 设置回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成 - 执行ID: {}", context.getExecutionId());
            sseEmitterMap.remove(context.getExecutionId());
        });
        
        emitter.onTimeout(() -> {
            log.info("SSE连接超时 - 执行ID: {}", context.getExecutionId());
            sseEmitterMap.remove(context.getExecutionId());
        });
        
        emitter.onError((ex) -> {
            log.error("SSE连接错误 - 执行ID: {}, 错误: {}", context.getExecutionId(), ex.getMessage());
            sseEmitterMap.remove(context.getExecutionId());
        });
        
        // 发送连接成功消息
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("SSE连接已建立 - 执行ID: " + context.getExecutionId()));
        } catch (IOException e) {
            log.error("发送SSE连接成功消息失败", e);
        }
    }
    
    @Override
    public void onWorkflowStarted(StreamExecutionContext context, int totalNodes) {
        JSONObject data = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("totalNodes", totalNodes)
                .fluentPut("status", "started")
                .fluentPut("timestamp", System.currentTimeMillis());
        
        sendSseEvent(context, StreamEventType.WORKFLOW_STARTED, data);
        log.info("🚀 [SSE] 工作流开始执行 - 执行ID: {}, 总节点数: {}", context.getExecutionId(), totalNodes);
    }
    
    @Override
    public void onNodeStarted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        sendSseEvent(context, StreamEventType.NODE_STARTED, nodeExecution);
        log.info("▶️ [SSE] 节点开始执行 - 节点: {} ({})", nodeExecution.getNodeName(), nodeExecution.getNodeId());
    }
    
    @Override
    public void onNodeRunning(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        sendSseEvent(context, StreamEventType.NODE_RUNNING, nodeExecution);
        log.debug("⏳ [SSE] 节点执行中 - 节点: {} ({})", nodeExecution.getNodeName(), nodeExecution.getNodeId());
    }
    
    @Override
    public void onNodeCompleted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        sendSseEvent(context, StreamEventType.NODE_COMPLETED, nodeExecution);
        log.info("✅ [SSE] 节点执行完成 - 节点: {} ({}), 耗时: {}ms", 
                nodeExecution.getNodeName(), nodeExecution.getNodeId(), nodeExecution.getDuration());
    }
    
    @Override
    public void onNodeFailed(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        sendSseEvent(context, StreamEventType.NODE_FAILED, nodeExecution);
        log.error("❌ [SSE] 节点执行失败 - 节点: {} ({}), 错误: {}", 
                nodeExecution.getNodeName(), nodeExecution.getNodeId(), nodeExecution.getErrorMessage());
    }
    
    @Override
    public void onWorkflowCompleted(StreamExecutionContext context, Object finalResult) {
        JSONObject data = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("finalResult", finalResult)
                .fluentPut("stats", context.getStats())
                .fluentPut("timestamp", System.currentTimeMillis());
        
        sendSseEvent(context, StreamEventType.WORKFLOW_COMPLETED, data);
        closeSseConnection(context);
        log.info("🎉 [SSE] 工作流执行完成 - 执行ID: {}", context.getExecutionId());
    }
    
    @Override
    public void onWorkflowFailed(StreamExecutionContext context, String errorMessage) {
        JSONObject data = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("errorMessage", errorMessage)
                .fluentPut("stats", context.getStats())
                .fluentPut("timestamp", System.currentTimeMillis());
        
        sendSseEvent(context, StreamEventType.WORKFLOW_FAILED, data);
        closeSseConnection(context);
        log.error("💥 [SSE] 工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), errorMessage);
    }
    
    @Override
    public void onProgressUpdate(StreamExecutionContext context, int progress, int executedNodes, int totalNodes) {
        JSONObject data = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("progress", progress)
                .fluentPut("executedNodes", executedNodes)
                .fluentPut("totalNodes", totalNodes)
                .fluentPut("timestamp", System.currentTimeMillis());
        
        sendSseEvent(context, StreamEventType.PROGRESS_UPDATE, data);
        log.info("📊 [SSE] 进度更新 - 执行ID: {}, 进度: {}% ({}/{})", 
                context.getExecutionId(), progress, executedNodes, totalNodes);
    }
    
    @Override
    public void onEvent(StreamExecutionContext context, StreamEventType eventType, Object data) {
        sendSseEvent(context, eventType, data);
        log.debug("📡 [SSE] 通用事件 - 类型: {}, 数据: {}", eventType.getCode(), data);
    }
    
    @Override
    public void cleanup(StreamExecutionContext context) {
        closeSseConnection(context);
        log.info("🧹 [SSE] 清理完成 - 执行ID: {}", context.getExecutionId());
    }
    
    @Override
    public boolean supports(StreamExecutionContext context) {
        return context.getOutputType() == StreamOutputType.SSE;
    }
    
    /**
     * 获取SSE发射器
     * 
     * @param executionId 执行ID
     * @return SSE发射器
     */
    public SseEmitter getSseEmitter(String executionId) {
        return sseEmitterMap.get(executionId);
    }
    
    /**
     * 添加SSE发射器
     * 
     * @param executionId 执行ID
     * @param emitter SSE发射器
     */
    public void addSseEmitter(String executionId, SseEmitter emitter) {
        sseEmitterMap.put(executionId, emitter);
        log.info("SSE发射器已添加 - 执行ID: {}", executionId);
    }
    
    /**
     * 移除SSE发射器
     * 
     * @param executionId 执行ID
     */
    public void removeSseEmitter(String executionId) {
        SseEmitter emitter = sseEmitterMap.remove(executionId);
        if (emitter != null) {
            try {
                emitter.complete();
            } catch (Exception e) {
                log.error("关闭SSE发射器失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
            }
        }
        log.info("SSE发射器已移除 - 执行ID: {}", executionId);
    }
    
    /**
     * 发送SSE事件
     */
    private void sendSseEvent(StreamExecutionContext context, StreamEventType eventType, Object data) {
        SseEmitter emitter = context.getSseEmitter();
        if (emitter == null) {
            emitter = sseEmitterMap.get(context.getExecutionId());
        }
        
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event()
                        .name(eventType.getCode())
                        .data(JSONObject.toJSONString(data)));
            } catch (IOException e) {
                log.error("发送SSE事件失败 - 执行ID: {}, 事件: {}, 错误: {}", 
                        context.getExecutionId(), eventType.getCode(), e.getMessage());
                sseEmitterMap.remove(context.getExecutionId());
            }
        }
    }
    
    /**
     * 关闭SSE连接
     */
    private void closeSseConnection(StreamExecutionContext context) {
        SseEmitter emitter = sseEmitterMap.get(context.getExecutionId());
        if (emitter != null) {
            try {
                emitter.complete();
            } catch (Exception e) {
                log.error("关闭SSE连接失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage());
            } finally {
                sseEmitterMap.remove(context.getExecutionId());
            }
        }
    }
}