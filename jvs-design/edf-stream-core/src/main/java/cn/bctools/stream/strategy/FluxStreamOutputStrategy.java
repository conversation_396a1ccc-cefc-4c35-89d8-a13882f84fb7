package cn.bctools.stream.strategy;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import cn.bctools.stream.enums.StreamEventType;
import cn.bctools.stream.enums.StreamOutputType;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Flux流式输出策略
 * 使用Reactive Streams提供响应式流式输出
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FluxStreamOutputStrategy implements StreamOutputStrategy {

    /**
     * 存储Flux发射器的映射
     */
    private final ConcurrentMap<String, FluxSink<Object>> fluxSinkMap = new ConcurrentHashMap<>();

    @Override
    public StreamOutputType getOutputType() {
        return StreamOutputType.FLUX;
    }

    @Override
    public void initialize(StreamExecutionContext context) {
        log.info("🚀 初始化Flux流式输出 - 执行ID: {}", context.getExecutionId());
        // FluxSink会在getFlux方法中创建
    }

    @Override
    public void onWorkflowStarted(StreamExecutionContext context, int totalNodes) {
        context.getStats().setTotalNodes(totalNodes);

        JSONObject event = createEvent(StreamEventType.WORKFLOW_STARTED,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("totalNodes", totalNodes)
                        .fluentPut("status", "started")
                        .fluentPut("timestamp", System.currentTimeMillis()));

        emitEvent(context, event);
        log.info("{} [FLUX] 工作流开始执行 - 执行ID: {}, 总节点数: {}", StreamConstants.LogMessages.WORKFLOW_STARTED, context.getExecutionId(), totalNodes);
    }

    @Override
    public void onNodeStarted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        JSONObject event = createEvent(StreamEventType.NODE_STARTED, nodeExecution);
        emitEvent(context, event);
        log.info("▶️ [FLUX] 节点开始执行 - 节点: {} ({})", nodeExecution.getNodeName(), nodeExecution.getNodeId());
    }

    @Override
    public void onNodeRunning(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        JSONObject event = createEvent(StreamEventType.NODE_RUNNING, nodeExecution);
        emitEvent(context, event);
        log.debug("⏳ [FLUX] 节点执行中 - 节点: {} ({})", nodeExecution.getNodeName(), nodeExecution.getNodeId());
    }

    @Override
    public void onNodeCompleted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        context.getStats().incrementExecutedNodes();
        context.getStats().updateAverageDuration(nodeExecution.getDuration());

        JSONObject event = createEvent(StreamEventType.NODE_COMPLETED, nodeExecution);
        emitEvent(context, event);
        log.info("✅ [FLUX] 节点执行完成 - 节点: {} ({}), 耗时: {}ms",
                nodeExecution.getNodeName(), nodeExecution.getNodeId(), nodeExecution.getDuration());
    }

    @Override
    public void onNodeFailed(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        context.getStats().incrementFailedNodes();

        JSONObject event = createEvent(StreamEventType.NODE_FAILED, nodeExecution);
        emitEvent(context, event);
        log.error("❌ [FLUX] 节点执行失败 - 节点: {} ({}), 错误: {}",
                nodeExecution.getNodeName(), nodeExecution.getNodeId(), nodeExecution.getErrorMessage());
    }

    @Override
    public void onWorkflowCompleted(StreamExecutionContext context, Object finalResult) {
        JSONObject event = createEvent(StreamEventType.WORKFLOW_COMPLETED,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("finalResult", finalResult)
                        .fluentPut("stats", context.getStats())
                        .fluentPut("timestamp", System.currentTimeMillis()));

        emitEvent(context, event);
        completeFlux(context);
        log.info("🎉 [FLUX] 工作流执行完成 - 执行ID: {}", context.getExecutionId());
    }

    @Override
    public void onWorkflowFailed(StreamExecutionContext context, String errorMessage) {
        JSONObject event = createEvent(StreamEventType.WORKFLOW_FAILED,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("errorMessage", errorMessage)
                        .fluentPut("stats", context.getStats())
                        .fluentPut("timestamp", System.currentTimeMillis()));

        emitEvent(context, event);
        errorFlux(context, new RuntimeException(errorMessage));
        log.error("💥 [FLUX] 工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), errorMessage);
    }

    @Override
    public void onProgressUpdate(StreamExecutionContext context, int progress, int executedNodes, int totalNodes) {
        JSONObject event = createEvent(StreamEventType.PROGRESS_UPDATE,
                new JSONObject()
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("progress", progress)
                        .fluentPut("executedNodes", executedNodes)
                        .fluentPut("totalNodes", totalNodes)
                        .fluentPut("timestamp", System.currentTimeMillis()));

        emitEvent(context, event);
        log.info("📊 [FLUX] 进度更新 - 执行ID: {}, 进度: {}% ({}/{})",
                context.getExecutionId(), progress, executedNodes, totalNodes);
    }

    @Override
    public void onEvent(StreamExecutionContext context, StreamEventType eventType, Object data) {
        JSONObject event = createEvent(eventType, data);
        emitEvent(context, event);
        log.debug("📡 [FLUX] 通用事件 - 类型: {}, 数据: {}", eventType.getCode(), data);
    }

    @Override
    public Flux<Object> getFlux(StreamExecutionContext context) {
        return Flux.<Object>create(sink -> {
                    // 存储FluxSink以便后续使用
                    fluxSinkMap.put(context.getExecutionId(), sink);
                    context.setFluxSink(sink);

                    // 设置清理回调
                    sink.onDispose(() -> {
                        fluxSinkMap.remove(context.getExecutionId());
                        log.info("🧹 [FLUX] 清理Flux资源 - 执行ID: {}", context.getExecutionId());
                    });

                    // 发送连接成功事件
                    JSONObject connectEvent = createEvent(StreamEventType.CUSTOM,
                            new JSONObject()
                                    .fluentPut("message", "Flux连接已建立")
                                    .fluentPut("executionId", context.getExecutionId())
                                    .fluentPut("timestamp", System.currentTimeMillis()));
                    sink.next(connectEvent);

                })
                .doOnSubscribe(subscription ->
                        log.info("🔗 [FLUX] 客户端订阅 - 执行ID: {}", context.getExecutionId()))
                .doOnCancel(() ->
                        log.info("🚫 [FLUX] 客户端取消订阅 - 执行ID: {}", context.getExecutionId()))
                // 10分钟超时
                .timeout(Duration.ofMinutes(10))
                // 允许多个订阅者
                .share();
    }

    @Override
    public void cleanup(StreamExecutionContext context) {
        FluxSink<Object> sink = fluxSinkMap.remove(context.getExecutionId());
        if (sink != null && !sink.isCancelled()) {
            sink.complete();
        }
        log.info("🧹 [FLUX] 清理完成 - 执行ID: {}", context.getExecutionId());
    }

    @Override
    public boolean supports(StreamExecutionContext context) {
        return context.getOutputType() == StreamOutputType.FLUX;
    }

    /**
     * 创建标准事件格式
     */
    private JSONObject createEvent(StreamEventType eventType, Object data) {
        return new JSONObject()
                .fluentPut("event", eventType.getCode())
                .fluentPut("data", data)
                .fluentPut("timestamp", System.currentTimeMillis());
    }

    /**
     * 发射事件到Flux流
     */
    private void emitEvent(StreamExecutionContext context, Object event) {
        FluxSink<Object> sink = context.getFluxSink();
        if (sink != null && !sink.isCancelled()) {
            try {
                sink.next(event);
            } catch (Exception e) {
                log.error("发射Flux事件失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage());
            }
        }
    }

    /**
     * 完成Flux流
     */
    private void completeFlux(StreamExecutionContext context) {
        FluxSink<Object> sink = context.getFluxSink();
        if (sink != null && !sink.isCancelled()) {
            sink.complete();
        }
        fluxSinkMap.remove(context.getExecutionId());
    }

    /**
     * Flux流发生错误
     */
    private void errorFlux(StreamExecutionContext context, Throwable error) {
        FluxSink<Object> sink = context.getFluxSink();
        if (sink != null && !sink.isCancelled()) {
            sink.error(error);
        }
        fluxSinkMap.remove(context.getExecutionId());
    }
}