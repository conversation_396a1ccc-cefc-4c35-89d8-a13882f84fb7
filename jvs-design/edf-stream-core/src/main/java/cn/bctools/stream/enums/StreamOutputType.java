package cn.bctools.stream.enums;

import cn.bctools.stream.constants.StreamConstants;
import lombok.Getter;

/**
 * 流式输出类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum StreamOutputType {
    
    /**
     * 控制台日志输出
     */
    CONSOLE(StreamConstants.StreamTypeCodes.CONSOLE, "控制台日志"),
    
    /**
     * WebSocket实时推送
     */
    WEBSOCKET(StreamConstants.StreamTypeCodes.WEBSOCKET, "WebSocket推送"),
    
    /**
     * Server-Sent Events推送
     */
    SSE(StreamConstants.StreamTypeCodes.SSE, "SSE推送"),
    
    /**
     * Reactive Flux流式输出
     */
    FLUX(StreamConstants.StreamTypeCodes.FLUX, "Flux流式输出"),
    /**
     * COMPOSITE组装输出，同时输出
     */
    COMPOSITE(StreamConstants.StreamTypeCodes.COMPOSITE, "COMPOSITE组装输出");
    
    private final String code;
    private final String description;
    
    StreamOutputType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 对应的枚举
     */
    public static StreamOutputType fromCode(String code) {
        if (code == null) {
            return CONSOLE;
        }
        
        for (StreamOutputType type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("无效的流式输出类型代码: " + code);
    }
    
    /**
     * 检查是否为流式输出类型
     * 
     * @return 是否为流式输出
     */
    public boolean isStreaming() {
        return this != CONSOLE;
    }
    
    /**
     * 检查是否为实时输出类型
     * 
     * @return 是否为实时输出
     */
    public boolean isRealtime() {
        return this == WEBSOCKET || this == SSE || this == FLUX;
    }
}