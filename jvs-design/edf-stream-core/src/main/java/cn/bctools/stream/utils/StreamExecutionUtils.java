package cn.bctools.stream.utils;

import cn.bctools.stream.constants.StreamConstants;

import cn.bctools.stream.listener.StreamExecutionListener;
import cn.bctools.common.utils.SpringContextUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 流式执行工具类
 * 提供流式执行的通用工具方法
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class StreamExecutionUtils {


    /**
     * 获取主要的流式执行监听器
     *
     * @return 主监听器，如果没有则返回null
     */
    public StreamExecutionListener getPrimaryStreamListener() {
        try {
            return SpringContextUtil.getBean(StreamExecutionListener.class);
        } catch (Exception e) {
            log.warn("未找到主要的流式执行监听器: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 通知工作流开始执行
     *
     * @param executionId 执行ID
     * @param totalNodes 总节点数
     */
    public void notifyWorkflowStarted(String executionId, int totalNodes) {
        StreamExecutionListener listener = getPrimaryStreamListener();
        if (listener != null) {
            try {
                listener.onWorkflowStarted(executionId, totalNodes);
            } catch (Exception e) {
                log.error("通知工作流开始执行失败 - 监听器: {}, 执行ID: {}, 错误: {}",
                        listener.getClass().getSimpleName(), executionId, e.getMessage());
            }
        }
    }
    
    /**
     * 通知工作流执行完成
     *
     * @param executionId 执行ID
     * @param result 执行结果
     */
    public void notifyWorkflowCompleted(String executionId, Object result) {
        StreamExecutionListener listener = getPrimaryStreamListener();
        if (listener != null) {
            try {
                listener.onWorkflowCompleted(executionId, result);
            } catch (Exception e) {
                log.error("通知工作流执行完成失败 - 监听器: {}, 执行ID: {}, 错误: {}",
                        listener.getClass().getSimpleName(), executionId, e.getMessage());
            }
        }
    }

    /**
     * 通知工作流执行失败
     *
     * @param executionId 执行ID
     * @param errorMessage 错误信息
     */
    public void notifyWorkflowFailed(String executionId, String errorMessage) {
        StreamExecutionListener listener = getPrimaryStreamListener();
        if (listener != null) {
            try {
                listener.onWorkflowFailed(executionId, errorMessage);
            } catch (Exception e) {
                log.error("通知工作流执行失败失败 - 监听器: {}, 执行ID: {}, 错误: {}",
                        listener.getClass().getSimpleName(), executionId, e.getMessage());
            }
        }
    }
    
    /**
     * 生成执行ID
     * 
     * @param prefix 前缀
     * @return 执行ID
     */
    public String generateExecutionId(String prefix) {
        return prefix + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int)(Math.random() * 0x10000));
    }
    
    /**
     * 生成执行ID（默认前缀）
     * 
     * @return 执行ID
     */
    public String generateExecutionId() {
        return generateExecutionId(StreamConstants.Patterns.EXECUTION_ID_PREFIX.replace("_", ""));
    }
    
    /**
     * 计算进度百分比
     * 
     * @param executedNodes 已执行节点数
     * @param totalNodes 总节点数
     * @return 进度百分比
     */
    public int calculateProgress(int executedNodes, int totalNodes) {
        if (totalNodes <= 0) {
            return 0;
        }
        return Math.min(100, (executedNodes * 100) / totalNodes);
    }

}