package cn.bctools.stream.factory;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.listener.StreamExecutionListener;
import cn.bctools.common.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 流式执行监听器工厂
 * 管理不同类型的流式输出监听器，类似于原来的StreamOutputStrategyFactory
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StreamExecutionListenerFactory {

    /**
     * 按输出类型分组的监听器映射
     */
    private final Map<String, StreamExecutionListener> listenerMap = new ConcurrentHashMap<>();

    /**
     * 按优先级排序的监听器列表
     */
    private List<StreamExecutionListener> sortedListeners;

    @PostConstruct
    public void initListeners() {
        log.info("🏭 初始化流式执行监听器工厂...");

        try {
            List<StreamExecutionListener> listeners =
                    new ArrayList<>(SpringContextUtil.getBeanMap(StreamExecutionListener.class).values());

            // 按优先级排序监听器
            sortedListeners = listeners.stream()
                    .sorted(Comparator.comparingInt(StreamExecutionListener::getPriority))
                    .collect(Collectors.toList());

            // 构建监听器映射
            for (StreamExecutionListener listener : listeners) {
                String outputType = listener.getOutputType();
                listenerMap.put(outputType, listener);
                log.info("📝 注册流式执行监听器: {} -> {}",
                        outputType, listener.getClass().getSimpleName());
            }

            log.info("✅ 流式执行监听器工厂初始化完成，共注册 {} 个监听器", listeners.size());

        } catch (Exception e) {
            log.warn("⚠️ 初始化流式执行监听器工厂失败: {}", e.getMessage());
            sortedListeners = new ArrayList<>();
        }
    }

    /**
     * 根据输出类型获取最佳监听器
     *
     * @param outputType 输出类型
     * @return 最佳监听器
     */
    public StreamExecutionListener getBestListener(String outputType) {
        // 1. 首先尝试根据输出类型直接获取
        if (outputType != null) {
            StreamExecutionListener listener = listenerMap.get(outputType);
            if (listener != null) {
                log.debug("🎯 根据输出类型选择监听器: {} -> {}",
                        outputType, listener.getClass().getSimpleName());
                return listener;
            }
        }

        // 2. 按优先级遍历所有监听器，找到第一个支持的
        if (sortedListeners != null) {
            for (StreamExecutionListener listener : sortedListeners) {
                if (listener.supports(outputType)) {
                    log.debug("🎯 根据优先级选择监听器: {} (优先级: {})",
                            listener.getClass().getSimpleName(), listener.getPriority());
                    return listener;
                }
            }
        }

        // 3. 如果没有找到合适的监听器，返回默认的控制台监听器
        StreamExecutionListener defaultListener = listenerMap.get(StreamConstants.StreamTypeCodes.CONSOLE);
        if (defaultListener != null) {
            log.warn("⚠️ 未找到合适的监听器，使用默认控制台监听器");
            return defaultListener;
        }

        // 4. 最后的兜底方案
        if (sortedListeners != null && !sortedListeners.isEmpty()) {
            StreamExecutionListener fallbackListener = sortedListeners.get(sortedListeners.size() - 1);
            log.warn("⚠️ 使用兜底监听器: {}", fallbackListener.getClass().getSimpleName());
            return fallbackListener;
        }

        log.error("❌ 没有可用的流式执行监听器");
        return null;
    }

    /**
     * 根据输出类型获取监听器
     *
     * @param outputType 输出类型
     * @return 对应的监听器
     */
    public StreamExecutionListener getListener(String outputType) {
        StreamExecutionListener listener = listenerMap.get(outputType);
        if (listener == null) {
            log.warn("⚠️ 未找到输出类型 {} 对应的监听器", outputType);
            // 返回默认监听器
            return listenerMap.get(StreamConstants.StreamTypeCodes.CONSOLE);
        }
        return listener;
    }

    /**
     * 获取所有支持的输出类型
     *
     * @return 支持的输出类型列表
     */
    public List<String> getSupportedOutputTypes() {
        if (sortedListeners == null) {
            return new ArrayList<>();
        }
        return sortedListeners.stream()
                .map(StreamExecutionListener::getOutputType)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取所有监听器
     *
     * @return 监听器列表
     */
    public List<StreamExecutionListener> getAllListeners() {
        return sortedListeners != null ? new ArrayList<>(sortedListeners) : new ArrayList<>();
    }

    /**
     * 获取所有监听器信息
     *
     * @return 监听器信息映射
     */
    public Map<String, Object> getListenerInfo() {
        Map<String, Object> info = new ConcurrentHashMap<>();

        info.put("totalListeners", sortedListeners != null ? sortedListeners.size() : 0);
        info.put("supportedOutputTypes", getSupportedOutputTypes());

        Map<String, Object> listenerDetails = new ConcurrentHashMap<>();
        if (sortedListeners != null) {
            for (StreamExecutionListener listener : sortedListeners) {
                Map<String, Object> detail = new ConcurrentHashMap<>();
                detail.put("className", listener.getClass().getSimpleName());
                detail.put("priority", listener.getPriority());
                detail.put("supportedOutputType", listener.getOutputType());

                listenerDetails.put(listener.getClass().getSimpleName(), detail);
            }
        }
        info.put("listeners", listenerDetails);

        return info;
    }

    /**
     * 检查是否支持指定的输出类型
     *
     * @param outputType 输出类型
     * @return 是否支持
     */
    public boolean isSupported(String outputType) {
        return listenerMap.containsKey(outputType);
    }

    /**
     * 智能推荐监听器
     * 根据输出类型推荐最合适的监听器
     *
     * @param outputType 输出类型
     * @return 推荐的监听器和推荐理由
     */
    public ListenerRecommendation recommendListener(String outputType) {
        StreamExecutionListener listener = getBestListener(outputType);
        String reason = buildRecommendationReason(outputType, listener);

        return new ListenerRecommendation(listener, reason);
    }

    /**
     * 构建推荐理由
     */
    private String buildRecommendationReason(String outputType, StreamExecutionListener listener) {
        if (listener == null) {
            return "未找到合适的监听器";
        }

        StringBuilder reason = new StringBuilder();

        if (outputType != null && outputType.equals(listener.getOutputType())) {
            reason.append("精确匹配输出类型: ").append(outputType);
        } else if (listener.supports(outputType)) {
            reason.append("支持输出类型: ").append(outputType);
        } else {
            reason.append("使用默认监听器");
        }

        reason.append("，选择监听器: ").append(listener.getClass().getSimpleName());
        reason.append("，优先级: ").append(listener.getPriority());

        return reason.toString();
    }

    /**
     * 监听器推荐结果
     */
    public static class ListenerRecommendation {
        private final StreamExecutionListener listener;
        private final String reason;

        public ListenerRecommendation(StreamExecutionListener listener, String reason) {
            this.listener = listener;
            this.reason = reason;
        }

        public StreamExecutionListener getListener() {
            return listener;
        }

        public String getReason() {
            return reason;
        }
    }
}