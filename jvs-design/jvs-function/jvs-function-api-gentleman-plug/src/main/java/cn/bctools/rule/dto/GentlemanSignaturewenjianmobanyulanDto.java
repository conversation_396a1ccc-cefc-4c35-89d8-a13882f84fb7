package cn.bctools.rule.dto;


import cn.bctools.rule.annotations.ParameterValue;
import cn.bctools.rule.entity.enums.InputType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * The type Gentleman signaturewen<PERSON><PERSON><PERSON><PERSON><PERSON>lan dto.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class GentlemanSignaturewenjianmobanyulanDto extends JunZiQianBaseDto {
    /**
     * The Template no.
     */
    @ParameterValue(info = "模版编号或共享编码", type = InputType.input)
    public String templateNo;


}
