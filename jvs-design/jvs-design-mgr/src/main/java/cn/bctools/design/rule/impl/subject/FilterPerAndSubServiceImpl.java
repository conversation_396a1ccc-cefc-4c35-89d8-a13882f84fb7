package cn.bctools.design.rule.impl.subject;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.design.crud.vo.TreeVo;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: Aaron2
 */
@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "学段学科过滤级联数据",
        group = RuleGroup.字典模块,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.对象,
        explain = "根据学段学科级联数据过滤(不传则返回所有)"
)
public class FilterPerAndSubServiceImpl implements BaseCustomFunctionInterface<FilterPerAndSubDto> {

    JvsTreeService treeService;

    @Override
    public Object execute(FilterPerAndSubDto filterPerAndSubDto, Map<String, Object> params) {
        List<TreeVo> perAndSubList = treeService.getPerAndSubList("function", filterPerAndSubDto.getOpen(), filterPerAndSubDto.getPeriodType(), filterPerAndSubDto.getSubjectType());
        if (ObjectNull.isNull(filterPerAndSubDto.getUniqueNames())) {
            return perAndSubList;
        }
        List<TreeVo> treeVos = filterByUniqueNames(perAndSubList, filterPerAndSubDto.getUniqueNames());

        return ObjectNull.isNull(treeVos) ? null : treeVos;
    }

    public List<TreeVo> filterByUniqueNames(List<TreeVo> treeVos, List<String> uniqueNames) {
        return treeVos.stream()
                .filter(treeVo -> shouldInclude(treeVo, uniqueNames))
                .collect(Collectors.toList());
    }

    private boolean shouldInclude(TreeVo treeVo, List<String> uniqueNames) {
        //同时处理传了学段的情况
        if (uniqueNames.contains(treeVo.getUniqueName())) {
            if (ObjectNull.isNotNull(treeVo.getChildren())) {
                List<TreeVo> treeVos = filterByUniqueNames(treeVo.getChildren(), uniqueNames);
                treeVo.setChildren(ObjectNull.isNotNull(treeVos) ? treeVos : null);
            }
            return true;
        } else if (ObjectNull.isNotNull(treeVo.getChildren())) {
            List<TreeVo> filteredChildren = filterByUniqueNames(treeVo.getChildren(), uniqueNames);
            treeVo.setChildren(filteredChildren);
            return !filteredChildren.isEmpty();
        }
        return false;
    }
}
