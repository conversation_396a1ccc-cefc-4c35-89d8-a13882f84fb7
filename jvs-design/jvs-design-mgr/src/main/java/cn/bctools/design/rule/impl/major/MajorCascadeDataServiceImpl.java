package cn.bctools.design.rule.impl.major;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.TreeUtils;
import cn.bctools.design.crud.entity.JvsTree;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.design.crud.vo.TreeVo;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "获取专业级联数据",
        group = RuleGroup.字典模块,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.数组,
        explain = "获取专业级联数据,根据传入的专业大类唯一标识数组构造树形数据返回"
)
public class MajorCascadeDataServiceImpl implements BaseCustomFunctionInterface<MajorCascadeDataDto> {
    JvsTreeService treeService;

    public final static String TYPE = "major";

    @Override
    public void inspect(MajorCascadeDataDto majorCascadeDataDto) {
        if (ObjectNull.isNull(majorCascadeDataDto.getUniqueNames())) {
            throw new BusinessException("请传入专业大类唯一标识数组");
        }
    }

    @Override
    public Object execute(MajorCascadeDataDto majorCascadeDataDto, Map<String, Object> params) {
        List<JvsTree> list = treeService.list(new LambdaQueryWrapper<JvsTree>()
                .select(JvsTree::getUniqueName)
                .eq(JvsTree::getType, TYPE)
                .in(JvsTree::getUniqueName, majorCascadeDataDto.getUniqueNames()));
        List<String> uniqueNames = list.stream().map(JvsTree::getUniqueName).collect(Collectors.toList());

        List<JvsTree> all = treeService.list(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getType, TYPE));
        List<TreeVo> allEntities = BeanCopyUtil.copys(all, TreeVo.class);

        List<TreeVo> tree = TreeUtils.listToTreeWithMultiRoots(allEntities, uniqueNames, TreeVo::getUniqueName, TreeVo::getParentId, TreeVo::setChildren, true, Comparator.comparing(TreeVo::getSort));

        processTreeList(tree);

        return tree;

    }

    public void processTreeList(List<TreeVo> treeList) {
        for (TreeVo tree : treeList) {
            processTree(tree);
        }
    }

    private void processTree(TreeVo node) {
        if (ObjectNull.isNotNull(node.getChildren())) {
            for (TreeVo child : node.getChildren()) {
                processTree(child);
            }
            node.setDisabled(true);
        } else {
            node.setDisabled(false);
            node.setChildren(null);
        }
    }
}
