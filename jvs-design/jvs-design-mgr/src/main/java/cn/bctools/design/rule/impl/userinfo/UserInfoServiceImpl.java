package cn.bctools.design.rule.impl.userinfo;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Rule(value = "获取指定用户信息",
        group = RuleGroup.用户角色模块,
        test = true,
        returnType = ClassType.对象,
        testShowEnum = TestShowEnum.JSON,
        order = 4,
        explain = "获取指定用户信息"
)
public class UserInfoServiceImpl implements BaseCustomFunctionInterface<UserInfoDto> {

    AuthUserServiceApi userServiceApi;

    @Override
    public void inspect(UserInfoDto o) {
        if (StrUtil.isEmpty(o.getUserId()) && StrUtil.isEmpty(o.getAccountName())&&StrUtil.isEmpty(o.getPhone())) {
           throw new BusinessException("用户id和用户账号和手机号不能同时为空");
        }
    }

    @Override
    public Object execute(UserInfoDto userInfoDto, Map<String, Object> params) {
        if (ObjectNull.isNull(userInfoDto.getUserId())) {
            userInfoDto.setUserId("");
        }
        if (ObjectNull.isNull(userInfoDto.getAccountName())){
            userInfoDto.setAccountName("");
        }
        if (ObjectNull.isNull(userInfoDto.getPhone())){
            userInfoDto.setPhone("");
        }
        R r = new R();
        if(userInfoDto.getDetail()){
            r = userServiceApi.getByIdAndAccountNameAndPhone(userInfoDto.getUserId(), userInfoDto.getAccountName(),userInfoDto.getPhone());
        }else {
            List<String> userIds = new ArrayList<>();
            if (ObjectNull.isNotNull(userInfoDto.getUserId())){
                userIds.add(userInfoDto.getUserId());
            }
            r = userServiceApi.searchUserGetInfo(new SearchUserDto()
                    .setUserIds(userIds)
                    .setAccountName(userInfoDto.getAccountName())
                    .setPhone(userInfoDto.getPhone()));
        }

        if (ObjectNull.isNull(r.getData())) {
            return null;
        }
        if (userInfoDto.getDetail()){
            return r.getData();
        }else {
            return ((List<?>)r.getData()).get(0);
        }
    }
}
