package cn.bctools.design.crud.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TreeVo implements Serializable,Comparable<TreeVo>  {

    private static final long serialVersionUID = 1L;

    public static final String DICT_ID_ROOT = "-1";

    private String id;

    private String name;

    private String value;

    private String groupId;

    private String uniqueName;

    private Integer sort;

    private String remarks;

    private String parentId;

    private List<TreeVo> children;

    private String type;

    private Boolean disabled;

    @Override
    public int compareTo(@NotNull TreeVo o) {
        return Integer.compare(this.sort,o.getSort());
    }
}