package cn.bctools.design.project.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("应用访问记录")
@EqualsAndHashCode(callSuper = false)
@TableName(value = "jvs_app_visit", autoResultMap = true)
public class JvsAppVisit implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键(app_id)")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;
    @TableField("visit_count")
    @ApiModelProperty("点击次数")
    private Integer visitCount;
}
