<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式逻辑引擎执行演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }
        .controls input, .controls button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls button {
            background: #007bff;
            color: white;
            cursor: pointer;
            border: none;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .controls button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .status-item {
            flex: 1;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .status-connected { background: #d4edda; color: #155724; }
        .status-running { background: #fff3cd; color: #856404; }
        .status-completed { background: #d1ecf1; color: #0c5460; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .progress-container {
            margin-bottom: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            transition: width 0.3s ease;
            width: 0%;
        }
        .progress-text {
            text-align: center;
            margin-top: 5px;
            font-weight: bold;
        }
        .logs {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #d1ecf1; }
        .log-success { background: #d4edda; }
        .log-warning { background: #fff3cd; }
        .log-error { background: #f8d7da; }
        .node-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .node-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: white;
        }
        .node-card.running { border-left: 4px solid #ffc107; }
        .node-card.completed { border-left: 4px solid #28a745; }
        .node-card.failed { border-left: 4px solid #dc3545; }
        .node-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .node-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            color: white;
        }
        .node-status.running { background: #ffc107; }
        .node-status.completed { background: #28a745; }
        .node-status.failed { background: #dc3545; }
        .node-content {
            font-size: 12px;
            color: #666;
        }
        .json-content {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 流式逻辑引擎执行演示</h1>
            <p>仿照Dify工作流，实时显示每个组件的执行进度和结果</p>
        </div>
        
        <div class="controls">
            <input type="text" id="executionId" placeholder="执行ID (留空自动生成)" />
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <select id="connectionType">
                <option value="sse">Server-Sent Events</option>
                <option value="websocket">WebSocket</option>
            </select>
        </div>
        
        <div class="status">
            <div class="status-item" id="connectionStatus">
                <strong>连接状态</strong><br>
                <span>未连接</span>
            </div>
            <div class="status-item" id="executionStatus">
                <strong>执行状态</strong><br>
                <span>等待中</span>
            </div>
            <div class="status-item" id="nodeCount">
                <strong>节点统计</strong><br>
                <span>0/0</span>
            </div>
        </div>
        
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">0%</div>
        </div>
        
        <div class="logs" id="logs"></div>
        
        <div class="node-details" id="nodeDetails"></div>
    </div>

    <script>
        let eventSource = null;
        let webSocket = null;
        let currentExecutionId = null;
        let nodes = new Map();

        function generateExecutionId() {
            return 'exec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function connect() {
            const executionIdInput = document.getElementById('executionId');
            const connectionType = document.getElementById('connectionType').value;
            
            currentExecutionId = executionIdInput.value.trim() || generateExecutionId();
            executionIdInput.value = currentExecutionId;
            
            if (connectionType === 'sse') {
                connectSSE();
            } else {
                connectWebSocket();
            }
        }

        function connectSSE() {
            const url = `/rule/stream/events/${currentExecutionId}`;
            eventSource = new EventSource(url);
            
            eventSource.onopen = function() {
                updateConnectionStatus('已连接 (SSE)', 'status-connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                addLog('SSE连接已建立', 'log-success');
            };
            
            eventSource.onerror = function() {
                updateConnectionStatus('连接错误', 'status-failed');
                addLog('SSE连接错误', 'log-error');
            };
            
            // 监听各种事件
            eventSource.addEventListener('workflow_started', handleWorkflowStarted);
            eventSource.addEventListener('node_started', handleNodeStarted);
            eventSource.addEventListener('node_completed', handleNodeCompleted);
            eventSource.addEventListener('node_failed', handleNodeFailed);
            eventSource.addEventListener('workflow_completed', handleWorkflowCompleted);
            eventSource.addEventListener('workflow_failed', handleWorkflowFailed);
            eventSource.addEventListener('progress_update', handleProgressUpdate);
        }

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const url = `${protocol}//${window.location.host}/ws/rule-execution/${currentExecutionId}`;
            webSocket = new WebSocket(url);
            
            webSocket.onopen = function() {
                updateConnectionStatus('已连接 (WebSocket)', 'status-connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                addLog('WebSocket连接已建立', 'log-success');
            };
            
            webSocket.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleMessage(message.event, message.data);
            };
            
            webSocket.onerror = function() {
                updateConnectionStatus('连接错误', 'status-failed');
                addLog('WebSocket连接错误', 'log-error');
            };
            
            webSocket.onclose = function() {
                updateConnectionStatus('连接已关闭', 'status-failed');
                addLog('WebSocket连接已关闭', 'log-warning');
            };
        }

        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            if (webSocket) {
                webSocket.close();
                webSocket = null;
            }
            
            updateConnectionStatus('未连接', '');
            document.getElementById('connectBtn').disabled = false;
            document.getElementById('disconnectBtn').disabled = true;
            addLog('连接已断开', 'log-info');
        }

        function handleMessage(eventType, data) {
            switch(eventType) {
                case 'workflow_started':
                    handleWorkflowStarted({data: JSON.stringify(data)});
                    break;
                case 'node_started':
                    handleNodeStarted({data: JSON.stringify(data)});
                    break;
                case 'node_completed':
                    handleNodeCompleted({data: JSON.stringify(data)});
                    break;
                case 'node_failed':
                    handleNodeFailed({data: JSON.stringify(data)});
                    break;
                case 'workflow_completed':
                    handleWorkflowCompleted({data: JSON.stringify(data)});
                    break;
                case 'workflow_failed':
                    handleWorkflowFailed({data: JSON.stringify(data)});
                    break;
                case 'progress_update':
                    handleProgressUpdate({data: JSON.stringify(data)});
                    break;
            }
        }

        function handleWorkflowStarted(event) {
            const data = JSON.parse(event.data);
            updateExecutionStatus('执行中', 'status-running');
            updateNodeCount(0, data.totalNodes);
            addLog(`🚀 工作流开始执行，总节点数: ${data.totalNodes}`, 'log-info');
        }

        function handleNodeStarted(event) {
            const data = JSON.parse(event.data);
            nodes.set(data.nodeId, data);
            updateNodeCard(data);
            addLog(`▶️ 节点开始执行: ${data.nodeName} (${data.functionName})`, 'log-info');
        }

        function handleNodeCompleted(event) {
            const data = JSON.parse(event.data);
            nodes.set(data.nodeId, data);
            updateNodeCard(data);
            addLog(`✅ 节点执行完成: ${data.nodeName}，耗时: ${data.duration}ms`, 'log-success');
        }

        function handleNodeFailed(event) {
            const data = JSON.parse(event.data);
            nodes.set(data.nodeId, data);
            updateNodeCard(data);
            addLog(`❌ 节点执行失败: ${data.nodeName}，错误: ${data.errorMessage}`, 'log-error');
        }

        function handleWorkflowCompleted(event) {
            updateExecutionStatus('已完成', 'status-completed');
            addLog('🎉 工作流执行完成', 'log-success');
        }

        function handleWorkflowFailed(event) {
            updateExecutionStatus('执行失败', 'status-failed');
            addLog('💥 工作流执行失败', 'log-error');
        }

        function handleProgressUpdate(event) {
            const data = JSON.parse(event.data);
            updateProgress(data.progress);
            updateNodeCount(data.executedNodes, data.totalNodes);
        }

        function updateConnectionStatus(status, className) {
            const element = document.getElementById('connectionStatus');
            element.className = 'status-item ' + className;
            element.querySelector('span').textContent = status;
        }

        function updateExecutionStatus(status, className) {
            const element = document.getElementById('executionStatus');
            element.className = 'status-item ' + className;
            element.querySelector('span').textContent = status;
        }

        function updateNodeCount(executed, total) {
            document.getElementById('nodeCount').querySelector('span').textContent = `${executed}/${total}`;
        }

        function updateProgress(progress) {
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = progress + '%';
        }

        function addLog(message, className) {
            const logs = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + className;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        function updateNodeCard(nodeData) {
            const nodeDetails = document.getElementById('nodeDetails');
            let card = document.getElementById('node-' + nodeData.nodeId);
            
            if (!card) {
                card = document.createElement('div');
                card.id = 'node-' + nodeData.nodeId;
                card.className = 'node-card';
                nodeDetails.appendChild(card);
            }
            
            card.className = 'node-card ' + nodeData.status;
            
            const statusClass = nodeData.status;
            const statusText = {
                'running': '执行中',
                'completed': '已完成',
                'failed': '失败'
            }[nodeData.status] || nodeData.status;
            
            card.innerHTML = `
                <div class="node-title">
                    <span>${nodeData.nodeName}</span>
                    <span class="node-status ${statusClass}">${statusText}</span>
                </div>
                <div class="node-content">
                    <p><strong>功能:</strong> ${nodeData.functionName}</p>
                    <p><strong>类型:</strong> ${nodeData.nodeType}</p>
                    ${nodeData.duration ? `<p><strong>耗时:</strong> ${nodeData.duration}ms</p>` : ''}
                    ${nodeData.inputs ? `<p><strong>输入参数:</strong></p><div class="json-content">${JSON.stringify(nodeData.inputs, null, 2)}</div>` : ''}
                    ${nodeData.outputs ? `<p><strong>输出结果:</strong></p><div class="json-content">${JSON.stringify(nodeData.outputs, null, 2)}</div>` : ''}
                    ${nodeData.errorMessage ? `<p><strong>错误信息:</strong> ${nodeData.errorMessage}</p>` : ''}
                </div>
            `;
        }

        // 页面加载完成后自动生成执行ID
        window.onload = function() {
            document.getElementById('executionId').value = generateExecutionId();
        };
    </script>
</body>
</html>
