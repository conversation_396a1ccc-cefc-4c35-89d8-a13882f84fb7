package cn.bctools.rule.config;

import cn.bctools.rule.listener.WebSocketStreamExecutionListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.*;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import java.net.URI;

/**
 * WebSocket配置
 * 用于实时推送逻辑引擎执行进度
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Autowired
    private WebSocketStreamExecutionListener webSocketStreamListener;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new RuleExecutionWebSocketHandler(), "/ws/rule-execution/{executionId}")
                .setAllowedOrigins("*"); // 生产环境应该限制域名
    }
    
    /**
     * 逻辑引擎执行WebSocket处理器
     */
    public class RuleExecutionWebSocketHandler implements WebSocketHandler {
        
        @Override
        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
            String executionId = extractExecutionId(session);
            if (executionId != null) {
                webSocketStreamListener.addSession(executionId, session);
                log.info("WebSocket连接已建立 - 执行ID: {}, 会话ID: {}", executionId, session.getId());
            } else {
                log.warn("无法从WebSocket路径中提取执行ID，关闭连接");
                session.close();
            }
        }
        
        @Override
        public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
            // 处理客户端发送的消息（如果需要）
            log.debug("收到WebSocket消息 - 会话ID: {}, 消息: {}", session.getId(), message.getPayload());
        }
        
        @Override
        public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
            log.error("WebSocket传输错误 - 会话ID: {}, 错误: {}", session.getId(), exception.getMessage());
            String executionId = extractExecutionId(session);
            if (executionId != null) {
                webSocketStreamListener.removeSession(executionId, session);
            }
        }
        
        @Override
        public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
            String executionId = extractExecutionId(session);
            if (executionId != null) {
                webSocketStreamListener.removeSession(executionId, session);
                log.info("WebSocket连接已关闭 - 执行ID: {}, 会话ID: {}, 状态: {}", 
                        executionId, session.getId(), closeStatus);
            }
        }
        
        @Override
        public boolean supportsPartialMessages() {
            return false;
        }
        
        /**
         * 从WebSocket会话中提取执行ID
         */
        private String extractExecutionId(WebSocketSession session) {
            try {
                URI uri = session.getUri();
                if (uri != null) {
                    String path = uri.getPath();
                    // 从路径 /ws/rule-execution/{executionId} 中提取executionId
                    String[] segments = path.split("/");
                    if (segments.length >= 3) {
                        return segments[segments.length - 1];
                    }
                }
            } catch (Exception e) {
                log.error("提取执行ID失败", e);
            }
            return null;
        }
    }
}
