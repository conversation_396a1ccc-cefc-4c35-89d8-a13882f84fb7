package cn.bctools.rule.listener;

import cn.bctools.rule.dto.StreamNodeExecutionDto;
import cn.bctools.rule.enums.StreamEventType;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认流式执行监听器实现
 * 将执行过程输出到日志，可以被继承或替换为其他实现（如WebSocket、SSE等）
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultStreamExecutionListener implements StreamExecutionListener {
    
    @Override
    public void onWorkflowStarted(String executionId, int totalNodes) {
        StreamNodeExecutionDto event = new StreamNodeExecutionDto()
                .setExecutionId(executionId)
                .setStatus("started")
                .setTotalNodes(totalNodes)
                .setExecutedNodes(0)
                .setProgress(0);
        
        log.info("🚀 [STREAM] 工作流开始执行 - 执行ID: {}, 总节点数: {}", executionId, totalNodes);
        outputStreamEvent(StreamEventType.WORKFLOW_STARTED, event);
    }
    
    @Override
    public void onNodeStarted(StreamNodeExecutionDto nodeExecution) {
        nodeExecution.setStatus("running");
        log.info("▶️ [STREAM] 节点开始执行 - 节点: {} ({}), 功能: {}", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId(), 
                nodeExecution.getFunctionName());
        outputStreamEvent(StreamEventType.NODE_STARTED, nodeExecution);
    }
    
    @Override
    public void onNodeRunning(StreamNodeExecutionDto nodeExecution) {
        log.debug("⏳ [STREAM] 节点执行中 - 节点: {} ({})", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId());
        outputStreamEvent(StreamEventType.NODE_RUNNING, nodeExecution);
    }
    
    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        nodeExecution.setStatus("completed");
        log.info("✅ [STREAM] 节点执行完成 - 节点: {} ({}), 耗时: {}ms", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId(), 
                nodeExecution.getDuration());
        outputStreamEvent(StreamEventType.NODE_COMPLETED, nodeExecution);
    }
    
    @Override
    public void onNodeFailed(StreamNodeExecutionDto nodeExecution) {
        nodeExecution.setStatus("failed");
        log.error("❌ [STREAM] 节点执行失败 - 节点: {} ({}), 错误: {}", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId(), 
                nodeExecution.getErrorMessage());
        outputStreamEvent(StreamEventType.NODE_FAILED, nodeExecution);
    }
    
    @Override
    public void onWorkflowCompleted(String executionId, Object finalResult) {
        log.info("🎉 [STREAM] 工作流执行完成 - 执行ID: {}", executionId);
        outputStreamEvent(StreamEventType.WORKFLOW_COMPLETED, finalResult);
    }
    
    @Override
    public void onWorkflowFailed(String executionId, String errorMessage) {
        log.error("💥 [STREAM] 工作流执行失败 - 执行ID: {}, 错误: {}", executionId, errorMessage);
        outputStreamEvent(StreamEventType.WORKFLOW_FAILED, errorMessage);
    }
    
    @Override
    public void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
        log.info("📊 [STREAM] 进度更新 - 执行ID: {}, 进度: {}% ({}/{})", 
                executionId, progress, executedNodes, totalNodes);
        
        StreamNodeExecutionDto progressEvent = new StreamNodeExecutionDto()
                .setExecutionId(executionId)
                .setProgress(progress)
                .setExecutedNodes(executedNodes)
                .setTotalNodes(totalNodes);
        
        outputStreamEvent(StreamEventType.PROGRESS_UPDATE, progressEvent);
    }
    
    @Override
    public void onEvent(StreamEventType eventType, Object data) {
        log.debug("📡 [STREAM] 通用事件 - 类型: {}, 数据: {}", eventType.getCode(), data);
        outputStreamEvent(eventType, data);
    }
    
    /**
     * 输出流式事件
     * 子类可以重写此方法来实现不同的输出方式（WebSocket、SSE、消息队列等）
     * 
     * @param eventType 事件类型
     * @param data 事件数据
     */
    protected void outputStreamEvent(StreamEventType eventType, Object data) {
        // 构造标准的流式输出格式
        String streamOutput = String.format("data: {\"event\": \"%s\", \"data\": %s}\n\n", 
                eventType.getCode(), 
                JSONObject.toJSONString(data));
        
        // 这里可以输出到不同的目标：
        // 1. 控制台（当前实现）
        System.out.print(streamOutput);
        
        // 2. WebSocket（需要注入WebSocket会话）
        // webSocketSession.sendMessage(new TextMessage(streamOutput));
        
        // 3. Server-Sent Events（需要SseEmitter）
        // sseEmitter.send(SseEmitter.event().data(streamOutput));
        
        // 4. 消息队列（需要消息发送器）
        // messageProducer.send(streamOutput);
    }
}
