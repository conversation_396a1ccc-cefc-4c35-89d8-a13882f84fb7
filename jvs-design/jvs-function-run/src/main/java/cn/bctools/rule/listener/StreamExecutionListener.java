package cn.bctools.rule.listener;

import cn.bctools.rule.dto.StreamNodeExecutionDto;
import cn.bctools.rule.enums.StreamEventType;

/**
 * 流式执行监听器接口
 * 用于监听节点执行过程并进行流式输出
 * 
 * <AUTHOR>
 */
public interface StreamExecutionListener {
    
    /**
     * 工作流开始事件
     * 
     * @param executionId 执行ID
     * @param totalNodes 总节点数
     */
    void onWorkflowStarted(String executionId, int totalNodes);
    
    /**
     * 节点开始执行事件
     * 
     * @param nodeExecution 节点执行信息
     */
    void onNodeStarted(StreamNodeExecutionDto nodeExecution);
    
    /**
     * 节点执行中事件（可选，用于长时间运行的节点）
     * 
     * @param nodeExecution 节点执行信息
     */
    void onNodeRunning(StreamNodeExecutionDto nodeExecution);
    
    /**
     * 节点执行完成事件
     * 
     * @param nodeExecution 节点执行信息
     */
    void onNodeCompleted(StreamNodeExecutionDto nodeExecution);
    
    /**
     * 节点执行失败事件
     * 
     * @param nodeExecution 节点执行信息
     */
    void onNodeFailed(StreamNodeExecutionDto nodeExecution);
    
    /**
     * 工作流完成事件
     * 
     * @param executionId 执行ID
     * @param finalResult 最终结果
     */
    void onWorkflowCompleted(String executionId, Object finalResult);
    
    /**
     * 工作流失败事件
     * 
     * @param executionId 执行ID
     * @param errorMessage 错误信息
     */
    void onWorkflowFailed(String executionId, String errorMessage);
    
    /**
     * 进度更新事件
     * 
     * @param executionId 执行ID
     * @param progress 进度百分比
     * @param executedNodes 已执行节点数
     * @param totalNodes 总节点数
     */
    void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes);
    
    /**
     * 通用事件处理
     * 
     * @param eventType 事件类型
     * @param data 事件数据
     */
    void onEvent(StreamEventType eventType, Object data);
}
