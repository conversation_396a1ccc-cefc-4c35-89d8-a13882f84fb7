package cn.bctools.ai.stream.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流式节点执行结果DTO
 * 仿照Dify工作流，每个组件执行都流式返回
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class StreamNodeExecutionDto {
    
    /**
     * 执行ID
     */
    private String executionId;
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型
     */
    private String nodeType;
    
    /**
     * 功能名称
     */
    private String functionName;
    
    /**
     * 执行状态：running, completed, failed
     */
    private String status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 节点输入参数
     */
    private Map<String, Object> inputs;
    
    /**
     * 节点输出结果
     */
    private Object outputs;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 执行进度百分比
     */
    private Integer progress;
    
    /**
     * 总节点数
     */
    private Integer totalNodes;
    
    /**
     * 已执行节点数
     */
    private Integer executedNodes;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> metadata;
}