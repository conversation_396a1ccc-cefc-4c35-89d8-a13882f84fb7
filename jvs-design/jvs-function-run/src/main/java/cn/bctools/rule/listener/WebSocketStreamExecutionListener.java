package cn.bctools.rule.listener;

import cn.bctools.rule.dto.StreamNodeExecutionDto;
import cn.bctools.rule.enums.StreamEventType;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * WebSocket流式执行监听器
 * 通过WebSocket实时推送执行进度给前端
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class WebSocketStreamExecutionListener implements StreamExecutionListener {
    
    /**
     * 存储WebSocket会话，按执行ID分组
     */
    private final ConcurrentHashMap<String, CopyOnWriteArraySet<WebSocketSession>> sessionMap = new ConcurrentHashMap<>();
    
    /**
     * 添加WebSocket会话
     * 
     * @param executionId 执行ID
     * @param session WebSocket会话
     */
    public void addSession(String executionId, WebSocketSession session) {
        sessionMap.computeIfAbsent(executionId, k -> new CopyOnWriteArraySet<>()).add(session);
        log.info("WebSocket会话已添加 - 执行ID: {}, 会话ID: {}", executionId, session.getId());
    }
    
    /**
     * 移除WebSocket会话
     * 
     * @param executionId 执行ID
     * @param session WebSocket会话
     */
    public void removeSession(String executionId, WebSocketSession session) {
        CopyOnWriteArraySet<WebSocketSession> sessions = sessionMap.get(executionId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                sessionMap.remove(executionId);
            }
        }
        log.info("WebSocket会话已移除 - 执行ID: {}, 会话ID: {}", executionId, session.getId());
    }
    
    @Override
    public void onWorkflowStarted(String executionId, int totalNodes) {
        StreamNodeExecutionDto event = new StreamNodeExecutionDto()
                .setExecutionId(executionId)
                .setStatus("started")
                .setTotalNodes(totalNodes)
                .setExecutedNodes(0)
                .setProgress(0);
        
        sendToWebSocket(executionId, StreamEventType.WORKFLOW_STARTED, event);
    }
    
    @Override
    public void onNodeStarted(StreamNodeExecutionDto nodeExecution) {
        sendToWebSocket(nodeExecution.getExecutionId(), StreamEventType.NODE_STARTED, nodeExecution);
    }
    
    @Override
    public void onNodeRunning(StreamNodeExecutionDto nodeExecution) {
        sendToWebSocket(nodeExecution.getExecutionId(), StreamEventType.NODE_RUNNING, nodeExecution);
    }
    
    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        sendToWebSocket(nodeExecution.getExecutionId(), StreamEventType.NODE_COMPLETED, nodeExecution);
    }
    
    @Override
    public void onNodeFailed(StreamNodeExecutionDto nodeExecution) {
        sendToWebSocket(nodeExecution.getExecutionId(), StreamEventType.NODE_FAILED, nodeExecution);
    }
    
    @Override
    public void onWorkflowCompleted(String executionId, Object finalResult) {
        sendToWebSocket(executionId, StreamEventType.WORKFLOW_COMPLETED, finalResult);
        // 工作流完成后清理会话
        sessionMap.remove(executionId);
    }
    
    @Override
    public void onWorkflowFailed(String executionId, String errorMessage) {
        sendToWebSocket(executionId, StreamEventType.WORKFLOW_FAILED, errorMessage);
        // 工作流失败后清理会话
        sessionMap.remove(executionId);
    }
    
    @Override
    public void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
        StreamNodeExecutionDto progressEvent = new StreamNodeExecutionDto()
                .setExecutionId(executionId)
                .setProgress(progress)
                .setExecutedNodes(executedNodes)
                .setTotalNodes(totalNodes);
        
        sendToWebSocket(executionId, StreamEventType.PROGRESS_UPDATE, progressEvent);
    }
    
    @Override
    public void onEvent(StreamEventType eventType, Object data) {
        // 通用事件处理，可以根据需要实现
        log.debug("WebSocket通用事件 - 类型: {}, 数据: {}", eventType.getCode(), data);
    }
    
    /**
     * 发送消息到WebSocket
     * 
     * @param executionId 执行ID
     * @param eventType 事件类型
     * @param data 数据
     */
    private void sendToWebSocket(String executionId, StreamEventType eventType, Object data) {
        CopyOnWriteArraySet<WebSocketSession> sessions = sessionMap.get(executionId);
        if (sessions == null || sessions.isEmpty()) {
            log.debug("没有找到执行ID对应的WebSocket会话: {}", executionId);
            return;
        }
        
        // 构造消息
        JSONObject message = new JSONObject();
        message.put("event", eventType.getCode());
        message.put("data", data);
        message.put("timestamp", System.currentTimeMillis());
        
        String messageText = message.toJSONString();
        
        // 发送给所有相关会话
        sessions.removeIf(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(messageText));
                    return false; // 保留会话
                } else {
                    log.warn("WebSocket会话已关闭，移除会话: {}", session.getId());
                    return true; // 移除会话
                }
            } catch (IOException e) {
                log.error("发送WebSocket消息失败 - 会话ID: {}, 错误: {}", session.getId(), e.getMessage());
                return true; // 移除有问题的会话
            }
        });
    }
}
