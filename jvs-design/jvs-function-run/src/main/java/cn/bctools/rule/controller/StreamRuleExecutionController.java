package cn.bctools.rule.controller;

import cn.bctools.common.utils.R;
import cn.bctools.rule.dto.StreamNodeExecutionDto;
import cn.bctools.rule.enums.StreamEventType;
import cn.bctools.rule.listener.StreamExecutionListener;
import cn.bctools.rule.listener.WebSocketStreamExecutionListener;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 流式规则执行控制器
 * 提供Server-Sent Events (SSE) 接口用于实时获取执行进度
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/rule/stream")
@Api(tags = "流式规则执行")
public class StreamRuleExecutionController {
    
    @Autowired(required = false)
    private WebSocketStreamExecutionListener webSocketListener;
    
    /**
     * 存储SSE连接
     */
    private final ConcurrentHashMap<String, SseEmitter> sseEmitters = new ConcurrentHashMap<>();
    
    /**
     * 创建SSE连接用于接收执行进度
     * 
     * @param executionId 执行ID
     * @return SseEmitter
     */
    @GetMapping(value = "/events/{executionId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("创建SSE连接接收执行进度")
    public SseEmitter streamEvents(@PathVariable String executionId) {
        log.info("创建SSE连接 - 执行ID: {}", executionId);
        
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        sseEmitters.put(executionId, emitter);
        
        // 设置连接完成和超时的回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成 - 执行ID: {}", executionId);
            sseEmitters.remove(executionId);
        });
        
        emitter.onTimeout(() -> {
            log.info("SSE连接超时 - 执行ID: {}", executionId);
            sseEmitters.remove(executionId);
        });
        
        emitter.onError((ex) -> {
            log.error("SSE连接错误 - 执行ID: {}, 错误: {}", executionId, ex.getMessage());
            sseEmitters.remove(executionId);
        });
        
        // 发送连接成功消息
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("SSE连接已建立 - 执行ID: " + executionId));
        } catch (IOException e) {
            log.error("发送SSE连接成功消息失败", e);
        }
        
        return emitter;
    }
    
    /**
     * 获取WebSocket连接信息
     * 
     * @param executionId 执行ID
     * @return 连接信息
     */
    @GetMapping("/websocket-info/{executionId}")
    @ApiOperation("获取WebSocket连接信息")
    public R<Object> getWebSocketInfo(@PathVariable String executionId) {
        JSONObject info = new JSONObject();
        info.put("executionId", executionId);
        info.put("websocketUrl", "/ws/rule-execution/" + executionId);
        info.put("sseUrl", "/rule/stream/events/" + executionId);
        
        return R.ok(info);
    }
    
    /**
     * SSE流式执行监听器
     * 将执行进度通过SSE推送给客户端
     */
    @RestController
    public static class SseStreamExecutionListener implements StreamExecutionListener {
        
        private final ConcurrentHashMap<String, SseEmitter> sseEmitters;
        
        public SseStreamExecutionListener(ConcurrentHashMap<String, SseEmitter> sseEmitters) {
            this.sseEmitters = sseEmitters;
        }
        
        @Override
        public void onWorkflowStarted(String executionId, int totalNodes) {
            StreamNodeExecutionDto event = new StreamNodeExecutionDto()
                    .setExecutionId(executionId)
                    .setStatus("started")
                    .setTotalNodes(totalNodes)
                    .setExecutedNodes(0)
                    .setProgress(0);
            
            sendSseEvent(executionId, StreamEventType.WORKFLOW_STARTED, event);
        }
        
        @Override
        public void onNodeStarted(StreamNodeExecutionDto nodeExecution) {
            sendSseEvent(nodeExecution.getExecutionId(), StreamEventType.NODE_STARTED, nodeExecution);
        }
        
        @Override
        public void onNodeRunning(StreamNodeExecutionDto nodeExecution) {
            sendSseEvent(nodeExecution.getExecutionId(), StreamEventType.NODE_RUNNING, nodeExecution);
        }
        
        @Override
        public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
            sendSseEvent(nodeExecution.getExecutionId(), StreamEventType.NODE_COMPLETED, nodeExecution);
        }
        
        @Override
        public void onNodeFailed(StreamNodeExecutionDto nodeExecution) {
            sendSseEvent(nodeExecution.getExecutionId(), StreamEventType.NODE_FAILED, nodeExecution);
        }
        
        @Override
        public void onWorkflowCompleted(String executionId, Object finalResult) {
            sendSseEvent(executionId, StreamEventType.WORKFLOW_COMPLETED, finalResult);
            // 完成后关闭SSE连接
            closeSseConnection(executionId);
        }
        
        @Override
        public void onWorkflowFailed(String executionId, String errorMessage) {
            sendSseEvent(executionId, StreamEventType.WORKFLOW_FAILED, errorMessage);
            // 失败后关闭SSE连接
            closeSseConnection(executionId);
        }
        
        @Override
        public void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
            StreamNodeExecutionDto progressEvent = new StreamNodeExecutionDto()
                    .setExecutionId(executionId)
                    .setProgress(progress)
                    .setExecutedNodes(executedNodes)
                    .setTotalNodes(totalNodes);
            
            sendSseEvent(executionId, StreamEventType.PROGRESS_UPDATE, progressEvent);
        }
        
        @Override
        public void onEvent(StreamEventType eventType, Object data) {
            log.debug("SSE通用事件 - 类型: {}, 数据: {}", eventType.getCode(), data);
        }
        
        /**
         * 发送SSE事件
         */
        private void sendSseEvent(String executionId, StreamEventType eventType, Object data) {
            SseEmitter emitter = sseEmitters.get(executionId);
            if (emitter != null) {
                try {
                    emitter.send(SseEmitter.event()
                            .name(eventType.getCode())
                            .data(JSONObject.toJSONString(data)));
                } catch (IOException e) {
                    log.error("发送SSE事件失败 - 执行ID: {}, 事件: {}, 错误: {}", 
                            executionId, eventType.getCode(), e.getMessage());
                    sseEmitters.remove(executionId);
                }
            }
        }
        
        /**
         * 关闭SSE连接
         */
        private void closeSseConnection(String executionId) {
            SseEmitter emitter = sseEmitters.get(executionId);
            if (emitter != null) {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.error("关闭SSE连接失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
                } finally {
                    sseEmitters.remove(executionId);
                }
            }
        }
    }
}
