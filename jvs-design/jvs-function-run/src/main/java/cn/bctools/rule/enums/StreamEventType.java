package cn.bctools.rule.enums;

/**
 * 流式输出事件类型枚举
 * 
 * <AUTHOR>
 */
public enum StreamEventType {
    
    /**
     * 工作流开始
     */
    WORKFLOW_STARTED("workflow_started", "工作流开始"),
    
    /**
     * 节点开始执行
     */
    NODE_STARTED("node_started", "节点开始执行"),
    
    /**
     * 节点执行中
     */
    NODE_RUNNING("node_running", "节点执行中"),
    
    /**
     * 节点执行完成
     */
    NODE_COMPLETED("node_completed", "节点执行完成"),
    
    /**
     * 节点执行失败
     */
    NODE_FAILED("node_failed", "节点执行失败"),
    
    /**
     * 工作流完成
     */
    WORKFLOW_COMPLETED("workflow_completed", "工作流完成"),
    
    /**
     * 工作流失败
     */
    WORKFLOW_FAILED("workflow_failed", "工作流失败"),
    
    /**
     * 进度更新
     */
    PROGRESS_UPDATE("progress_update", "进度更新"),
    
    /**
     * 错误信息
     */
    ERROR("error", "错误信息");
    
    private final String code;
    private final String description;
    
    StreamEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
}
