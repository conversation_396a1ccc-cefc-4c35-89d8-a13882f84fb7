# 流式逻辑引擎执行系统

## 概述

本系统将原本同步执行的逻辑引擎改造为流式输出模式，仿照Dify工作流的方式，每个组件执行都会实时返回结果，包括组件的入参和输出结果。

## 核心特性

### 🚀 实时流式输出
- 每个节点执行都会实时推送状态和结果
- 支持WebSocket和Server-Sent Events两种推送方式
- 包含节点的输入参数、输出结果、执行耗时等详细信息

### 📊 执行进度监控
- 实时显示工作流执行进度
- 节点级别的状态跟踪（运行中、已完成、失败）
- 详细的错误信息和异常处理

### 🎯 多种输出方式
- **控制台日志**: 默认输出到日志文件
- **WebSocket**: 实时双向通信
- **Server-Sent Events**: 单向实时推送
- **可扩展**: 支持自定义监听器实现

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   RuleRunService │───▶│  RuleStartUtils  │───▶│ RuleDesignUtils │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                    StreamExecutionListener                      │
├─────────────────────────────────────────────────────────────────┤
│  • DefaultStreamExecutionListener (日志输出)                    │
│  • WebSocketStreamExecutionListener (WebSocket推送)            │
│  • SseStreamExecutionListener (SSE推送)                        │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        前端实时显示                              │
├─────────────────────────────────────────────────────────────────┤
│  • 执行进度条                                                   │
│  • 节点状态卡片                                                 │
│  • 实时日志                                                     │
│  • 输入输出详情                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. StreamNodeExecutionDto
流式节点执行结果DTO，包含：
- 执行ID、节点ID、节点名称
- 执行状态、开始/结束时间、耗时
- 输入参数、输出结果
- 错误信息、执行进度

### 2. StreamExecutionListener
流式执行监听器接口，定义了以下事件：
- `onWorkflowStarted`: 工作流开始
- `onNodeStarted`: 节点开始执行
- `onNodeCompleted`: 节点执行完成
- `onNodeFailed`: 节点执行失败
- `onWorkflowCompleted`: 工作流完成
- `onWorkflowFailed`: 工作流失败
- `onProgressUpdate`: 进度更新

### 3. 监听器实现

#### DefaultStreamExecutionListener
- 默认实现，输出到控制台和日志
- 格式化的流式输出，便于调试和监控

#### WebSocketStreamExecutionListener
- 通过WebSocket实时推送执行状态
- 支持多客户端连接
- 自动管理连接生命周期

#### SseStreamExecutionListener
- 通过Server-Sent Events推送状态
- 单向推送，适合简单的状态监控
- 自动处理连接超时和错误

## 使用方式

### 1. 后端集成

#### 启用流式输出
系统会自动检测并使用配置的StreamExecutionListener实现：

```java
// 默认会使用DefaultStreamExecutionListener
// 如果需要WebSocket推送，确保WebSocketStreamExecutionListener被注册为Bean

@Component
public class CustomStreamListener implements StreamExecutionListener {
    // 自定义实现
}
```

#### 执行逻辑引擎
原有的执行方式不变，系统会自动添加流式输出：

```java
// 在RuleRunService中调用
ruleStartUtils.start(po, logPo, ruleExecDto);
```

### 2. 前端集成

#### WebSocket方式
```javascript
const ws = new WebSocket(`ws://localhost:8080/ws/rule-execution/${executionId}`);

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('事件类型:', message.event);
    console.log('数据:', message.data);
};
```

#### Server-Sent Events方式
```javascript
const eventSource = new EventSource(`/rule/stream/events/${executionId}`);

eventSource.addEventListener('node_completed', function(event) {
    const nodeData = JSON.parse(event.data);
    console.log('节点完成:', nodeData);
});
```

### 3. 演示页面
访问 `/stream-execution-demo.html` 查看完整的演示页面，包含：
- 连接管理
- 实时进度显示
- 节点状态卡片
- 详细的输入输出信息

## 事件类型

| 事件类型 | 描述 | 数据结构 |
|---------|------|----------|
| `workflow_started` | 工作流开始 | `{executionId, totalNodes}` |
| `node_started` | 节点开始执行 | `StreamNodeExecutionDto` |
| `node_running` | 节点执行中 | `StreamNodeExecutionDto` |
| `node_completed` | 节点执行完成 | `StreamNodeExecutionDto` |
| `node_failed` | 节点执行失败 | `StreamNodeExecutionDto` |
| `workflow_completed` | 工作流完成 | `finalResult` |
| `workflow_failed` | 工作流失败 | `errorMessage` |
| `progress_update` | 进度更新 | `{progress, executedNodes, totalNodes}` |

## 配置选项

### application.yml
```yaml
# 启用WebSocket支持
spring:
  websocket:
    enabled: true

# 自定义配置
rule:
  stream:
    # 是否启用流式输出
    enabled: true
    # 默认监听器类型: default, websocket, sse
    default-listener: default
    # WebSocket路径
    websocket-path: /ws/rule-execution
    # SSE路径
    sse-path: /rule/stream/events
```

## 扩展开发

### 自定义监听器
```java
@Component
public class CustomStreamListener implements StreamExecutionListener {
    
    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        // 自定义处理逻辑
        // 例如：发送到消息队列、存储到数据库、发送邮件通知等
    }
    
    // 实现其他方法...
}
```

### 消息队列集成
```java
@Component
public class MqStreamListener implements StreamExecutionListener {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        rabbitTemplate.convertAndSend("rule.execution.queue", nodeExecution);
    }
}
```

## 性能考虑

1. **内存管理**: WebSocket连接会占用内存，建议设置合理的超时时间
2. **并发处理**: 大量并发执行时，考虑使用消息队列异步处理
3. **网络带宽**: 频繁的状态推送会消耗网络带宽，可根据需要调整推送频率
4. **存储优化**: 长时间运行的工作流建议将状态持久化到数据库

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查防火墙设置
   - 确认WebSocket配置正确
   - 查看浏览器控制台错误信息

2. **SSE连接超时**
   - 调整超时时间设置
   - 检查网络连接稳定性
   - 确认服务器负载正常

3. **事件丢失**
   - 检查监听器是否正确注册
   - 确认事件处理没有异常
   - 查看服务器日志

### 调试技巧

1. 启用详细日志：
```yaml
logging:
  level:
    cn.bctools.rule: DEBUG
```

2. 使用演示页面测试连接和事件接收

3. 检查Spring Bean注册情况：
```java
@Autowired
private ApplicationContext applicationContext;

// 检查监听器是否注册
StreamExecutionListener listener = applicationContext.getBean(StreamExecutionListener.class);
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的流式输出功能
- 提供WebSocket和SSE两种推送方式
- 包含完整的演示页面

### 后续计划
- [ ] 支持执行暂停和恢复
- [ ] 添加执行历史查询接口
- [ ] 支持自定义事件过滤
- [ ] 集成更多消息队列
- [ ] 性能监控和统计功能
