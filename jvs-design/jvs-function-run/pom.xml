<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-design</artifactId>
        <version>2.2</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-function-run</artifactId>
    <version>2.2</version>

    <dependencies>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-function</artifactId>
        </dependency>

        <!-- 添加流式输出核心支持 -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>edf-stream-core</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>