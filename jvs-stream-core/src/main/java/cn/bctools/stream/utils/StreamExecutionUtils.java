package cn.bctools.stream.utils;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.listener.StreamExecutionListener;
import cn.bctools.common.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 流式执行工具类
 * 提供流式执行的通用工具方法
 * 
 * <AUTHOR>
 */
@Slf4j
public class StreamExecutionUtils {
    
    private StreamExecutionUtils() {
        // 工具类，禁止实例化
    }
    
    /**
     * 获取所有流式执行监听器
     * 
     * @return 监听器列表
     */
    public static List<StreamExecutionListener> getStreamListeners() {
        try {
            return SpringContextUtil.getBeansOfType(StreamExecutionListener.class);
        } catch (Exception e) {
            log.warn("获取流式执行监听器失败: {}", e.getMessage());
            return List.of();
        }
    }
    
    /**
     * 获取主要的流式执行监听器
     * 
     * @return 主监听器，如果没有则返回null
     */
    public static StreamExecutionListener getPrimaryStreamListener() {
        try {
            return SpringContextUtil.getBean(StreamExecutionListener.class);
        } catch (Exception e) {
            log.debug("未找到主要的流式执行监听器: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 通知工作流开始执行
     * 
     * @param executionId 执行ID
     * @param totalNodes 总节点数
     */
    public static void notifyWorkflowStarted(String executionId, int totalNodes) {
        List<StreamExecutionListener> listeners = getStreamListeners();
        for (StreamExecutionListener listener : listeners) {
            try {
                listener.onWorkflowStarted(executionId, totalNodes);
            } catch (Exception e) {
                log.error("通知工作流开始执行失败 - 监听器: {}, 执行ID: {}, 错误: {}", 
                        listener.getClass().getSimpleName(), executionId, e.getMessage());
            }
        }
    }
    
    /**
     * 通知工作流执行完成
     * 
     * @param executionId 执行ID
     * @param result 执行结果
     */
    public static void notifyWorkflowCompleted(String executionId, Object result) {
        List<StreamExecutionListener> listeners = getStreamListeners();
        for (StreamExecutionListener listener : listeners) {
            try {
                listener.onWorkflowCompleted(executionId, result);
            } catch (Exception e) {
                log.error("通知工作流执行完成失败 - 监听器: {}, 执行ID: {}, 错误: {}", 
                        listener.getClass().getSimpleName(), executionId, e.getMessage());
            }
        }
    }
    
    /**
     * 通知工作流执行失败
     * 
     * @param executionId 执行ID
     * @param errorMessage 错误信息
     */
    public static void notifyWorkflowFailed(String executionId, String errorMessage) {
        List<StreamExecutionListener> listeners = getStreamListeners();
        for (StreamExecutionListener listener : listeners) {
            try {
                listener.onWorkflowFailed(executionId, errorMessage);
            } catch (Exception e) {
                log.error("通知工作流执行失败失败 - 监听器: {}, 执行ID: {}, 错误: {}", 
                        listener.getClass().getSimpleName(), executionId, e.getMessage());
            }
        }
    }
    
    /**
     * 生成执行ID
     * 
     * @param prefix 前缀
     * @return 执行ID
     */
    public static String generateExecutionId(String prefix) {
        return prefix + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int)(Math.random() * 0x10000));
    }
    
    /**
     * 生成执行ID（默认前缀）
     * 
     * @return 执行ID
     */
    public static String generateExecutionId() {
        return generateExecutionId(StreamConstants.Patterns.EXECUTION_ID_PREFIX.replace("_", ""));
    }
    
    /**
     * 计算进度百分比
     * 
     * @param executedNodes 已执行节点数
     * @param totalNodes 总节点数
     * @return 进度百分比
     */
    public static int calculateProgress(int executedNodes, int totalNodes) {
        if (totalNodes <= 0) {
            return 0;
        }
        return Math.min(100, (executedNodes * 100) / totalNodes);
    }
    
    /**
     * 检查执行ID是否有效
     * 
     * @param executionId 执行ID
     * @return 是否有效
     */
    public static boolean isValidExecutionId(String executionId) {
        return executionId != null && 
               !executionId.trim().isEmpty() && 
               !StreamConstants.Defaults.UNKNOWN.equals(executionId);
    }
}
