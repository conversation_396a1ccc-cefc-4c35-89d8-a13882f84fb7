package cn.bctools.stream.listener;

import cn.bctools.stream.dto.StreamNodeExecutionDto;

/**
 * 流式执行监听器接口
 * 提供工作流执行过程中的事件回调
 * 
 * <AUTHOR>
 */
public interface StreamExecutionListener {
    
    /**
     * 工作流开始执行
     * 
     * @param executionId 执行ID
     * @param totalNodes 总节点数
     */
    default void onWorkflowStarted(String executionId, int totalNodes) {
        // 默认空实现
    }
    
    /**
     * 节点开始执行
     * 
     * @param nodeExecution 节点执行信息
     */
    default void onNodeStarted(StreamNodeExecutionDto nodeExecution) {
        // 默认空实现
    }
    
    /**
     * 节点执行完成
     * 
     * @param nodeExecution 节点执行信息
     */
    default void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        // 默认空实现
    }
    
    /**
     * 节点执行失败
     * 
     * @param nodeExecution 节点执行信息
     */
    default void onNodeFailed(StreamNodeExecutionDto nodeExecution) {
        // 默认空实现
    }
    
    /**
     * 工作流执行完成
     * 
     * @param executionId 执行ID
     * @param result 执行结果
     */
    default void onWorkflowCompleted(String executionId, Object result) {
        // 默认空实现
    }
    
    /**
     * 工作流执行失败
     * 
     * @param executionId 执行ID
     * @param errorMessage 错误信息
     */
    default void onWorkflowFailed(String executionId, String errorMessage) {
        // 默认空实现
    }
    
    /**
     * 进度更新
     * 
     * @param executionId 执行ID
     * @param progress 进度百分比
     * @param executedNodes 已执行节点数
     * @param totalNodes 总节点数
     */
    default void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
        // 默认空实现
    }
}
