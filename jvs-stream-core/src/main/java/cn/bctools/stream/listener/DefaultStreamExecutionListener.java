package cn.bctools.stream.listener;

import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

/**
 * 默认流式执行监听器
 * 提供基础的日志输出功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnMissingBean(StreamExecutionListener.class)
public class DefaultStreamExecutionListener implements StreamExecutionListener {
    
    @Override
    public void onWorkflowStarted(String executionId, int totalNodes) {
        log.info("{} - 执行ID: {}, 总节点数: {}", 
                StreamConstants.LogMessages.WORKFLOW_STARTED, executionId, totalNodes);
    }
    
    @Override
    public void onNodeStarted(StreamNodeExecutionDto nodeExecution) {
        log.info("{} - 节点: {} ({}), 执行ID: {}", 
                StreamConstants.LogMessages.NODE_STARTED, 
                nodeExecution.getNodeName(), 
                nodeExecution.getFunctionName(),
                nodeExecution.getExecutionId());
    }
    
    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        log.info("{} - 节点: {} ({}), 耗时: {}ms, 执行ID: {}", 
                StreamConstants.LogMessages.NODE_COMPLETED,
                nodeExecution.getNodeName(), 
                nodeExecution.getFunctionName(),
                nodeExecution.getDuration(),
                nodeExecution.getExecutionId());
    }
    
    @Override
    public void onNodeFailed(StreamNodeExecutionDto nodeExecution) {
        log.error("{} - 节点: {} ({}), 错误: {}, 执行ID: {}", 
                StreamConstants.LogMessages.NODE_FAILED,
                nodeExecution.getNodeName(), 
                nodeExecution.getFunctionName(),
                nodeExecution.getErrorMessage(),
                nodeExecution.getExecutionId());
    }
    
    @Override
    public void onWorkflowCompleted(String executionId, Object result) {
        log.info("{} - 执行ID: {}, 结果类型: {}", 
                StreamConstants.LogMessages.WORKFLOW_COMPLETED, 
                executionId, 
                result != null ? result.getClass().getSimpleName() : "null");
    }
    
    @Override
    public void onWorkflowFailed(String executionId, String errorMessage) {
        log.error("{} - 执行ID: {}, 错误: {}", 
                StreamConstants.LogMessages.WORKFLOW_FAILED, 
                executionId, 
                errorMessage);
    }
    
    @Override
    public void onProgressUpdate(String executionId, int progress, int executedNodes, int totalNodes) {
        log.info("📊 进度更新 - 执行ID: {}, 进度: {}% ({}/{})", 
                executionId, progress, executedNodes, totalNodes);
    }
}
