package cn.bctools.stream.constants;

/**
 * 流式输出常量类
 * 统一管理所有字符串常量，避免硬编码
 * 
 * <AUTHOR>
 */
public final class StreamConstants {
    
    private StreamConstants() {
        // 工具类，禁止实例化
    }
    
    /**
     * HTTP头常量
     */
    public static final class Headers {
        public static final String ACCEPT = "Accept";
        public static final String CONTENT_TYPE = "Content-Type";
        public static final String USER_AGENT = "User-Agent";
        public static final String X_FORWARDED_FOR = "X-Forwarded-For";
        public static final String X_REAL_IP = "X-Real-IP";
        public static final String X_STREAM_TYPE = "X-Stream-Type";
        public static final String X_TIMEOUT_SECONDS = "X-Timeout-Seconds";
        public static final String X_ASYNC = "X-Async";
        public static final String X_CALLBACK_URL = "X-Callback-Url";
        public static final String X_EXECUTION_ID = "X-Execution-Id";
        
        private Headers() {}
    }
    
    /**
     * 媒体类型常量
     */
    public static final class MediaTypes {
        public static final String TEXT_EVENT_STREAM = "text/event-stream";
        public static final String APPLICATION_STREAM_JSON = "application/stream+json";
        public static final String APPLICATION_JSON = "application/json";
        public static final String TEXT_PLAIN = "text/plain";
        
        private MediaTypes() {}
    }
    
    /**
     * 流式输出类型代码常量
     */
    public static final class StreamTypeCodes {
        public static final String CONSOLE = "console";
        public static final String WEBSOCKET = "websocket";
        public static final String SSE = "sse";
        public static final String FLUX = "flux";
        public static final String MESSAGE_QUEUE = "mq";
        public static final String DATABASE = "database";
        public static final String FILE = "file";
        public static final String CUSTOM = "custom";
        
        private StreamTypeCodes() {}
    }
    
    /**
     * 事件类型代码常量
     */
    public static final class EventTypeCodes {
        public static final String WORKFLOW_STARTED = "workflow_started";
        public static final String NODE_STARTED = "node_started";
        public static final String NODE_RUNNING = "node_running";
        public static final String NODE_COMPLETED = "node_completed";
        public static final String NODE_FAILED = "node_failed";
        public static final String WORKFLOW_COMPLETED = "workflow_completed";
        public static final String WORKFLOW_FAILED = "workflow_failed";
        public static final String PROGRESS_UPDATE = "progress_update";
        public static final String ERROR = "error";
        public static final String DEBUG = "debug";
        public static final String CUSTOM = "custom";
        public static final String CONNECTED = "connected";
        
        private EventTypeCodes() {}
    }
    
    /**
     * 执行状态常量
     */
    public static final class ExecutionStatus {
        public static final String PENDING = "pending";
        public static final String RUNNING = "running";
        public static final String COMPLETED = "completed";
        public static final String FAILED = "failed";
        public static final String CANCELLED = "cancelled";
        public static final String TIMEOUT = "timeout";
        public static final String STARTED = "started";
        
        private ExecutionStatus() {}
    }
    
    /**
     * 节点状态常量
     */
    public static final class NodeStatus {
        public static final String WAITING = "waiting";
        public static final String RUNNING = "running";
        public static final String COMPLETED = "completed";
        public static final String FAILED = "failed";
        public static final String SKIPPED = "skipped";
        
        private NodeStatus() {}
    }
    
    /**
     * URL路径常量
     */
    public static final class Paths {
        public static final String API_PREFIX = "/api";
        public static final String WORKFLOWS_RUN = "/workflows/run";
        public static final String WORKFLOWS_STREAM = "/workflows/stream";
        public static final String WORKFLOWS_EXECUTION = "/workflows/execution";
        public static final String WEBSOCKET_PREFIX = "/ws";
        public static final String WEBSOCKET_WORKFLOW = "/workflow-execution";
        
        private Paths() {}
    }
    
    /**
     * 属性名常量
     */
    public static final class AttributeNames {
        public static final String TIMEOUT_SECONDS = "timeoutSeconds";
        public static final String CLIENT_IP = "clientIp";
        public static final String USER_AGENT = "userAgent";
        public static final String IS_MOBILE = "isMobile";
        public static final String PREFERRED_OUTPUT_TYPE = "preferredOutputType";
        public static final String CONNECTION_GUIDE = "connectionGuide";
        public static final String PROTOCOL = "protocol";
        public static final String MESSAGE_FORMAT = "messageFormat";
        public static final String EXECUTION_ID = "executionId";
        public static final String APP_ID = "appId";
        
        private AttributeNames() {}
    }
    
    /**
     * 默认值常量
     */
    public static final class Defaults {
        public static final int DEFAULT_TIMEOUT_SECONDS = 300;
        public static final int MAX_TIMEOUT_SECONDS = 3600;
        public static final long DEFAULT_SSE_TIMEOUT_MS = 300000L;
        public static final long DEFAULT_WEBSOCKET_TIMEOUT_MS = 300000L;
        public static final int DEFAULT_FLUX_BUFFER_SIZE = 1000;
        public static final int DEFAULT_FLUX_TIMEOUT_SECONDS = 600;
        public static final String DEFAULT_THREAD_NAME_PREFIX = "stream-execution-";
        public static final String DEFAULT_CLEANUP_THREAD_NAME = "stream-cleanup";
        public static final String UNKNOWN = "unknown";
        
        private Defaults() {}
    }
    
    /**
     * 消息常量
     */
    public static final class Messages {
        public static final String CONNECTION_ESTABLISHED = "连接已建立";
        public static final String CONNECTION_CLOSED = "连接已关闭";
        public static final String CONNECTION_TIMEOUT = "连接超时";
        public static final String CONNECTION_ERROR = "连接错误";
        public static final String EXECUTION_COMPLETED = "执行完成，无返回结果";
        public static final String EXECUTION_FAILED = "执行失败";
        public static final String WEBSOCKET_CONNECTION_GUIDE = "请使用WebSocket客户端连接到指定URL";
        public static final String WEBSOCKET_ENDPOINT_MESSAGE = "请通过WebSocket端点连接";
        public static final String CONTEXT_NOT_FOUND = "执行上下文不存在";
        public static final String EXECUTION_CANCELLED = "执行已取消";
        public static final String INVALID_STREAM_TYPE = "无效的流式输出类型";
        public static final String INVALID_APP_TYPE = "该应用不是工作流应用";
        
        private Messages() {}
    }
    
    /**
     * 日志消息常量
     */
    public static final class LogMessages {
        public static final String WORKFLOW_STARTED = "🚀 工作流开始执行";
        public static final String WORKFLOW_COMPLETED = "🎉 工作流执行完成";
        public static final String WORKFLOW_FAILED = "💥 工作流执行失败";
        public static final String NODE_STARTED = "▶️ 节点开始执行";
        public static final String NODE_COMPLETED = "✅ 节点执行完成";
        public static final String NODE_FAILED = "❌ 节点执行失败";
        public static final String CONNECTION_ESTABLISHED_LOG = "🔗 连接已建立";
        public static final String CONNECTION_CLOSED_LOG = "🚫 连接已关闭";
        public static final String PROCESSOR_SELECTED = "🎯 选择处理器";
        public static final String CONTEXT_PREPARED = "🎯 执行上下文准备完成";
        public static final String ASYNC_EXECUTION_STARTED = "🚀 开始异步执行工作流";
        public static final String CLEANUP_COMPLETED = "🧹 清理完成";
        
        private LogMessages() {}
    }
    
    /**
     * 正则表达式常量
     */
    public static final class Patterns {
        public static final String MOBILE_USER_AGENT = "(?i).*(mobile|android|iphone|ipad).*";
        public static final String API_PATH = "^/api/.*";
        public static final String WEBSOCKET_PATH = "^/ws/.*";
        public static final String EXECUTION_ID_PREFIX = "exec_";
        public static final String SYNC_ID_PREFIX = "sync_";
        
        private Patterns() {}
    }
}
