package cn.bctools.stream.enums;

import cn.bctools.stream.constants.StreamConstants;

/**
 * 流式输出类型枚举
 * 
 * <AUTHOR>
 */
public enum StreamOutputType {
    
    /**
     * 控制台日志输出
     */
    CONSOLE(StreamConstants.StreamTypeCodes.CONSOLE, "控制台日志"),
    
    /**
     * WebSocket实时推送
     */
    WEBSOCKET(StreamConstants.StreamTypeCodes.WEBSOCKET, "WebSocket推送"),
    
    /**
     * Server-Sent Events推送
     */
    SSE(StreamConstants.StreamTypeCodes.SSE, "SSE推送"),
    
    /**
     * Reactive Flux流式输出
     */
    FLUX(StreamConstants.StreamTypeCodes.FLUX, "Flux流式输出"),
    
    /**
     * 消息队列推送
     */
    MESSAGE_QUEUE(StreamConstants.StreamTypeCodes.MESSAGE_QUEUE, "消息队列推送"),
    
    /**
     * 数据库存储
     */
    DATABASE(StreamConstants.StreamTypeCodes.DATABASE, "数据库存储"),
    
    /**
     * 文件输出
     */
    FILE(StreamConstants.StreamTypeCodes.FILE, "文件输出"),
    
    /**
     * 自定义输出
     */
    CUSTOM(StreamConstants.StreamTypeCodes.CUSTOM, "自定义输出");
    
    private final String code;
    private final String description;
    
    StreamOutputType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 对应的枚举
     */
    public static StreamOutputType fromCode(String code) {
        if (code == null) {
            return CONSOLE;
        }
        
        for (StreamOutputType type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("无效的流式输出类型代码: " + code);
    }
    
    /**
     * 检查是否为流式输出类型
     * 
     * @return 是否为流式输出
     */
    public boolean isStreaming() {
        return this != CONSOLE;
    }
    
    /**
     * 检查是否为实时输出类型
     * 
     * @return 是否为实时输出
     */
    public boolean isRealtime() {
        return this == WEBSOCKET || this == SSE || this == FLUX;
    }
}
