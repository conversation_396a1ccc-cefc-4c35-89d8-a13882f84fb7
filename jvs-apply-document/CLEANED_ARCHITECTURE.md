# 🧹 清理后的简洁架构

## **问题识别与解决**

您完全正确！`StreamExecutionListenerFactory` 确实是历史残留，存在以下问题：

### **❌ 原有问题**
1. **过度设计** - 复杂的工厂模式，但实际只需要一个监听器
2. **功能重复** - 与 `StreamExecutionUtils` 功能重叠
3. **未被使用** - 在实际流程中没有被调用
4. **增加复杂度** - 不必要的抽象层

### **✅ 清理后的架构**

## **简化后的核心类**

### **edf-stream-core 模块**

#### **1. StreamConstants** ⭐
```java
// 统一常量管理，避免硬编码
public static final class EventTypeCodes {
    public static final String WORKFLOW_STARTED = "workflow_started";
    public static final String NODE_COMPLETED = "node_completed";
    // ...
}
```

#### **2. StreamNodeExecutionDto** ⭐
```java
// 节点执行信息传输对象
@Data
public class StreamNodeExecutionDto {
    private String executionId;
    private String nodeId;
    private String status;
    // ...
}
```

#### **3. StreamExecutionListener** ⭐
```java
// 简单的监听器接口
public interface StreamExecutionListener {
    void onWorkflowStarted(String executionId, int totalNodes);
    void onNodeCompleted(StreamNodeExecutionDto nodeExecution);
    // ...
}
```

#### **4. DefaultStreamExecutionListener** ⭐
```java
// 默认监听器实现 - 同时输出日志和流式数据
@Component
public class DefaultStreamExecutionListener implements StreamExecutionListener {
    @Autowired
    private StreamOutputManager streamOutputManager;
    
    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        // 1. 输出日志
        log.info("节点完成: {}", nodeExecution.getNodeName());
        
        // 2. 发送到流式输出
        if (streamOutputManager != null) {
            streamOutputManager.sendNodeCompleted(nodeExecution);
        }
    }
}
```

#### **5. StreamOutputManager** ⭐⭐⭐ (核心类)
```java
// 真正的流式输出管理器
@Component
public class StreamOutputManager {
    private final Map<String, SseEmitter> sseEmitters = new ConcurrentHashMap<>();
    private final Map<String, FluxSink<Object>> fluxSinks = new ConcurrentHashMap<>();
    
    // 创建SSE连接
    public SseEmitter createSseConnection(String executionId, long timeoutMs) { ... }
    
    // 发送数据到所有连接
    public void sendNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        sendEvent(nodeExecution.getExecutionId(), "node_completed", nodeExecution);
    }
}
```

#### **6. StreamExecutionUtils** ⭐
```java
// 简化的工具类
public class StreamExecutionUtils {
    // 获取主监听器
    public static StreamExecutionListener getPrimaryStreamListener() {
        return SpringContextUtil.getBean(StreamExecutionListener.class);
    }
    
    // 便捷通知方法
    public static void notifyWorkflowStarted(String executionId, int totalNodes) {
        StreamExecutionListener listener = getPrimaryStreamListener();
        if (listener != null) {
            listener.onWorkflowStarted(executionId, totalNodes);
        }
    }
}
```

### **document-mgr 模块**

#### **7. StreamWorkflowController** ⭐⭐
```java
// HTTP端点实现
@RestController
public class StreamWorkflowController {
    @Autowired
    private StreamOutputManager streamOutputManager;
    
    @PostMapping("/api/stream/workflows/sse")
    public SseEmitter executeWorkflowWithSSE(@RequestBody Map<String, Object> paramMap) {
        String executionId = StreamExecutionUtils.generateExecutionId();
        
        // 创建SSE连接
        SseEmitter emitter = streamOutputManager.createSseConnection(executionId, 300000L);
        
        // 异步执行工作流
        executeWorkflowAsync(executionId, paramMap);
        
        return emitter;
    }
}
```

#### **8. WorkflowWebSocketHandler** ⭐
```java
// WebSocket处理器
@Component
public class WorkflowWebSocketHandler implements WebSocketHandler {
    // 处理WebSocket连接和消息
    public void sendToExecution(String executionId, String eventType, Object data) { ... }
}
```

## **完整数据流**

```
1. 客户端请求
   ↓
2. StreamWorkflowController (创建连接)
   ↓
3. StreamOutputManager.createSseConnection() (建立SSE/Flux连接)
   ↓
4. AppGenerateService.generate() (异步执行工作流)
   ↓
5. RuleDesignUtils (节点执行时调用)
   ↓
6. StreamExecutionUtils.notifyNodeCompleted()
   ↓
7. DefaultStreamExecutionListener.onNodeCompleted()
   ↓
8. StreamOutputManager.sendNodeCompleted()
   ↓
9. 数据推送到 SSE/Flux/WebSocket
   ↓
10. 客户端接收实时数据
```

## **关键简化点**

### **❌ 删除的冗余类**
- `StreamExecutionListenerFactory` - 过度设计的工厂
- `StrategyBasedStreamExecutionListener` - 复杂的策略监听器
- 各种不必要的策略接口和实现

### **✅ 保留的核心类**
- **6个核心类** 解决所有问题
- **每个类职责单一** 没有冗余方法
- **真正的流式输出** 数据实时推送

## **使用示例**

### **工作流执行中的调用**
```java
// 在 RuleDesignUtils 中
StreamExecutionListener listener = StreamExecutionUtils.getPrimaryStreamListener();

// 节点开始
listener.onNodeStarted(nodeExecution);

// 节点完成  
nodeExecution.setCompleted();
listener.onNodeCompleted(nodeExecution);
```

### **客户端接收数据**
```javascript
// SSE方式
const eventSource = new EventSource('/api/stream/workflows/sse');
eventSource.addEventListener('node_completed', (event) => {
    const data = JSON.parse(event.data);
    console.log('节点完成:', data.data.nodeName);
});

// WebSocket方式
const ws = new WebSocket('ws://localhost:8080/ws/workflow-execution');
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.event === 'node_completed') {
        console.log('节点完成:', data.data.nodeName);
    }
};
```

## **架构优势**

1. **简洁明了** - 只有必要的类，没有过度设计
2. **职责清晰** - 每个类都有明确的作用
3. **真实可用** - 真正实现了流式数据输出
4. **易于维护** - 代码简单，逻辑清晰
5. **性能良好** - 没有不必要的抽象层

## **总结**

清理后的架构：
- **删除了** 3个冗余类
- **保留了** 8个核心类
- **实现了** 真正的流式输出
- **解决了** 所有实际问题

这就是一个**简洁、实用、高效**的流式输出架构！
