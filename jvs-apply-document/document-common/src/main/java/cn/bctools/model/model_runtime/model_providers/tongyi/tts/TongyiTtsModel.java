package cn.bctools.model.model_runtime.model_providers.tongyi.tts;

import cn.bctools.model.entity.dto.EncryptedConfig;
import cn.bctools.model.model_runtime.model_providers.__base.TtsModel;
import dev.langchain4j.community.model.dashscope.QwenTtsModel;

public class TongyiTtsModel extends TtsModel {

    @Override
    public void _invoke(String modelName, String content, EncryptedConfig encryptedConfig) {

        String apiKey = encryptedConfig.getDashscope_api_key();
        QwenTtsModel qwenTtsModel = new QwenTtsModel(apiKey, modelName);

        try {
            qwenTtsModel.sambert(content);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }


}
