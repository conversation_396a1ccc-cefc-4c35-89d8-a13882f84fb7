package cn.bctools.model.model_runtime.model_providers;

import cn.bctools.model.entity.dto.EncryptedConfig;
import cn.bctools.model.model_runtime.entities.ProviderEntity;
import lombok.Getter;

import java.util.Map;

@Getter
public class ModelProviderExtension {

    private final String name;
    private final ModelProvider provider;
    private final ProviderEntity providerEntity;
    private final Integer position;

    public ModelProviderExtension(String name, ModelProvider provider, ProviderEntity providerEntity, Integer position) {
        this.name = name;
        this.provider = provider;
        this.providerEntity = providerEntity;
        this.position = position == null ? Integer.MAX_VALUE : position;
    }

}
