<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.model.mapper.ProviderModelsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.model.entity.data.ProviderModels">
        <id column="id" property="id" />
        <result column="provider_name" property="providerName" />
        <result column="model_name" property="modelName" />
        <result column="model_type" property="modelType" />
        <result column="encrypted_config" property="encryptedConfig" />
        <result column="is_valid" property="isValid" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by_id" property="createById" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, provider_name, model_name, model_type, encrypted_config, is_valid, create_time, update_time, create_by_id, create_by, update_by, tenant_id, del_flag
    </sql>

</mapper>
