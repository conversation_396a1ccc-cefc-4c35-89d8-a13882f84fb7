package cn.bctools.model.model_runtime.entities;

import java.util.List;
import java.util.Map;

public class AIModelEntity {
    private String model;
    private String label;
    private List<Map<String, Object>> parameterRules;
    private PriceConfig pricing;
    private String fetchFrom;

    public AIModelEntity(Map<String, Object> data) {
        this.model = (String) data.get("model");
        this.label = (String) ((Map<?, ?>) data.get("label")).get("en_US");
        this.parameterRules = (List<Map<String, Object>>) data.get("parameter_rules");
        this.pricing = new PriceConfig((Map<String, Object>) data.get("pricing"));
        this.fetchFrom = (String) data.get("fetch_from");
    }

    public String getModel() { return model; }
    public List<Map<String, Object>> getParameterRules() { return parameterRules; }
    public PriceConfig getPricing() { return pricing; }

}
