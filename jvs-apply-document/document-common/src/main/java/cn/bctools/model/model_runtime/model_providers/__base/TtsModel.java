package cn.bctools.model.model_runtime.model_providers.__base;


import cn.bctools.model.entity.dto.EncryptedConfig;
import cn.bctools.model.entity.vo.VoiceVO;
import cn.bctools.model.model_runtime.entities.ModelConfig;
import cn.bctools.model.model_runtime.entities.Voices;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class TtsModel extends AIModel {

    public List<VoiceVO> getModelVoices(String model, String language, EncryptedConfig encryptedConfig) {
        ModelConfig modelConfig = getModelConfig(model, encryptedConfig);
        if (modelConfig == null) {
            throw new RuntimeException("Model not found");
        }
        List<VoiceVO> voiceVOS = new ArrayList<>();
        if (modelConfig.getModel_properties() != null) {
            List<Voices> voices = modelConfig.getModel_properties().getVoices();
            if (voices != null && !voices.isEmpty()) {
                for (Voices voice : voices) {
                    if (voice.getLanguage() != null && !voice.getLanguage().contains(language)) {
                        continue;
                    }
                    VoiceVO voiceVO = new VoiceVO();
                    voiceVO.setName(voice.getName());
                    voiceVO.setValue(voice.getMode());
                    voiceVOS.add(voiceVO);
                }
             }
        }

        return voiceVOS;
    }


    public void invoke(String model, String voice, String content, EncryptedConfig encryptedConfig) {
        ModelConfig modelConfig = getModelConfig(model, encryptedConfig);
        if (modelConfig == null) {
            throw new RuntimeException("Model not found");
        }
        if (modelConfig.getModel_properties() != null && StringUtils.isEmpty(voice)) {
            List<Voices> voices = modelConfig.getModel_properties().getVoices();
            if (voices != null && !voices.isEmpty()) {
                voice = voices.get(0).getMode();
            }
        }

        _invoke(voice, content, encryptedConfig);
    }

    public void _invoke(String voice, String content, EncryptedConfig encryptedConfig) {

    }


}
