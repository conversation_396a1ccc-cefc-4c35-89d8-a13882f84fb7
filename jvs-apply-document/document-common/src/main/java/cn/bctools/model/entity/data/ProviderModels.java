package cn.bctools.model.entity.data;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 模型供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("provider_models")
@ApiModel(value="ProviderModels对象", description="模型供应商")
public class ProviderModels implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "供应商名称")
    @TableField("provider_name")
    private String providerName;

    @ApiModelProperty(value = "模型名称")
    @TableField("model_name")
    private String modelName;

    @ApiModelProperty(value = "模型类型")
    @TableField("model_type")
    private String modelType;

    @ApiModelProperty(value = "加密后的配置")
    @TableField("encrypted_config")
    private String encryptedConfig;

    @ApiModelProperty(value = "是否有效")
    @TableField("is_valid")
    private Boolean isValid;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @ApiModelProperty(value = "创建人名称")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人名称")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "删除标志")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;


}