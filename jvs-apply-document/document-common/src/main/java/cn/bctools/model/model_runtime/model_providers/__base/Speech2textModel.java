package cn.bctools.model.model_runtime.model_providers.__base;

import cn.bctools.model.entity.dto.EncryptedConfig;


public class Speech2textModel extends AIModel {

    public String defaultAudioFileUrl = "http://edf-oss.mtu.plus/jvs-public/ten_1/2_2/jvs-auth-mgr/audio/2025/06/18/2025-06-181935181699282960384-audio.mp3";

    public String invoke(String modelName, String fileLink, EncryptedConfig encryptedConfig) {
        return _invoke(modelName, fileLink, encryptedConfig);
    }

    public String _invoke(String modelName, String fileLink, EncryptedConfig encryptedConfig) {
        return null;
    }




}
