package cn.bctools.model.model_runtime.model_providers;

import cn.bctools.model.model_runtime.entities.ProviderEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Component
@RequiredArgsConstructor
public class ModelProviderFactory implements ApplicationRunner {

    private static final Set<String> ALLOW_PROVIDERS = Set.of(
            "openai", "ollama", "tongyi", "wenxin"
    );

    private static final Logger log = LoggerFactory.getLogger(ModelProviderFactory.class);

    private static Map<String, ModelProviderExtension> modelProviderExtensions;

    public static Map<String, ModelProviderExtension> getLoadedModelProviders() {
        return modelProviderExtensions;
    }


    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始异步初始化模型供应商...");
        CompletableFuture.runAsync(() -> {
            try {
                modelProviderExtensions = getModelProviderMap();
                log.info("模型供应商初始化完成");
            } catch (Exception e) {
                // 处理异常或抛出
                log.error("模型供应商初始化失败", e);
            }
        });
    }

    public Map<String, ModelProviderExtension> getModelProviderMap() throws IOException {
        if (modelProviderExtensions != null) {
            return modelProviderExtensions;
        }

        log.info("开始加载模型供应商...");

        String path = "model_providers";

        ClassLoader classLoader = this.getClass().getClassLoader();

        log.info("模型供应商基础目录存在，准备加载允许的供应商: {}", ALLOW_PROVIDERS);

        Map<String, Integer> positionMap = new HashMap<>();

        List<ModelProviderExtension> modelProviders = new ArrayList<>();

        ALLOW_PROVIDERS.forEach(providerName -> {
            // 构建provider配置文件路径
            String configFilePath = path + "/" + providerName + "/" + providerName + ".yaml";

            ProviderEntity entity;
            Yaml yaml = new Yaml();
            try (InputStream in = classLoader.getResourceAsStream(configFilePath)) {
                if (Objects.isNull(in)) {
                    log.error("模型供应商: {},配置文件: {} 不存在", providerName, configFilePath);
                }
                entity = yaml.loadAs(in, ProviderEntity.class);
                log.info("加载模型供应商: {}", providerName);
                if (entity == null) {
                    log.warn("无法加载 {}.yaml 文件，在路径 {}, 跳过.", providerName, configFilePath);
                    return;
                }

                // 动态构建类名并加载类
                // 首字母大写
                String providerNameUpper = providerName.substring(0, 1).toUpperCase() + providerName.substring(1);
                String className = "cn.bctools.model.model_runtime.model_providers." + providerName + "." + providerNameUpper + "Provider";
                Class<? extends ModelProvider> providerClass = null;
                try {
                    @SuppressWarnings("unchecked")
                    Class<? extends ModelProvider> clazz = (Class<? extends ModelProvider>) Class.forName(className);
                    providerClass = clazz;
                } catch (ClassNotFoundException e) {
                    log.error("无法加载供应商类: {}", className, e);
                    return;
                }

                Integer position = positionMap.getOrDefault(providerName, 0);

                try {
                    ModelProvider provider = providerClass.getDeclaredConstructor().newInstance();
                    modelProviders.add(new ModelProviderExtension(providerName, provider, entity, position));
                } catch (InstantiationException | IllegalAccessException e) {
                    log.error("Failed to instantiate provider: {}", providerName, e);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } catch (IOException e) {
                // 处理异常或抛出
                log.error("Failed to load model provider: {}", providerName, e);
            }

        });

        // 构建最终 map
        modelProviderExtensions = new LinkedHashMap<>();
        modelProviders.sort(Comparator.comparingInt(ModelProviderExtension::getPosition));
        for (ModelProviderExtension ext : modelProviders) {
            modelProviderExtensions.put(ext.getName(), ext);
        }

        log.info("模型供应商加载完成，共加载 {} 个供应商: {}",
                modelProviderExtensions.size(),
                String.join(", ", modelProviderExtensions.keySet()));

        return modelProviderExtensions;
    }

    private Map<String, Integer> getProviderPositionMap(File providersDir) {
        File positionFile = new File(providersDir, "position.yaml");
        Map<String, Integer> positionMap = new LinkedHashMap<>();

        if (positionFile.exists()) {
            Yaml yaml = new Yaml();
            try (InputStream in = new FileInputStream(positionFile)) {
                List<String> order = yaml.load(in);
                for (int i = 0; i < order.size(); i++) {
                    positionMap.put(order.get(i), i);
                }
            } catch (Exception e) {
                log.error("Failed to load position.yaml", e);
            }
        }

        return positionMap;
    }

}