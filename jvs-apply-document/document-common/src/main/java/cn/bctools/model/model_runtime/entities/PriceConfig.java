package cn.bctools.model.model_runtime.entities;

import java.math.BigDecimal;
import java.util.Map;

public class PriceConfig {
    private BigDecimal input;
    private BigDecimal output;
    private BigDecimal unit;
    private String currency;

    public PriceConfig(Map<String, Object> data) {
        this.input = new BigDecimal(data.get("input").toString());
        this.output = data.get("output") != null ? new BigDecimal(data.get("output").toString()) : null;
        this.unit = new BigDecimal(data.get("unit").toString());
        this.currency = (String) data.get("currency");
    }

    public BigDecimal getInput() { return input; }
    public BigDecimal getOutput() { return output; }
    public BigDecimal getUnit() { return unit; }
    public String getCurrency() { return currency; }
}
