package cn.bctools.model.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;

/**
 * <p>
 * 默认模型配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/default-models")
@Api(tags = "默认模型配置")
public class TenantDefaultModelsController {

}
