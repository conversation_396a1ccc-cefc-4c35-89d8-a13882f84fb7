package cn.bctools.model.model_runtime.entities;

import lombok.Data;

import java.util.List;

@Data
public class ProviderEntity {
    private String provider;
    private Label description;
    private Label label;
    private Label icon_small;
    private Label icon_large;
    private String background;
    private HelpInfo help;
    private List<String> supported_model_types;
    private List<String> configurate_methods;
    private CredentialSchema provider_credential_schema;
    private ModelCredentialSchema model_credential_schema;
}
