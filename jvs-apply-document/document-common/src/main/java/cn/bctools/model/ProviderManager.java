package cn.bctools.model;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.model.entity.ProviderCredentialsCacheType;
import cn.bctools.model.entity.RedisKeyConstants;
import cn.bctools.model.entity.data.Encrypted;
import cn.bctools.model.entity.data.ProviderModels;
import cn.bctools.model.entity.data.TenantDefaultModels;
import cn.bctools.model.entity.dto.*;
import cn.bctools.model.entity.vo.*;
import cn.bctools.model.model_runtime.entities.ModelConfig;
import cn.bctools.model.model_runtime.entities.ModelProperties;
import cn.bctools.model.model_runtime.entities.ProviderEntity;
import cn.bctools.model.model_runtime.entities.ProviderType;
import cn.bctools.model.model_runtime.enums.ModelType;
import cn.bctools.model.model_runtime.model_providers.ModelProvider;
import cn.bctools.model.model_runtime.model_providers.ModelProviderExtension;
import cn.bctools.model.model_runtime.model_providers.ModelProviderFactory;
import cn.bctools.model.model_runtime.model_providers.__base.AIModel;
import cn.bctools.model.service.EncryptedService;
import cn.bctools.model.service.ProviderModelsService;
import cn.bctools.model.service.ProvidersService;
import cn.bctools.model.service.TenantDefaultModelsService;
import cn.bctools.model.utils.RsaUtil;
import cn.bctools.redis.utils.RedisUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.bctools.model.entity.data.Providers;

import java.io.IOException;
import java.util.*;

import java.util.stream.Collectors;

@Service
public class ProviderManager {

    @Autowired
    ModelProviderFactory modelProviderFactory;

    @Autowired
    ProvidersService providersService;

    @Autowired
    ProviderModelsService providerModelsService;

    @Autowired
    EncryptedService encryptedService;

    @Autowired
    TenantDefaultModelsService tenantDefaultModelsService;

    @Autowired
    RedisUtils redisUtils;

    private final String HIDDEN_VALUE = "[__HIDDEN__]";


    public List<ProviderList> getConfigurations() {

        // Step 1: 获取所有提供商记录
        List<Providers> providers = providersService.lambdaQuery()
                .eq(Providers::getIsValid, true)
                .eq(Providers::getDelFlag, false)
                .list();

        Map<String, List<Providers>> providerMap = providers.stream()
                .filter(p -> {
                    if (p.getProviderType().equals("system")) {
                        return false;
                    }
                    if (p.getEncryptedConfig().isEmpty()) {
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.groupingBy(Providers::getProviderName));


        // Get all provider model records of the workspace
        List<ProviderModels> providerModels = providerModelsService.list();
        Map<String, List<ProviderModels>> providerModelMap = providerModels.stream()
                .collect(Collectors.groupingBy(ProviderModels::getProviderName));


        List<ProviderList> providerLists = new ArrayList<>();

        try {
            Map<String, ModelProviderExtension> modelProviderExtensions = modelProviderFactory.getModelProviderMap();
            modelProviderExtensions.forEach((name, provider) -> {

                // todo 过滤模型； dify的provider_manager line 125

                // 获取配置信息
                List<Providers> providerRecord = providerMap.getOrDefault(name, new ArrayList<>());
                List<ProviderModels> providerModerRecords = providerModelMap.getOrDefault(name, new ArrayList<>());

                CustomConfiguration customConfiguration = toCustomConfiguration(
                        "1",
                        provider.getProviderEntity(),
                        providerRecord,
                        providerModerRecords
                );

                ProviderList ProviderList = BeanCopyUtil.copy(provider.getProviderEntity(), ProviderList.class);

                String status = "no-configure";
                if (customConfiguration.getProvider() != null || !customConfiguration.getModels().isEmpty()) {
                    status = "active";
                }
                CustomConfigurationVO configurationVO = new CustomConfigurationVO();
                configurationVO.setStatus(status);
                ProviderList.setCustom_configuration(configurationVO);
                providerLists.add(ProviderList);

                // todo 先写死系统配置，后面再看 _to_system_configuration
                SystemConfigurationVO systemConfigurationVO = new SystemConfigurationVO();
                systemConfigurationVO.setEnabled(false);
                systemConfigurationVO.setQuota_configurations(new ArrayList<>());
                ProviderList.setSystem_configuration(systemConfigurationVO);

            });

            return providerLists;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public List<ModelConfig> getProviderModels(String providerName, String modelType) {

        ModelProviderExtension provider = ModelUtil.getModelProvider(providerName);

        ProviderEntity providerEntity = provider.getProviderEntity();

        List<String> supported_model_types = new ArrayList<>();
        if (modelType == null) {
            supported_model_types = providerEntity.getSupported_model_types();
        } else {
            if (providerEntity.getSupported_model_types().contains(modelType)) {
                supported_model_types.add(modelType);
            }
        }

        // todo 处理 model_setting


        List<ModelConfig> modelConfigs = new ArrayList<>();
        for (String supported_model_type : supported_model_types) {
            List<ModelConfig> modelConfigs1 = _getProviderModels(providerName, supported_model_type);
            modelConfigs.addAll(modelConfigs1);
        }

        List<ProviderModels> providerModels = providerModelsService.lambdaQuery()
                .eq(ProviderModels::getProviderName, providerName)
                .eq(modelType != null, ProviderModels::getModelType, modelType)
                .eq(ProviderModels::getIsValid, true)
                .eq(ProviderModels::getDelFlag, false)
                .list();

        String modelBaseKey = String.format(RedisKeyConstants.MODEL_CONFIGURATION_KEY_PREFIX, 1, providerEntity.getProvider());

        for (ProviderModels providerModelRecord : providerModels) {
            String modelKey = modelBaseKey + providerModelRecord.getModelName();
            // 尝试读取缓存中有没有
            CustomModelConfiguration customModelConfiguration = (CustomModelConfiguration) redisUtils.get(modelKey);
            if (customModelConfiguration == null) {
                customModelConfiguration = updateCustomModelConfigurations(providerEntity.getProvider(), providerModelRecord, providerEntity);
            }

            ModelConfig modelConfig = new ModelConfig();
            modelConfig.setModel(customModelConfiguration.getModel());
            modelConfig.setLabel(providerEntity.getLabel());
            modelConfig.setModel_type(providerModelRecord.getModelType());
            ModelProperties model_properties = new ModelProperties();
            model_properties.setMode(customModelConfiguration.getCredentials().getMode());
            model_properties.setContext_size(Integer.parseInt(customModelConfiguration.getCredentials().getContext_size()));
            modelConfig.setModel_properties(model_properties);
            modelConfig.setFetch_from("customizable-model");
            modelConfig.setDeprecated(false);
            modelConfigs.add(modelConfig);
        }

        return modelConfigs;

    }

    public List<ModelConfig> _getProviderModels(String providerName, String modelType) {

        try {
            AIModel model = ModelUtil.getModel(providerName, modelType);
            return model.predefinedModel();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public CustomConfiguration toCustomConfiguration(String tenantId,
            ProviderEntity providerEntity,
            List<Providers> providerRecords,
            List<ProviderModels> providerModelRecords) {


        String providerKey = String.format(RedisKeyConstants.PROVIDER_CONFIGURATION_KEY, "1", providerEntity.getProvider());

        CustomProviderConfiguration customProviderConfiguration = (CustomProviderConfiguration) redisUtils.get(providerKey);
        if (customProviderConfiguration == null) {
            customProviderConfiguration = updateProviderConfiguration(providerEntity.getProvider(), providerRecords, providerEntity);
        }

        String modelBaseKey = String.format(RedisKeyConstants.MODEL_CONFIGURATION_KEY_PREFIX, 1, providerEntity.getProvider());

        List<CustomModelConfiguration> customModelConfigurations = new ArrayList<>();

        for (ProviderModels providerModelRecord : providerModelRecords) {
            String modelKey = modelBaseKey + providerModelRecord.getModelName();
            // 尝试读取缓存中有没有
            CustomModelConfiguration customModelConfiguration = (CustomModelConfiguration) redisUtils.get(modelKey);
            if (customModelConfiguration == null) {
                customModelConfiguration = updateCustomModelConfigurations(providerEntity.getProvider(), providerModelRecord, providerEntity);
            }
            customModelConfigurations.add(customModelConfiguration);
        }

        return new CustomConfiguration(customProviderConfiguration, customModelConfigurations);
    }

    public EncryptedConfig encryptConfiguration(List<String> providerCredentialSecretVariables, EncryptedConfig encryptedConfig) {

        Encrypted encrypted = encryptedService.lambdaQuery()
                .eq(Encrypted::getTargetType, "providers")
                .one();
        if (encrypted == null) {
            throw new RuntimeException("加密信息不存在");
        }
        // 初始化加密信息
        RsaUtil rsaUtil = new RsaUtil(encrypted.getPublicKey(), encrypted.getPrivateKey());

        Map<String, Object> map = BeanCopyUtil.beanToMap(encryptedConfig);
        map.forEach((key, value) -> {
            if (providerCredentialSecretVariables.contains(key)) {
                String encryptedValue = rsaUtil.encrypt(value.toString());
                map.put(key, encryptedValue);
            }
        });

        return BeanCopyUtil.copy(map, EncryptedConfig.class);
    }

    public void decryptConfiguration(List<String> providerCredentialSecretVariables, EncryptedConfig encryptedConfig) {

        Encrypted encrypted = encryptedService.lambdaQuery()
                .eq(Encrypted::getTargetType, "providers")
                .one();
        if (encrypted == null) {
            throw new RuntimeException("加密信息不存在");
        }
        // 初始化加密信息
        RsaUtil rsaUtil = new RsaUtil(encrypted.getPublicKey(), encrypted.getPrivateKey());

        for (String variable : providerCredentialSecretVariables) {
            String decryptedValue = null;
            switch (variable) {
                case "api_key":
                    decryptedValue = rsaUtil.decrypt(encryptedConfig.getApi_key());
                    encryptedConfig.setApi_key(decryptedValue);
                    break;
                case "secret_key":
                    decryptedValue = rsaUtil.decrypt(encryptedConfig.getSecret_key());
                    encryptedConfig.setSecret_key(decryptedValue);
                    break;
                case "dashscope_api_key":
                    decryptedValue = rsaUtil.decrypt(encryptedConfig.getDashscope_api_key());
                    encryptedConfig.setDashscope_api_key(decryptedValue);
                    break;
                default:
                    break;
            }
        }
    }

    // 更新提供者配置
    private CustomProviderConfiguration updateProviderConfiguration(String providerName, List<Providers> providerRecords, ProviderEntity providerEntity) {

        String providerKey = String.format(RedisKeyConstants.PROVIDER_CONFIGURATION_KEY, "1", providerName);

        // 2. 查找自定义提供者记录
        Providers customProviderRecord = null;
        for (Providers providerRecord : providerRecords) {
            if (providerRecord.getProviderType().equals("system")) {
                continue;
            }
            if (providerRecord.getEncryptedConfig() == null || providerRecord.getEncryptedConfig().isEmpty()) {
                continue;
            }
            customProviderRecord = providerRecord;
            break; // 找到第一个有效记录即可
        }

        // 1. 提取提供者的敏感变量名
        List<String> providerCredentialSecretVariables = ProviderUtil.extractSecretVariables(providerEntity);
        // 3. 获取或解密提供者凭证
        CustomProviderConfiguration customProviderConfiguration = null;
        if (customProviderRecord != null) {

            String encryptedConfigString = customProviderRecord.getEncryptedConfig();
            EncryptedConfig encryptedConfig = JSON.parseObject(encryptedConfigString, EncryptedConfig.class);
            if (encryptedConfig == null) {
                throw new RuntimeException("Invalid encrypted config");
            }
            decryptConfiguration(providerCredentialSecretVariables, encryptedConfig);

            customProviderConfiguration = new CustomProviderConfiguration(encryptedConfig);
            redisUtils.set(providerKey, customProviderConfiguration);

        }

        return customProviderConfiguration;
    }

    private CustomModelConfiguration updateCustomModelConfigurations(String providerName, ProviderModels providerModelRecord, ProviderEntity providerEntity) {

        // 5. 处理模型凭证
        CustomModelConfiguration customModelConfigurations = new CustomModelConfiguration();

        if (providerModelRecord.getEncryptedConfig() == null ||
                providerModelRecord.getEncryptedConfig().isEmpty()) {
            return customModelConfigurations;
        }

        String encryptedConfigString = providerModelRecord.getEncryptedConfig();
        EncryptedConfig encryptedConfig = JSON.parseObject(encryptedConfigString, EncryptedConfig.class);
        if (encryptedConfig == null) {
            throw new RuntimeException("Invalid encrypted config");
        }

        // 1. 提取提供者的敏感变量名
        List<String> modelCredentialSecretVariables = ProviderUtil.extractSecretVariables(providerEntity);

        decryptConfiguration(modelCredentialSecretVariables, encryptedConfig);

        CustomModelConfiguration modelConfig = new CustomModelConfiguration(
                providerModelRecord.getModelName(),
                ModelType.fromValue(providerModelRecord.getModelType()),
                encryptedConfig
        );


        String modelBaseKey = String.format(RedisKeyConstants.MODEL_CONFIGURATION_KEY_PREFIX, 1, providerName);
        // ✅ 缓存当前 model 到 Redis
        String modelKey = modelBaseKey + providerModelRecord.getModelName();
        redisUtils.set(modelKey, modelConfig);

        return customModelConfigurations;

    }


    public void saveProviderCredentials(String providerName, EncryptedConfig encryptedConfig) {
        this.customCredentialsValidate(providerName, encryptedConfig);
    }

    public void customCredentialsValidate(String providerName, EncryptedConfig encryptedConfig) {

        Providers providerEncryptedOne = providersService.lambdaQuery()
                .eq(Providers::getProviderName, providerName)
                .one();

        ModelProviderExtension providerExtension = ModelUtil.getModelProvider(providerName);
        ProviderEntity providerEntity = providerExtension.getProviderEntity();
        List<String> providerCredentialSecretVariables = ProviderUtil.extractSecretVariables(providerEntity);

        Map<String, Object> originMap;
        if (providerEncryptedOne != null) {
            EncryptedConfig originConfig = com.alibaba.fastjson2.JSON.parseObject(providerEncryptedOne.getEncryptedConfig(), EncryptedConfig.class);
            this.decryptConfiguration(providerCredentialSecretVariables, originConfig);
            originMap = BeanCopyUtil.beanToMap(originConfig);
        } else {
            originMap = new HashMap<>();
        }

        Map<String, Object> map = BeanCopyUtil.beanToMap(encryptedConfig);
        map.forEach((k, v) -> {
            if (providerCredentialSecretVariables.contains(k) && originMap.containsKey(k)) {
                if (v.equals(HIDDEN_VALUE)) {
                    map.put(k, originMap.get(k));
                }
            }
        });


        EncryptedConfig finalConfig = BeanCopyUtil.copy(EncryptedConfig.class, map);


        providerExtension.getProvider().validateProviderCredentials(finalConfig);

        // 更新缓存
        String providerKey = String.format(RedisKeyConstants.PROVIDER_CONFIGURATION_KEY, 1, providerName);
        redisUtils.set(providerKey, new CustomProviderConfiguration(finalConfig));

        finalConfig = this.encryptConfiguration(providerCredentialSecretVariables, finalConfig);

        if (providerEncryptedOne != null) {
            providerEncryptedOne.setEncryptedConfig(com.alibaba.fastjson2.JSON.toJSONString(finalConfig));
            providersService.updateById(providerEncryptedOne);
        } else {
            Providers provider = new Providers();

            provider.setProviderName(providerName);
            provider.setProviderType("custom");
            provider.setEncryptedConfig(com.alibaba.fastjson2.JSON.toJSONString(finalConfig));
            provider.setIsValid(true);
            providersService.save(provider);
        }

    }

    public void deleteProviderCredentials(String providerName) {

        Providers providerEncryptedOne = providersService.lambdaQuery()
                .eq(Providers::getProviderName, providerName)
                .one();

        if (providerEncryptedOne != null) {
            providersService.removeById(providerEncryptedOne);
            // 更新缓存
            String providerKey = String.format(RedisKeyConstants.PROVIDER_CONFIGURATION_KEY, 1, providerName);
            redisUtils.del(providerKey);
        }

    }


    public EncryptedConfig getProviderCredentials(String providerName, Boolean obfuscated) {

        ModelProviderExtension providerExtension = ModelUtil.getModelProvider(providerName);

        String providerKey = String.format(RedisKeyConstants.PROVIDER_CONFIGURATION_KEY, 1, providerName);
        CustomProviderConfiguration cachedProvider = (CustomProviderConfiguration) redisUtils.get(providerKey);
        if (cachedProvider == null) {
            throw new RuntimeException("未配置模型");
        }
        EncryptedConfig encryptedConfig = cachedProvider.getCredentials();
        if (obfuscated) {
            List<String> providerCredentialSecretVariables = ProviderUtil.extractSecretVariables(providerExtension.getProviderEntity());
            encryptedConfig = ProviderUtil.obfuscatedCredentialsVariables(providerCredentialSecretVariables, encryptedConfig);
        }
        return encryptedConfig;

    }

    public void updateProviderModels(String providerName, ProviderModelVO providerModelVO) {

        Encrypted encrypted = encryptedService.lambdaQuery()
                .eq(Encrypted::getTargetType, "providers")
                .one();
        if (encrypted == null) {
            throw new RuntimeException("加密信息不存在");
        }
        // 初始化加密信息
        RsaUtil rsaUtil = new RsaUtil(encrypted.getPublicKey(), encrypted.getPrivateKey());

        EncryptedConfig encryptedConfig = new EncryptedConfig();
        CredentialsBean credentialsBean = providerModelVO.getCredentials();
        if (credentialsBean != null) {
            encryptedConfig = BeanCopyUtil.copy(credentialsBean, EncryptedConfig.class);
        }

        ProviderModels providerModels = providerModelsService.lambdaQuery()
                .eq(ProviderModels::getProviderName, providerName)
                .eq(ProviderModels::getModelName, providerModelVO.getModel())
                .eq(ProviderModels::getModelType, providerModelVO.getModel_type())
                .one();
        boolean update = providerModels != null;
        EncryptedConfig originalConfig = null;
        if (providerModels == null) {
            providerModels = new ProviderModels();
        } else {
            originalConfig = com.alibaba.fastjson2.JSON.parseObject(providerModels.getEncryptedConfig(), EncryptedConfig.class);
            ModelProviderExtension provider = ModelUtil.getModelProvider(providerName);
            List<String> providerCredentialSecretVariables = ProviderUtil.extractSecretVariables(provider.getProviderEntity());
            this.decryptConfiguration(providerCredentialSecretVariables, originalConfig);

            if (credentialsBean != null) {
                Map<String, Object> originMap = BeanCopyUtil.beanToMap(originalConfig);;
                Map<String, Object> map = BeanCopyUtil.beanToMap(credentialsBean);
                map.forEach((k, v) -> {
                    if (providerCredentialSecretVariables.contains(k) && originMap.containsKey(k)) {
                        if (v.equals("[__HIDDEN__]")) {
                            map.put(k, originMap.get(k));
                        }
                    }
                });
                encryptedConfig = BeanCopyUtil.copy(EncryptedConfig.class, map);
            }
        }
        providerModels.setProviderName(providerName);
        providerModels.setModelName(providerModelVO.getModel());
        providerModels.setModelType(providerModelVO.getModel_type());
        providerModels.setIsValid(true);
        providerModels.setEncryptedConfig(com.alibaba.fastjson2.JSON.toJSONString(providerModelVO.getCredentials()));

        AIModel aimodel = ModelUtil.getModel(providerName, providerModelVO.getModel_type());

        try {
            aimodel.validateCredentials(providerModelVO.getModel(), encryptedConfig);
        } catch (Exception e) {
            throw new RuntimeException("验证失败" + e);
        }

        String modelKey = String.format(RedisKeyConstants.MODEL_CONFIGURATION_KEY_PREFIX, 1, providerName) + providerModelVO.getModel();
        CustomModelConfiguration modelConfig = new CustomModelConfiguration(
                providerModelVO.getModel(),
                ModelType.fromValue(providerModelVO.getModel_type()),
                encryptedConfig
        );
        redisUtils.set(modelKey, modelConfig);

        if (StringUtils.isNotEmpty(encryptedConfig.getApi_key())) {
            encryptedConfig.setApi_key(rsaUtil.encrypt(encryptedConfig.getApi_key()));
        }
        if (StringUtils.isNotEmpty(encryptedConfig.getSecret_key())) {
            encryptedConfig.setSecret_key(rsaUtil.encrypt(encryptedConfig.getSecret_key()));
        }
        if (StringUtils.isNotEmpty(encryptedConfig.getDashscope_api_key())) {
            encryptedConfig.setDashscope_api_key(rsaUtil.encrypt(encryptedConfig.getDashscope_api_key()));
        }

        providerModels.setEncryptedConfig(com.alibaba.fastjson2.JSON.toJSONString(encryptedConfig));

        if (update) {
            providerModelsService.updateById(providerModels);
        } else {
            providerModelsService.save(providerModels);
        }

    }

    public void deleteProviderModels(String providerName, ProviderModelVO providerModelVO) {

        ProviderModels providerModels = providerModelsService.lambdaQuery()
                .eq(ProviderModels::getProviderName, providerName)
                .eq(ProviderModels::getModelName, providerModelVO.getModel())
                .eq(ProviderModels::getModelType, providerModelVO.getModel_type())
                .one();
        
        if (providerModels == null) {
            throw new RuntimeException("模型不存在");
        }

        providerModelsService.removeById(providerModels);
        redisUtils.del(String.format(RedisKeyConstants.MODEL_CONFIGURATION_KEY_PREFIX, 1, providerName) + providerModelVO.getModel());
    }

    public ProviderModelCredentialVO getProviderModelCredentials(String providerName, ProviderModelVO providerModelVO) {

        String modelBaseKey = String.format(RedisKeyConstants.MODEL_CONFIGURATION_KEY_PREFIX, 1, providerName);
        String modelKey = modelBaseKey + providerModelVO.getModel();
        CustomModelConfiguration customModelConfiguration = (CustomModelConfiguration) redisUtils.get(modelKey);
        if (customModelConfiguration == null) {
            throw new RuntimeException("模型不存在");
        }

        ModelProviderExtension provider = ModelUtil.getModelProvider(providerName);
        List<String> providerCredentialSecretVariables = ProviderUtil.extractSecretVariables(provider.getProviderEntity());

        EncryptedConfig encryptedConfig = customModelConfiguration.getCredentials();
        encryptedConfig = ProviderUtil.obfuscatedCredentialsVariables(providerCredentialSecretVariables, encryptedConfig);

        ProviderModelCredentialVO providerModelCredentialVO = new ProviderModelCredentialVO();
        providerModelCredentialVO.setCredentials(encryptedConfig);

        LoadBalancingBean loadBalancingBean = new LoadBalancingBean();
        loadBalancingBean.setEnabled(true);

        return providerModelCredentialVO;
    }

    public DefaultModel getDefaultModels(ModelType modelType) {

        TenantDefaultModels tenantDefaultModels = tenantDefaultModelsService.lambdaQuery()
                .eq(TenantDefaultModels::getModelType, modelType)
                .one();

        if (tenantDefaultModels == null) {
            throw new RuntimeException("未设置模型");
        }

        String providerName = tenantDefaultModels.getProviderName();
        String modelName = tenantDefaultModels.getModelName();

        ModelInstance instance = new ModelInstance(providerName, modelName, modelType);
        DefaultModel defaultModel = new DefaultModel();
        defaultModel.setModel(instance);
        defaultModel.setProviderName(providerName);
        defaultModel.setModelName(tenantDefaultModels.getModelName());
        return defaultModel;
    }


    public DefaultModelDetailVO getModelDetail(String model_type) {
        TenantDefaultModels tenantDefaultModels = tenantDefaultModelsService.lambdaQuery()
                .eq(TenantDefaultModels::getModelType, model_type)
                .one();

        if (tenantDefaultModels == null) {
            throw new RuntimeException("未设置模型");
        }

        DefaultModelDetailVO model = new DefaultModelDetailVO();
        model.setModel(tenantDefaultModels.getModelName());
        model.setModel_type(tenantDefaultModels.getModelType());

        ModelProviderExtension provider = ModelUtil.getModelProvider(tenantDefaultModels.getProviderName());
        ProviderEntity providerEntity = provider.getProviderEntity();

        DefaultModelDetailVO.DefaultModelProviderEntity providerModel = BeanCopyUtil.copy(providerEntity, DefaultModelDetailVO.DefaultModelProviderEntity.class);
        model.setProvider(providerModel);

        return model;
    }

    public void setDefaultModel(DefaultModelSetVO params) {

        List<DefaultModelSetDetailVO> settings = params.getModel_settings();
        for (DefaultModelSetDetailVO setting : settings) {
            String model = setting.getModel();
            String model_type = setting.getModel_type();
            String provider = setting.getProvider();

            ModelType modelType = ModelType.fromValue(model_type);

            if (StringUtils.isEmpty(provider)) {
                throw new RuntimeException("请选择模型供应商");
            }
            if (StringUtils.isEmpty(model)) {
                throw new RuntimeException("请选择模型");
            }

            TenantDefaultModels tenantDefaultModels = tenantDefaultModelsService.lambdaQuery()
                    .eq(TenantDefaultModels::getModelType, model_type)
                    .one();

            if (tenantDefaultModels == null) {
                tenantDefaultModels = new TenantDefaultModels();
                tenantDefaultModels.setModelType(model_type);
                tenantDefaultModels.setModelName(model);
                tenantDefaultModels.setProviderName(provider);
            } else {
                tenantDefaultModels.setModelName(model);
                tenantDefaultModels.setProviderName(provider);
            }

            tenantDefaultModelsService.saveOrUpdate(tenantDefaultModels);
        }

    }

}