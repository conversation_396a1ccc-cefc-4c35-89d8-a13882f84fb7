package cn.bctools.model.model_runtime.entities;

import lombok.Data;

import java.util.List;

@Data
public class ModelProperties {

    private String mode;
    private int context_size;
    private int max_chunks;
    private int file_upload_limit;
    private String supported_file_extensions;
    private String default_voice;
    private List<Voices> voices;
    private int word_limit;
    private String audio_type;
    private int max_workers;
}