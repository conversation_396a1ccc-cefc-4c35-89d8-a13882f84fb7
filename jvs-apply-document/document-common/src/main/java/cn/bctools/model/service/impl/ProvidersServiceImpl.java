package cn.bctools.model.service.impl;

import cn.bctools.model.entity.data.Providers;
import cn.bctools.model.mapper.ProvidersMapper;
import cn.bctools.model.service.ProvidersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 模型供应商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ProvidersServiceImpl extends ServiceImpl<ProvidersMapper, Providers> implements ProvidersService {

}
