package cn.bctools.model.service.impl;

import cn.bctools.model.entity.data.Encrypted;
import cn.bctools.model.mapper.EncryptedMapper;
import cn.bctools.model.service.EncryptedService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 加密信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class EncryptedServiceImpl extends ServiceImpl<EncryptedMapper, Encrypted> implements EncryptedService {

}
