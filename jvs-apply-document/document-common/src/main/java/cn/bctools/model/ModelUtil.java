package cn.bctools.model;

import cn.bctools.model.model_runtime.enums.ModelType;
import cn.bctools.model.model_runtime.model_providers.ModelProviderExtension;
import cn.bctools.model.model_runtime.model_providers.ModelProviderFactory;
import cn.bctools.model.model_runtime.model_providers.__base.AIModel;

public class ModelUtil {

    public static ModelProviderExtension getModelProvider(String provider) {

        ModelProviderExtension modelProvider = ModelProviderFactory.getLoadedModelProviders().getOrDefault(provider, null);
        if (modelProvider == null) {
            throw new RuntimeException("未找到对应的模型提供者");
        }

        return modelProvider;
    }

    public static AIModel getModel(String providerName, String modelType) {
        try {
            ModelProviderExtension provider = getModelProvider(providerName);
            return provider.getProvider().getModelInstance(ModelType.fromValue(modelType));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



}
