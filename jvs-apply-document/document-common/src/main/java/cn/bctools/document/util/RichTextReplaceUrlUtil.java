package cn.bctools.document.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

/**
 * <AUTHOR>
 * 富文本下载 前缀添加
 */
public class RichTextReplaceUrlUtil {

    /**
     * 添加前缀
     *
     * @param richTextContent html内容
     * @param prefix          需要添加的前缀
     */
    public static String addImagePrefix(String richTextContent, String prefix) {
        Document document = Jsoup.parse(richTextContent);
        Elements images = document.select("img[src]");

        for (Element image : images) {
            String imageSrc = image.attr("src");
            if (!imageSrc.startsWith("http")) {
                image.attr("src", prefix + imageSrc);
            }
        }

        return document.toString();
    }
}
