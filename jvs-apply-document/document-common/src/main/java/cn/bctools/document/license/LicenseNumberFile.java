//package cn.bctools.document.license;
//
//import cn.bctools.document.entity.DcLibrary;
//import cn.bctools.document.entity.enums.DcLibraryTypeEnum;
//import cn.bctools.document.service.DcLibraryService;
//import cn.bctools.license.cons.JvsVersion;
//import cn.bctools.license.service.LicenseCheckNumberService;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import lombok.AllArgsConstructor;
//import org.springframework.stereotype.Component;
//
//@Component
//@AllArgsConstructor
//public class LicenseNumberFile implements LicenseCheckNumberService {
//
//    private final DcLibraryService dcLibraryService;
//
//    @Override
//    public int getLicenseNumber(JvsVersion.functions functionName) {
//        switch (functionName) {
//            case 转换工具:
//            case 文档数量:
//                return dcLibraryService.count(new LambdaQueryWrapper<DcLibrary>().ne(DcLibrary::getType, DcLibraryTypeEnum.knowledge));
//        }
//        return 0;
//    }
//}
