package cn.bctools.document.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 公共的配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "common")
@Data
public class CommonConfig {
    /**
     * onlyOffice url
     */
    private String onlyOfficeUrl;
    /**
     * onlyOffice url
     */
    private String ocrUrl;


}
