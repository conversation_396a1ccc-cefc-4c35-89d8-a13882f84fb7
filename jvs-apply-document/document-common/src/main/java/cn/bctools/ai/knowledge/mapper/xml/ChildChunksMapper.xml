<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.knowledge.mapper.ChildChunksMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.knowledge.entity.data.ChildChunk">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="know_id" property="knowId" />
        <result column="ki_id" property="kiId" />
        <result column="segment_id" property="segmentId" />
        <result column="position" property="position" />
        <result column="content" property="content" />
        <result column="word_count" property="wordCount" />
        <result column="index_node_id" property="indexNodeId" />
        <result column="index_node_hash" property="indexNodeHash" />
        <result column="type" property="type" />
        <result column="create_by_id" property="createById" />
        <result column="create_time" property="createTime" />
        <result column="update_by_id" property="updateById" />
        <result column="update_time" property="updateTime" />
        <result column="indexing_time" property="indexingTime" />
        <result column="completed_time" property="completedTime" />
        <result column="error" property="error" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, know_id, ki_id, segment_id, position, content, word_count, index_node_id, index_node_hash, type, create_by_id, create_time, update_by_id, update_time, indexing_time, completed_time, error
    </sql>

</mapper>