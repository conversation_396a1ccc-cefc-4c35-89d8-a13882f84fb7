<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.knowledge.mapper.KnowMetadataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.knowledge.entity.data.KnowMetadata">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="know_id" property="knowId" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="update_time" property="updateTime" />
        <result column="update_by_id" property="updateById" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, know_id, name, type, create_time, create_by, create_by_id, update_time, update_by_id
    </sql>

</mapper>
