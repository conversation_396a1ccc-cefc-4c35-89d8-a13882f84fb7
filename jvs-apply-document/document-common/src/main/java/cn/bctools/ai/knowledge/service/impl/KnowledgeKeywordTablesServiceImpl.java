package cn.bctools.ai.knowledge.service.impl;

import cn.bctools.ai.knowledge.entity.data.KnowledgeKeywordTable;
import cn.bctools.ai.knowledge.mapper.KnowledgeKeywordTablesMapper;
import cn.bctools.ai.knowledge.service.KnowledgeKeywordTablesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 知识库关键词映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Slf4j
@Service
@AllArgsConstructor
public class KnowledgeKeywordTablesServiceImpl extends ServiceImpl<KnowledgeKeywordTablesMapper, KnowledgeKeywordTable> implements KnowledgeKeywordTablesService {

}