package cn.bctools.ai.knowledge.entity.bean.knowledge;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 重排序模型信息
 * <AUTHOR>
 * RerankingModel
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class RerankingModel {
    /**
     * rerankingProviderName
     */
    @JsonProperty("reranking_provider_name")
    @JSONField(name = "reranking_provider_name")
    private String rerankingProviderName;
    /**
     * rerankingModelName
     */
    @JsonProperty("reranking_model_name")
    @JSONField(name = "reranking_model_name")
    private String rerankingModelName;
}