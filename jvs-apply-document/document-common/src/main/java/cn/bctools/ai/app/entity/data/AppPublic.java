package cn.bctools.ai.app.entity.data;

import cn.bctools.ai.knowledge.entity.enums.AuditStatus;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应用公开表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_app_public")
@ApiModel(value="AppPublic对象", description="应用公开表")
public class AppPublic implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "类型")
    @TableField("type")
    private String type;

    @ApiModelProperty(value = "应用id")
    @TableField("app_id")
    private String appId;

    @ApiModelProperty(value = "应用类别")
    @TableField("app_mode")
    private String appMode;

    @ApiModelProperty(value = "应用名称")
    @TableField("app_name")
    private String appName;

    @ApiModelProperty(value = "应用图标")
    @TableField("app_icon")
    private String appIcon;

    @ApiModelProperty(value = "应用描述")
    @TableField("app_desc")
    private String appDesc;

    @ApiModelProperty(value = "应用版本号")
    @TableField("app_version")
    private String appVersion;

    @ApiModelProperty(value = "应用配置id")
    @TableField("app_config_id")
    private String appConfigId;

    @ApiModelProperty(value = "应用工作流id")
    @TableField("app_workflow_id")
    private String appWorkflowId;

    @ApiModelProperty(value = "审核状态 0-待审核，1-已通过，2-已退回")
    @TableField("audit_status")
    private AuditStatus auditStatus;

    @ApiModelProperty(value = "审核意见")
    @TableField("audit_opinion")
    private String auditOpinion;

    @ApiModelProperty(value = "审核人")
    @TableField("audit_by")
    private String auditBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @ApiModelProperty(value = "创建人名称")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人名称")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "删除标志")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;


}