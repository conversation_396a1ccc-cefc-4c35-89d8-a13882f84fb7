package cn.bctools.ai.app.app_config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
public enum VariableEntityType {
    TEXT_INPUT("text-input"),
    SELECT("select"),
    PARAGRAPH("paragraph"),
    NUMBER("number"),
    EXTERNAL_DATA_TOOL("external_data_tool"),
    FILE("file"),
    FILE_LIST("file-list");

    private final String value;

    VariableEntityType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static VariableEntityType fromValue(String value) {
        for (VariableEntityType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid VariableEntityType value: " + value);
    }
}

