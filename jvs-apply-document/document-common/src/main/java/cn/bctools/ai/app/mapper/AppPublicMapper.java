package cn.bctools.ai.app.mapper;

import cn.bctools.ai.app.entity.data.AppPublic;
import cn.bctools.ai.app.entity.vo.app_public.AppPublicApplyDetailVO;
import cn.bctools.ai.app.entity.vo.app_public.AppPublicApplyListVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 应用公开表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface AppPublicMapper extends BaseMapper<AppPublic> {

    IPage<AppPublicApplyDetailVO> getMyApplyList(Page<AppPublicApplyDetailVO> page, AppPublicApplyListVO params);

}
