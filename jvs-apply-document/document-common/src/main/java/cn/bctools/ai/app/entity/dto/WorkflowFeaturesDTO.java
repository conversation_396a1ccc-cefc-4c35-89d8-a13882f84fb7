package cn.bctools.ai.app.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
@NoArgsConstructor
@Data
public class WorkflowFeaturesDTO {
    private String opening_statement;
    private List<?> suggested_questions;
    private SuggestedQuestionsAfterAnswerBean suggested_questions_after_answer;
    private TextToSpeechBean text_to_speech;
    private SpeechToTextBean speech_to_text;
    private RetrieverResourceBean retriever_resource;
    private SensitiveWordAvoidanceBean sensitive_word_avoidance;
    private FileUploadBean file_upload;
}
