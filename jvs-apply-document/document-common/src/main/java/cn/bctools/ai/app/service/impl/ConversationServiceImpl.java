package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.entity.data.Conversation;
import cn.bctools.ai.app.entity.data.ConversationMessageFeedbacks;
import cn.bctools.ai.app.entity.data.ConversationMessages;
import cn.bctools.ai.app.entity.dto.ConversationListDTO;
import cn.bctools.ai.app.entity.dto.ConversationMessagesDTO;
import cn.bctools.ai.app.entity.dto.ModelConfigDTO;
import cn.bctools.ai.app.entity.vo.ChatMessageBodyVO;
import cn.bctools.ai.app.entity.vo.ConversationListVO;
import cn.bctools.ai.app.entity.vo.api.ConversationMessageVO;
import cn.bctools.ai.app.entity.vo.api.FeedbackVO;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.mapper.ConversationMapper;
import cn.bctools.ai.app.service.ConversationMessageFeedbacksService;
import cn.bctools.ai.app.service.ConversationMessagesService;
import cn.bctools.ai.app.service.ConversationService;
import cn.bctools.ai.app.utils.LLMGenerator;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 会话记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ConversationServiceImpl extends ServiceImpl<ConversationMapper, Conversation> implements ConversationService {

    ConversationMessagesService conversationMessagesService;

    ConversationMessageFeedbacksService conversationMessageFeedbacksService;

    @Override
    public Conversation initConversation(ChatMessageBodyVO param, String accountId, InvokeFrom invokeFrom) {
        Conversation conversation = new Conversation();


        AppDetail app = AppDetailContext.getObject();
        conversation.setAppId(app.getId());
        conversation.setModelId(app.getMode());
        conversation.setAppModelConfigId(app.getAppModelConfigId());

        String fromSource;
        if (InvokeFrom.SERVICE_API.equals(invokeFrom) || InvokeFrom.WEB_APP.equals(invokeFrom)) {
            fromSource = "api";
            conversation.setFromEndUserId(AppDetailContext.getEndUserId());
        } else {
            fromSource = "console";
            conversation.setFromAccountId(accountId);
        }
        conversation.setInvokeFrom(invokeFrom.getValue());
        conversation.setFromSource(fromSource);

        ModelConfigDTO modelConfig = param.getModel_config();
        conversation.setModelProvider(JSON.toJSONString(modelConfig.getModel()));
        conversation.setName("New conversation");
        conversation.setInputs(JSON.toJSONString(param.getInputs()));
        conversation.setIntroduction(modelConfig.getOpening_statement());
        conversation.setStatus("normal");

        this.save(conversation);

        return conversation;
    }

    @Override
    public IPage<ConversationListDTO> getConversationList(Page<Conversation> page, ConversationListVO params) {

        AppDetail app = AppDetailContext.getObject();

        IPage<Conversation> rtn = this.lambdaQuery()
                .eq(Conversation::getAppId, app.getId())
                .ge(params.getStart() != null, Conversation::getCreateTime, params.getStart())
                .le(params.getEnd() != null, Conversation::getCreateTime, params.getEnd())
                .like(params.getKeyword() != null, Conversation::getName, params.getKeyword())
                .orderBy(StringUtils.isNotBlank(params.getSortBy()), false, convertToSFunction(params.getSortBy()))
                .page(page);


        Map<String, Integer> adminLikeMap = new HashMap<>();
        Map<String, Integer> adminDisLikeMap = new HashMap<>();
        Map<String, Integer> userLikeMap = new HashMap<>();
        Map<String, Integer> userDisLikeMap = new HashMap<>();


        List<String> conversationIds = rtn.getRecords().stream().map(Conversation::getId).toList();
        if (!conversationIds.isEmpty()) {
            List<ConversationMessageFeedbacks> ms = conversationMessageFeedbacksService.lambdaQuery()
                    .in(ConversationMessageFeedbacks::getConversationId, conversationIds)
                    .list();

            for (ConversationMessageFeedbacks feedback : ms) {
                String cid = feedback.getConversationId();
                String fromSource = feedback.getFromSource();
                String rating = feedback.getRating();

                if ("admin".equals(fromSource)) {
                    if ("like".equals(rating)) {
                        adminLikeMap.merge(cid, 1, Integer::sum);
                    } else if ("dislike".equals(rating)) {
                        adminDisLikeMap.merge(cid, 1, Integer::sum);
                    }
                } else if ("user".equals(fromSource)) {
                    if ("like".equals(rating)) {
                        userLikeMap.merge(cid, 1, Integer::sum);
                    } else if ("dislike".equals(rating)) {
                        userDisLikeMap.merge(cid, 1, Integer::sum);
                    }
                }
            }
        } else {
            return new Page<>();
        }


        return rtn.convert(item -> {
            ConversationListDTO conversationListDTO = BeanCopyUtil.copy(item, ConversationListDTO.class);
            ConversationListDTO.FeedbackStat admin_feedback_stats = new ConversationListDTO.FeedbackStat();
            admin_feedback_stats.setLike(adminLikeMap.getOrDefault(item.getId(), 0));
            admin_feedback_stats.setDislike(adminDisLikeMap.getOrDefault(item.getId(), 0));
            conversationListDTO.setAdmin_feedback_stats(admin_feedback_stats);

            ConversationListDTO.FeedbackStat user_feedback_stats = new ConversationListDTO.FeedbackStat();
            user_feedback_stats.setLike(userLikeMap.getOrDefault(item.getId(), 0));
            user_feedback_stats.setDislike(userDisLikeMap.getOrDefault(item.getId(), 0));
            conversationListDTO.setUser_feedback_stats(user_feedback_stats);
            return conversationListDTO;
        });

    }

    private static SFunction<Conversation, Object> convertToSFunction(String sortField) {
        return switch (sortField) {
            case "create_time" -> Conversation::getCreateTime;
            case "update_time" -> Conversation::getUpdateTime;
            // 添加其他支持的字段
            default -> Conversation::getCreateTime; // 默认按时间排序
        };
    }

    @Override
    public void saveFeedback(String messageId, String rating, String fromSource) {

        ConversationMessages message = conversationMessagesService.getById(messageId);
        if (message == null) {
            throw new BusinessException("消息不存在");
        }
        String userId = UserCurrentUtils.getUserId();

        ConversationMessageFeedbacks feedbacks = conversationMessageFeedbacksService.lambdaQuery()
                .eq(ConversationMessageFeedbacks::getMessageId, messageId)
                .eq(ConversationMessageFeedbacks::getFromSource, fromSource)
                .eq(ConversationMessageFeedbacks::getFromAccountId, userId)
                .one();

        if (feedbacks != null) {
            if (rating == null) {
                conversationMessageFeedbacksService.removeById(feedbacks.getId());
                return;
            }
            throw new BusinessException("请勿重复反馈");
        }

        List<String> allowType = Arrays.asList("like", "dislike");
        if (!allowType.contains(rating)) {
            throw new BusinessException("反馈类型错误");
        }

        ConversationMessageFeedbacks fb = new ConversationMessageFeedbacks();
        fb.setAppId(message.getAppId());
        fb.setConversationId(message.getConversationId());
        fb.setMessageId(messageId);
        fb.setRating(rating);
        fb.setFromSource(fromSource);
        fb.setFromAccountId(userId);
        conversationMessageFeedbacksService.save(fb);

    }

    public IPage<ConversationMessagesDTO> adminGetConversationMessages(Page<ConversationMessages> page, String conversationId) {

        Conversation conversation = this.lambdaQuery()
                .eq(Conversation::getId, conversationId)
                .one();

        if (conversation == null) {
            throw new BusinessException("会话不存在");
        }

        IPage<ConversationMessages> messages = conversationMessagesService.lambdaQuery()
                .eq(ConversationMessages::getConversationId, conversationId)
                .orderByDesc(ConversationMessages::getCreateTime)
                .page(page);


        List<String> messageIds = messages.getRecords().stream().map(ConversationMessages::getId).toList();
        Map<String, String> ratingMap = new HashMap<>();
        if (!messageIds.isEmpty()) {
            List<ConversationMessageFeedbacks> ms = conversationMessageFeedbacksService.lambdaQuery()
                    .in(ConversationMessageFeedbacks::getMessageId, messageIds)
                    .eq(ConversationMessageFeedbacks::getFromSource, "admin")
                    .list();
            ms.forEach(item -> {
                ratingMap.put(item.getMessageId(), item.getRating());
            });
        }

        IPage<ConversationMessagesDTO> rtn = messages.convert(item -> {


            ConversationMessagesDTO conversationMessagesDTO = BeanCopyUtil.copy(item, ConversationMessagesDTO.class);
            if (item.getInputs() != null) {
                conversationMessagesDTO.setInputsMap(JSON.parseObject(item.getInputs(), Map.class));
                conversationMessagesDTO.setInputs("");
            }

            if (item.getMessage() != null) {
                conversationMessagesDTO.setMessagesList(JSON.parseArray(item.getMessage()));
                conversationMessagesDTO.setMessage("");
            }

            FeedbackVO feedbackVO = new FeedbackVO();
            feedbackVO.setRating(ratingMap.get(item.getId()));
            conversationMessagesDTO.setFeedback(feedbackVO);

            return conversationMessagesDTO;
        });

        return rtn;

    }

    public List<String> getSuggestedQuestions(String appId, String messageId) {

        ConversationMessages message = conversationMessagesService.getById(messageId);
        if (message == null) {
            throw new BusinessException("消息不存在");
        }

        Conversation conversation = this.lambdaQuery()
                .eq(Conversation::getId, message.getConversationId())
                .one();
        if (conversation == null) {
            throw new BusinessException("会话不存在");
        }
        if (!conversation.getStatus().equals("normal")) {
            throw new BusinessException("会话异常");
        }

        int maxTokenLimit = 3000;
        int messageLimit = 3;

        List<ConversationMessages> historyMessages = conversationMessagesService.lambdaQuery()
                .eq(ConversationMessages::getConversationId, message.getConversationId())
//                .eq(ConversationMessages::getAppId, appId)
                .orderByDesc(ConversationMessages::getCreateTime)
                .last("limit " + messageLimit)
                .list();

        List<ChatMessage> chatMessages = new ArrayList<>();

        for (ConversationMessages item : historyMessages) {

            UserMessage userMessage = UserMessage.from(item.getQuery());
            chatMessages.add(userMessage);

            AiMessage aiMessage = AiMessage.from(item.getAnswer());
            chatMessages.add(aiMessage);
        }

        // todo 限制token数


        String humanPreFix = "Human";
        String aiPreFix = "Assistant";
        List<String> stringMessages = new ArrayList<>();
        for (ChatMessage item : chatMessages) {

            String role = "";
            String content = "";
            if (item instanceof UserMessage) {
                role = humanPreFix;
                content = ((UserMessage) item).singleText();
            } else if (item instanceof AiMessage) {
                role = aiPreFix;
                content = ((AiMessage) item).text();
            } else {
                continue;
            }

            stringMessages.add(role + ": " + content);
        }

        String historyPrompt = String.join("\n", stringMessages);

        return LLMGenerator.generateSuggestedQuestionsAfterAnswer(historyPrompt);



    }



}
