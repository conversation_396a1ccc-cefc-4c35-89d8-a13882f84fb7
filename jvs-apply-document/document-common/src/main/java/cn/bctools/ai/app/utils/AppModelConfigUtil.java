package cn.bctools.ai.app.utils;

import cn.bctools.ai.app.entity.data.AppModelConfigs;
import cn.bctools.ai.app.entity.dto.ModelBean;
import cn.bctools.ai.app.entity.dto.ModelConfigDTO;
import cn.bctools.ai.app.entity.dto.UserInputFormBean;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class AppModelConfigUtil {

    public static void config_validate(ModelConfigDTO config, AppMode appMode) {

        if (appMode == AppMode.CHAT) {
            validate_model(config.getModel());
        }

    }


    public static void validate_model(ModelBean model) {

        if (model == null) {
            throw new BusinessException("缺少应用模型配置");
        }

        if (StringUtils.isBlank(model.getProvider())) {
            throw new BusinessException("缺少模型提供者");
        }

        if (StringUtils.isBlank(model.getName())) {
            throw new BusinessException("缺少模型名称");
        }

    }


    public static ModelConfigDTO convert(AppModelConfigs config) {

        String user_input_form = config.getUser_input_form();
        config.setUser_input_form("");

        String suggested_questions = config.getSuggested_questions();
        config.setSuggested_questions("");

        ModelConfigDTO configDTO = BeanCopyUtil.copy(JSON.toJSON(config), ModelConfigDTO.class);

        if (user_input_form != null) {
            List<Map<String, UserInputFormBean>> result = JSON.parseObject(
                    user_input_form, new TypeReference<>() {}
            );
            configDTO.setUser_input_form(result);
        }
        if (suggested_questions != null) {
            List<String> suggested_questions_list = JSON.parseArray(suggested_questions, String.class);
            configDTO.setSuggested_questions(suggested_questions_list);
        }

        return configDTO;
    }


}
