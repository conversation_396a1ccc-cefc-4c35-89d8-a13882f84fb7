package cn.bctools.ai.app.interceptor;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.service.AppsService;
import cn.bctools.ai.app.utils.JwtUtil;
import cn.bctools.common.utils.SpringContextUtil;
import io.jsonwebtoken.Claims;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class ApiInterceptor implements HandlerInterceptor {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object handler) throws Exception {
        //在流式响应场景下，由于响应通道保持打开状态，可能会触发拦截器的多次执行。我们可以在拦截器中添加一个标志位来避免重复处理。
        // 当响应已提交时直接放行（针对流式响应第二次触发的情况）
        if (response.isCommitted()) {
            return true;
        }
        AppsService appsService = SpringContextUtil.getBean(AppsService.class);

        //用户登录判断业务
        String jwtToken = request.getHeader("X-APP-TOKEN");
        if (jwtToken != null) {
            Claims claims = JwtUtil.parseToken(jwtToken);

            String appId = claims.get("iss").toString();
            String configId = claims.get("config_id").toString();
            AppDetail app;
            if (configId != null && !configId.isEmpty()) {
                app = appsService.getAppInfoByConfigId(appId, configId);
            } else {
                app = appsService.getAppInfo(appId);
            }
            if (app == null) {
                throw new RuntimeException("应用不存在");
            }
            AppDetailContext.setObject(app);

            String endUserId = claims.get("end_user_id").toString();
            AppDetailContext.setEndUserId(endUserId);
            return true;
        }

        System.out.println("用户未登录");
        response.setStatus(403);
        return false;
    }


    /**
     * 请求完成后执行（无论是否发生异常）
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) throws Exception {
        // 清理 ThreadLocal 上下文，防止内存泄漏
        AppDetailContext.clear();
        AppDetailContext.endUserClear();

        // 可选：打印请求结束日志
        System.out.println("请求完成，已清理上下文");

        // 可选：记录异常日志
        if (ex != null) {
            System.err.println("请求处理过程中发生异常: " + ex.getMessage());
            ex.printStackTrace();
        }
    }

}
