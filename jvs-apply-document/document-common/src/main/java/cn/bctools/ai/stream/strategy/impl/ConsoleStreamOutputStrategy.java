package cn.bctools.ai.stream.strategy.impl;

import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.rule.dto.StreamNodeExecutionDto;
import cn.bctools.ai.stream.enums.StreamEventType;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.stream.strategy.StreamOutputStrategy;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 控制台日志流式输出策略
 * 将执行过程输出到控制台和日志文件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConsoleStreamOutputStrategy implements StreamOutputStrategy {
    
    @Override
    public StreamOutputType getOutputType() {
        return StreamOutputType.CONSOLE;
    }
    
    @Override
    public void initialize(StreamExecutionContext context) {
        log.info("🚀 初始化控制台流式输出 - 执行ID: {}", context.getExecutionId());
        outputStreamEvent(StreamEventType.CUSTOM, 
                new JSONObject()
                        .fluentPut("message", "控制台流式输出已初始化")
                        .fluentPut("executionId", context.getExecutionId())
                        .fluentPut("timestamp", System.currentTimeMillis()));
    }
    
    @Override
    public void onWorkflowStarted(StreamExecutionContext context, int totalNodes) {
        JSONObject event = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("totalNodes", totalNodes)
                .fluentPut("status", "started")
                .fluentPut("timestamp", System.currentTimeMillis());
        
        log.info("🚀 [STREAM] 工作流开始执行 - 执行ID: {}, 总节点数: {}", context.getExecutionId(), totalNodes);
        outputStreamEvent(StreamEventType.WORKFLOW_STARTED, event);
    }
    
    @Override
    public void onNodeStarted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        log.info("▶️ [STREAM] 节点开始执行 - 节点: {} ({}), 功能: {}", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId(), 
                nodeExecution.getFunctionName());
        outputStreamEvent(StreamEventType.NODE_STARTED, nodeExecution);
    }
    
    @Override
    public void onNodeRunning(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        log.debug("⏳ [STREAM] 节点执行中 - 节点: {} ({})", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId());
        outputStreamEvent(StreamEventType.NODE_RUNNING, nodeExecution);
    }
    
    @Override
    public void onNodeCompleted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        log.info("✅ [STREAM] 节点执行完成 - 节点: {} ({}), 耗时: {}ms", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId(), 
                nodeExecution.getDuration());
        
        // 输出输入输出详情
        if (nodeExecution.getInputs() != null && !nodeExecution.getInputs().isEmpty()) {
            log.debug("📥 [STREAM] 节点输入参数 - 节点: {}, 参数: {}", 
                    nodeExecution.getNodeName(), 
                    JSONObject.toJSONString(nodeExecution.getInputs()));
        }
        
        if (nodeExecution.getOutputs() != null) {
            log.debug("📤 [STREAM] 节点输出结果 - 节点: {}, 结果: {}", 
                    nodeExecution.getNodeName(), 
                    JSONObject.toJSONString(nodeExecution.getOutputs()));
        }
        
        outputStreamEvent(StreamEventType.NODE_COMPLETED, nodeExecution);
    }
    
    @Override
    public void onNodeFailed(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        log.error("❌ [STREAM] 节点执行失败 - 节点: {} ({}), 错误: {}", 
                nodeExecution.getNodeName(), 
                nodeExecution.getNodeId(), 
                nodeExecution.getErrorMessage());
        outputStreamEvent(StreamEventType.NODE_FAILED, nodeExecution);
    }
    
    @Override
    public void onWorkflowCompleted(StreamExecutionContext context, Object finalResult) {
        log.info("🎉 [STREAM] 工作流执行完成 - 执行ID: {}", context.getExecutionId());
        
        // 输出执行统计
        StreamExecutionContext.ExecutionStats stats = context.getStats();
        log.info("📊 [STREAM] 执行统计 - 总节点: {}, 已执行: {}, 失败: {}, 总耗时: {}ms, 平均耗时: {}ms", 
                stats.getTotalNodes(), 
                stats.getExecutedNodes(), 
                stats.getFailedNodes(), 
                stats.getTotalDuration(), 
                stats.getAverageNodeDuration());
        
        JSONObject event = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("finalResult", finalResult)
                .fluentPut("stats", stats)
                .fluentPut("timestamp", System.currentTimeMillis());
        
        outputStreamEvent(StreamEventType.WORKFLOW_COMPLETED, event);
    }
    
    @Override
    public void onWorkflowFailed(StreamExecutionContext context, String errorMessage) {
        log.error("💥 [STREAM] 工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), errorMessage);
        
        JSONObject event = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("errorMessage", errorMessage)
                .fluentPut("stats", context.getStats())
                .fluentPut("timestamp", System.currentTimeMillis());
        
        outputStreamEvent(StreamEventType.WORKFLOW_FAILED, event);
    }
    
    @Override
    public void onProgressUpdate(StreamExecutionContext context, int progress, int executedNodes, int totalNodes) {
        log.info("📊 [STREAM] 进度更新 - 执行ID: {}, 进度: {}% ({}/{})", 
                context.getExecutionId(), progress, executedNodes, totalNodes);
        
        JSONObject event = new JSONObject()
                .fluentPut("executionId", context.getExecutionId())
                .fluentPut("progress", progress)
                .fluentPut("executedNodes", executedNodes)
                .fluentPut("totalNodes", totalNodes)
                .fluentPut("timestamp", System.currentTimeMillis());
        
        outputStreamEvent(StreamEventType.PROGRESS_UPDATE, event);
    }
    
    @Override
    public void onEvent(StreamExecutionContext context, StreamEventType eventType, Object data) {
        log.debug("📡 [STREAM] 通用事件 - 类型: {}, 数据: {}", eventType.getCode(), data);
        outputStreamEvent(eventType, data);
    }
    
    @Override
    public void cleanup(StreamExecutionContext context) {
        log.info("🧹 [STREAM] 控制台输出清理完成 - 执行ID: {}", context.getExecutionId());
    }
    
    @Override
    public boolean supports(StreamExecutionContext context) {
        return context.getOutputType() == StreamOutputType.CONSOLE;
    }
    
    /**
     * 输出流式事件到控制台
     * 格式化输出，便于调试和监控
     * 
     * @param eventType 事件类型
     * @param data 事件数据
     */
    private void outputStreamEvent(StreamEventType eventType, Object data) {
        // 构造标准的流式输出格式
        String streamOutput = String.format("data: {\"event\": \"%s\", \"data\": %s}\n\n", 
                eventType.getCode(), 
                JSONObject.toJSONString(data));
        
        // 输出到控制台（可以被重定向到文件）
        System.out.print(streamOutput);
        
        // 同时输出到日志文件（结构化日志）
        log.debug("STREAM_EVENT: event={}, data={}", eventType.getCode(), JSONObject.toJSONString(data));
    }
}