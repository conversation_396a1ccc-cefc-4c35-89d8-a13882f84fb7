package cn.bctools.ai.knowledge.entity.bean.docmetadata;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * 元数据类型基类
 */
@ApiModel("元数据类型基类")
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = MetaBook.class, name = MetaBook.TYPE),
        @JsonSubTypes.Type(value = MetaBusinessDocument.class, name = MetaBusinessDocument.TYPE),
        @JsonSubTypes.Type(value = MetaImChatLog.class, name = MetaImChatLog.TYPE),
        @JsonSubTypes.Type(value = MetaPaper.class, name = MetaPaper.TYPE),
        @JsonSubTypes.Type(value = MetaPersonalDocument.class, name = MetaPersonalDocument.TYPE),
        @JsonSubTypes.Type(value = MetaSocialMediaPost.class, name = MetaSocialMediaPost.TYPE),
        @JsonSubTypes.Type(value = MetaSyncedFromGithub.class, name = MetaSyncedFromGithub.TYPE),
        @JsonSubTypes.Type(value = MetaSyncedFromNotion.class, name = MetaSyncedFromNotion.TYPE),
        @JsonSubTypes.Type(value = MetaWebPage.class, name = MetaWebPage.TYPE),
        @JsonSubTypes.Type(value = MetaWikipediaEntry.class, name = MetaWikipediaEntry.TYPE),
        @JsonSubTypes.Type(value = MetaOther.class, name = MetaOther.TYPE),
})
public class MetaBase {

    @ApiModelProperty("类型")
    private String type;

}