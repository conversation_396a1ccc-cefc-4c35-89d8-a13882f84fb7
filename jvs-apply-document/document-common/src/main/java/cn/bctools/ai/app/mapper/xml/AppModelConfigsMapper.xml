<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.app.mapper.AppModelConfigsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.app.entity.data.AppModelConfigs">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="opening_statement" property="openingStatement" />
        <result column="suggested_questions" property="suggestedQuestions" />
        <result column="suggested_questions_after_answer" property="suggestedQuestionsAfterAnswer" />
        <result column="more_like_this" property="moreLikeThis" />
        <result column="model" property="model" />
        <result column="user_input_form" property="userInputForm" />
        <result column="pre_prompt" property="prePrompt" />
        <result column="agent_mode" property="agentMode" />
        <result column="speech_to_text" property="speechToText" />
        <result column="sensitive_word_avoidance" property="sensitiveWordAvoidance" />
        <result column="retriever_resource" property="retrieverResource" />
        <result column="dataset_configs" property="datasetConfigs" />
        <result column="external_data_tools" property="externalDataTools" />
        <result column="file_upload" property="fileUpload" />
        <result column="text_to_speech" property="textToSpeech" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by_id" property="createById" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, opening_statement, suggested_questions, suggested_questions_after_answer, more_like_this, model, user_input_form, pre_prompt, agent_mode, speech_to_text, sensitive_word_avoidance, retriever_resource, dataset_configs, external_data_tools, file_upload, text_to_speech, create_time, update_time, create_by_id, create_by, update_by, tenant_id, del_flag
    </sql>

</mapper>
