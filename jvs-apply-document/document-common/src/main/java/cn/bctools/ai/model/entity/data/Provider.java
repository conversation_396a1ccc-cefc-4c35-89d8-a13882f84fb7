package cn.bctools.ai.model.entity.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 模型供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_provider")
@ApiModel(value="Provider对象", description="模型供应商")
public class Provider implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "模型供应商名称")
    @TableField("provider_name")
    private String providerName;

    @ApiModelProperty(value = "模型供应商类型")
    @TableField("provider_type")
    private String providerType;

    @ApiModelProperty(value = "模型供应商配置")
    @TableField("provider_config")
    private String providerConfig;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "修改人id")
    @TableField(value = "update_by_id", fill = FieldFill.INSERT_UPDATE)
    private String updateById;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识，0-未删除，1-已删除")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;


}