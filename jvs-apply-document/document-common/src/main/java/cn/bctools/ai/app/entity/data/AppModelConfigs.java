package cn.bctools.ai.app.entity.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应用配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "ai_app_model_configs", autoResultMap = true)
@ApiModel(value="AppModelConfigs对象", description="应用配置")
public class AppModelConfigs implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "应用id")
    @TableField("app_id")
    private String appId;

    @ApiModelProperty(value = "版本")
    @TableField("version")
    @JsonInclude
    private String version;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "上一个版本id")
    @TableField("previous_version")
    private String previousVersion;

    @ApiModelProperty(value = "开场白")
    @TableField("opening_statement")
    private String opening_statement;

    @ApiModelProperty(value = "建议问题")
    @TableField(value = "suggested_questions")
    private String suggested_questions;

    @ApiModelProperty(value = "下一步问题建议")
    @TableField(value = "suggested_questions_after_answer")
    private String suggested_questions_after_answer;

    @ApiModelProperty(value = "更多类似")
    @TableField(value = "more_like_this")
    private String more_like_this;

    @ApiModelProperty(value = "模型配置")
    @TableField(value = "model")
    private String model;

    @ApiModelProperty(value = "变量")
    @TableField(value = "user_input_form")
    private String user_input_form;

    @ApiModelProperty(value = "预设prompt")
    @TableField("pre_prompt")
    private String pre_prompt;

    @ApiModelProperty(value = "代理模式")
    @TableField(value = "agent_mode")
    private String agent_mode;

    @ApiModelProperty(value = "语音转文字")
    @TableField(value = "speech_to_text")
    private String speech_to_text;

    @ApiModelProperty(value = "内容审查")
    @TableField(value = "sensitive_word_avoidance")
    private String sensitive_word_avoidance;

    @ApiModelProperty(value = "引用和归属")
    @TableField(value = "retriever_resource")
    private String retriever_resource;

    @ApiModelProperty(value = "引用知识库配置")
    @TableField(value = "dataset_configs")
    private String dataset_configs;

    @ApiModelProperty(value = "外部工具")
    @TableField(value = "external_data_tools")
    private String external_data_tools;

    @ApiModelProperty(value = "文件上传")
    @TableField(value = "file_upload")
    private String file_upload;

    @ApiModelProperty(value = "文字转语音")
    @TableField(value = "text_to_speech")
    private String text_to_speech;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @ApiModelProperty(value = "创建人名称")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人名称")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "删除标志")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;


}