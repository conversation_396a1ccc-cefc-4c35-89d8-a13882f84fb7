package cn.bctools.ai.knowledge.service.impl;

import cn.bctools.ai.common.util.ExceptionUtil;
import cn.bctools.ai.common.util.TokenCalculateUtil;
import cn.bctools.ai.knowledge.core.context.KnowContext;
import cn.bctools.ai.knowledge.core.eventlistener.event.DeleteSegmentIndexEvent;
import cn.bctools.ai.knowledge.core.eventlistener.event.DisableSegmentToIndexEvent;
import cn.bctools.ai.knowledge.core.eventlistener.event.EnableSegmentToIndexEvent;
import cn.bctools.ai.knowledge.core.eventlistener.event.ImportSegmentEvent;
import cn.bctools.ai.knowledge.core.rag.VectorService;
import cn.bctools.ai.knowledge.entity.bean.knowledge.EmbeddingModelSetting;
import cn.bctools.ai.knowledge.entity.bean.processrule.ProcessRule;
import cn.bctools.ai.knowledge.entity.constant.IndexingTechnique;
import cn.bctools.ai.knowledge.entity.constant.RKey;
import cn.bctools.ai.knowledge.entity.data.ChildChunk;
import cn.bctools.ai.knowledge.entity.data.ItemSegment;
import cn.bctools.ai.knowledge.entity.data.Knowledge;
import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.enums.DocForm;
import cn.bctools.ai.knowledge.entity.enums.ProcessStatus;
import cn.bctools.ai.knowledge.entity.enums.StatusAction;
import cn.bctools.ai.knowledge.entity.vo.*;
import cn.bctools.ai.knowledge.mapper.ItemSegmentsMapper;
import cn.bctools.ai.knowledge.service.ChildChunksService;
import cn.bctools.ai.knowledge.service.ItemSegmentsService;
import cn.bctools.ai.knowledge.service.KnowledgeItemService;
import cn.bctools.ai.knowledge.util.TextProcessUtils;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.database.util.IdGenerator;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import dev.langchain4j.community.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识库条目分块 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Slf4j
@Service
@AllArgsConstructor
public class ItemSegmentsServiceImpl extends ServiceImpl<ItemSegmentsMapper, ItemSegment> implements ItemSegmentsService {

    private final ChildChunksService chunkService;

    private final KnowledgeItemService itemService;

    private final ApplicationContext applicationContext;

    private final RedisUtils redisUtils;

    private final RedissonClient redissonClient;

    private final VectorService vectorService;

    @Override
    public int maxPosition(String itemId) {
        Integer max = baseMapper.maxPosition(itemId);
        if (Objects.isNull(max)) {
            return 0;
        }
        return max;
    }

    @Override
    public ItemSegment getByIndexId(String knowId, String indexNodeId) {
        return this.lambdaQuery().eq(ItemSegment::getKnowId, knowId)
                .eq(ItemSegment::getIndexNodeId, indexNodeId).last("limit 1").one();
    }

    @Override
    public Map<String, Integer> getItemsSegmentCount(List<String> itemIds) {
        if (ObjectNull.isNull(itemIds)) {
            return Map.of();
        }
        List<ItemSegmentCountVo> list = baseMapper.getItemsSegmentCount(itemIds);
        if (ObjectNull.isNull(list)) {
            return Map.of();
        }

        return list.stream().collect(Collectors.toMap(ItemSegmentCountVo::getKiId, ItemSegmentCountVo::getCount));
    }

    @Override
    public Map<String, Integer> getCompletedItemsSegmentCount(List<String> itemIds) {
        if (ObjectNull.isNull(itemIds)) {
            return Map.of();
        }
        List<ItemSegmentCountVo> list = baseMapper.getCompletedItemsSegmentCount(itemIds);
        if (ObjectNull.isNull(list)) {
            return Map.of();
        }
        return list.stream().collect(Collectors.toMap(ItemSegmentCountVo::getKiId, ItemSegmentCountVo::getCount));

    }

    @Override
    public Integer getAvailableSegmentCount(String knowId) {
        return this.lambdaQuery().eq(ItemSegment::getKnowId, knowId)
                .eq(ItemSegment::getStatus, ProcessStatus.COMPLETED)
                .eq(ItemSegment::getEnabled, true).count().intValue();
    }

    @Override
    public Integer getTotalHitCount(String itemId) {
        return baseMapper.getTotalHitCount(itemId);
    }


    @Override
    public void getPage(String itemId, Page<ItemSegment> page, SegmentSearchVo vo) {
        LambdaQueryChainWrapper<ItemSegment> wrapper = this.lambdaQuery().eq(ItemSegment::getKiId, itemId);
        if (StrUtil.isNotEmpty(vo.getStatusList())) {
            List<String> statusList = Arrays.stream(vo.getStatusList().split(",")).toList();
            wrapper.in(ItemSegment::getStatus, statusList);
        }

        if (Objects.nonNull(vo.getHitCountGte())) {
            wrapper.ge(ItemSegment::getHitCount, vo.getHitCountGte());
        }

        if (Objects.nonNull(vo.getKeyword())) {
            wrapper.like(ItemSegment::getContent, vo.getKeyword());
        }

        if (Objects.nonNull(vo.getEnabled())) {
            wrapper.eq(ItemSegment::getEnabled, vo.getEnabled());
        }

        wrapper.page(page);

        if (ObjectNull.isNull(page.getRecords())) {
            return;
        }

        //确定知识库的doc_form是否只有一种形式   目前只支持一种形式，但是api可以传入别的形式，需要在新增文档时检验
        List<String> segmentIds = page.getRecords().stream().map(ItemSegment::getId).toList();
        //获取其中一个文档的处理规则
        ProcessRule processRule = itemService.getProcessRuleByItemId(page.getRecords().get(0).getKiId());
        Map<String, List<ChildChunk>> childChunkMap = chunkService.getSegmentsChildChunks(segmentIds, processRule);
        page.getRecords().forEach(e -> {
            e.setChildChunks(childChunkMap.getOrDefault(e.getId(), List.of()));
        });
    }

    @Override
    public void deleteBatch(String knowId, String itemId, List<String> segmentIds) {
        List<String> indexNodeIds = this.lambdaQuery()
                .eq(ItemSegment::getKiId, itemId)
                .in(ItemSegment::getId, segmentIds)
                .select(ItemSegment::getIndexNodeId)
                .list().stream().map(ItemSegment::getIndexNodeId).collect(Collectors.toList());
        if (ObjectNull.isNotNull(indexNodeIds)) {
            //删除文本块
            this.lambdaUpdate().in(ItemSegment::getIndexNodeId, indexNodeIds).remove();
            //删除文本块索引信息
            applicationContext.publishEvent(new DeleteSegmentIndexEvent(this, knowId, itemId, indexNodeIds));
        }
    }

    @Override
    public void updateSegmentStatus(StatusAction statusAction, List<String> segmentIds) {
        Knowledge knowledge = KnowContext.getKnowledge();
        KnowledgeItem item = KnowContext.getDocument();
        LambdaQueryChainWrapper<ItemSegment> wrapper = this.lambdaQuery()
                .eq(ItemSegment::getKnowId, knowledge.getId())
                .eq(ItemSegment::getKiId, item.getId())
                .in(ItemSegment::getId, segmentIds)
                .select(ItemSegment::getId);
        switch (statusAction) {
            case ENABLE:
                List<String> enableIds = wrapper
                        .eq(ItemSegment::getEnabled, false)
                        .list().stream().map(ItemSegment::getId).toList();
                if (ObjectNull.isNotNull(enableIds)) {
                    List<String> enableSegmentIds = new ArrayList<>();
                    for (String segmentId : enableIds) {
                        //检验是否索引中
                        String cacheKey = RKey.SEGMENT_INDEXING + segmentId;
                        if (redisUtils.exists(cacheKey)) {
                            continue;
                        }
                        enableSegmentIds.add(segmentId);
                    }

                    //更新状态
                    this.lambdaUpdate().in(ItemSegment::getId, enableSegmentIds)
                            .set(ItemSegment::getEnabled, true)
                            .set(ItemSegment::getDisabledById, null)
                            .set(ItemSegment::getDisabledTime, null)
                            .update();
                    //更新索引
                    applicationContext.publishEvent(new EnableSegmentToIndexEvent(this, knowledge.getId(),
                            item.getId(), enableSegmentIds));
                }
                break;
            case DISABLE:
                List<String> disableIds = wrapper
                        .eq(ItemSegment::getEnabled, true)
                        .list().stream().map(ItemSegment::getId).toList();
                if (ObjectNull.isNotNull(disableIds)) {
                    List<String> disableSegmentIds = new ArrayList<>();
                    for (String segmentId : disableIds) {
                        //检验是否索引中
                        String cacheKey = RKey.SEGMENT_INDEXING + segmentId;
                        if (redisUtils.exists(cacheKey)) {
                            continue;
                        }
                        disableSegmentIds.add(segmentId);
                    }

                    //更新状态
                    this.lambdaUpdate().in(ItemSegment::getId, disableSegmentIds)
                            .set(ItemSegment::getEnabled, false)
                            .set(ItemSegment::getDisabledById, UserCurrentUtils.getUserId())
                            .set(ItemSegment::getDisabledTime, LocalDateTime.now())
                            .update();
                    //更新索引
                    applicationContext.publishEvent(new DisableSegmentToIndexEvent(this, knowledge.getId(),
                            item.getId(), disableSegmentIds));
                }
                break;
            default:
                break;
        }
    }

    @Override
    @SneakyThrows
    public ItemSegment createSegment(CreateSegmentReqVo vo) {
        Knowledge knowledge = KnowContext.getKnowledge();
        KnowledgeItem item = KnowContext.getDocument();
        String docId = IdGenerator.getIdStr();
        String hash = TextProcessUtils.generateTextHash(vo.getContent());
        int tokens = TokenCalculateUtil.getTokenNumOfContent(vo.getContent());
        String lockName = RKey.ADD_SEGMENT_LOCK_DOCUMENT + item.getId();
        RLock lock = redissonClient.getLock(lockName);

        ItemSegment segment = new ItemSegment();
        try {
            boolean locked = lock.tryLock(10 * 60, 10 * 60, TimeUnit.SECONDS);
            if (locked) {
                int maxPosition = this.maxPosition(item.getId());
                segment.setKnowId(knowledge.getId());
                segment.setKiId(item.getId());
                segment.setIndexNodeId(docId);
                segment.setIndexNodeHash(hash);
                segment.setTokens(tokens);
                segment.setWordCount(vo.getContent().length());
                segment.setPosition(maxPosition + 1);
                segment.setContent(vo.getContent());
                segment.setStatus(ProcessStatus.COMPLETED);
                segment.setIndexingTime(LocalDateTime.now());
                segment.setCompletedTime(LocalDateTime.now());

                if (DocForm.QA_MODEL.equals(item.getDocForm())) {
                    segment.setWordCount(segment.getWordCount() + StrUtil.length(vo.getAnswer()));
                    segment.setAnswer(vo.getAnswer());
                }

                //新增文本块
                this.save(segment);
                //更新文档字符数
                item.setWordCount(item.getWordCount() + segment.getWordCount());
                itemService.updateById(item);

                try {
                    vectorService.createSegmentsVector(List.of(segment), List.of(vo.getKeywords()),
                            knowledge, item);

                } catch (Exception e) {
                    String error;
                    if (e instanceof BusinessException) {
                        error = e.getMessage();
                    } else {
                        error = ExceptionUtil.gatherInfo(e);
                    }
                    segment.setEnabled(false);
                    segment.setDisabledById(UserCurrentUtils.getUserId());
                    segment.setDisabledTime(LocalDateTime.now());
                    segment.setStatus(ProcessStatus.ERROR);
                    segment.setError(error);
                    this.updateById(segment);
                    throw e;
                }
            }

            List<ChildChunk> childChunks = chunkService.getSegmentChildChunks(segment.getId(), item.getProcessRule());
            segment.setChildChunks(childChunks);
            return segment;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    @Override
    public ItemSegment updateSegment(String segmentId, UpdateSegmentReqVo vo) {
        Knowledge knowledge = KnowContext.getKnowledge();
        KnowledgeItem item = KnowContext.getDocument();

        String cacheKey = RKey.SEGMENT_INDEXING + segmentId;
        if (redisUtils.exists(cacheKey)) {
            throw new BusinessException("文本块正处于索引状态，请稍后重试");
        }
        ItemSegment segment = this.getExistById(segmentId);
        Boolean action = vo.getEnabled();

        //判断是否禁用文本块
        if (Objects.nonNull(vo.getEnabled())) {
            //文本块启用&&操作禁用，则禁用文本块
            if (!Objects.equals(segment.getEnabled(), action) && !action) {
                segment.setEnabled(false);
                segment.setDisabledById(UserCurrentUtils.getUserId());
                segment.setDisabledTime(LocalDateTime.now());
                this.updateById(segment);
                redisUtils.set(cacheKey, 1, 600L);
                applicationContext.publishEvent(new DisableSegmentToIndexEvent(this, knowledge.getId()
                        , item.getId(), List.of(segmentId)));
                return segment;
            }
        }

        //判断是否可更新
        if (Boolean.FALSE.equals(segment.getEnabled())) {
            throw new BusinessException("不能更新禁用的文本块");
        }

        try {
            //是否QA模式
            boolean isQa = DocForm.QA_MODEL.equals(item.getDocForm());
            //是否父子分段模式
            boolean isParentChild = DocForm.HIERARCHICAL_MODEL.equals(item.getDocForm());
            //是否重新生成子块
            boolean regenerateChild = Boolean.TRUE.equals(vo.getRegenerateChildChunks());
            //是否高质量
            Boolean isHighQuality = IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique());
            // 内容未改变
            if (Objects.equals(vo.getContent(), segment.getContent())) {
                if (isQa) {
                    segment.setAnswer(vo.getAnswer());
                    segment.setWordCount(segment.getWordCount() + StrUtil.length(vo.getAnswer()));
                }
                boolean keywordChange = false;
                if (ObjectNull.isNotNull(vo.getKeywords())) {
                    if (segment.getKeywords().size() != vo.getKeywords().size()) {
                        segment.setKeywords(vo.getKeywords());
                        keywordChange = true;
                    }
                }
                segment.setEnabled(true);
                segment.setDisabledById(null);
                segment.setDisabledTime(null);
                this.updateById(segment);

                if (isParentChild
                        && regenerateChild) {
                    //重新生成子块
                    EmbeddingModelSetting embeddingModel;
                    if (isHighQuality) {
                        if (ObjectNull.isNotNull(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel())) {
                            embeddingModel = new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(),
                                    knowledge.getEmbeddingModel());
                        } else {
                            //使用系统默认
                            embeddingModel = new EmbeddingModelSetting();
                        }
                    } else {
                        throw new BusinessException("知识库索引模式不是高质量模式，不可生成子块");
                    }

                    if (Objects.isNull(item.getProcessRule())) {
                        throw new BusinessException("文档处理规则为空，处理失败");
                    }

                    //重新生成子块
                    vectorService.generateChildChunks(knowledge, item, segment, embeddingModel, item.getProcessRule(), true);
                } else if (List.of(DocForm.TEXT_MODEL, DocForm.QA_MODEL).contains(item.getDocForm())) {
                    // 如果设置文档启用或者关键词数组发生变动，则重新创建文本块索引
                    if (Boolean.TRUE.equals(vo.getEnabled()) || keywordChange) {
                        vectorService.createSegmentsVector(
                                List.of(segment),
                                ObjectNull.isNotNull(vo.getKeywords()) ? List.of(vo.getKeywords()) : null,
                                knowledge, item
                        );
                    }
                }
            } else {
                String hash = TextProcessUtils.generateTextHash(vo.getContent());
                int tokens = 0;
                if (isHighQuality) {
                    if (isQa) {
                        tokens = TokenCalculateUtil.getTokenNumOfContent(vo.getContent() + vo.getAnswer());
                    } else {
                        tokens = TokenCalculateUtil.getTokenNumOfContent(vo.getContent());
                    }
                }
                //用于计算文档字符长度
                Integer rawWordCount = segment.getWordCount();
                segment.setContent(vo.getContent());
                segment.setIndexNodeHash(hash);
                segment.setWordCount(vo.getContent().length());
                segment.setTokens(tokens);
                segment.setStatus(ProcessStatus.COMPLETED);
                segment.setIndexingTime(LocalDateTime.now());
                segment.setCompletedTime(LocalDateTime.now());
                segment.setEnabled(true);
                segment.setDisabledTime(null);
                segment.setDisabledById(null);
                if (isQa) {
                    segment.setAnswer(vo.getAnswer());
                    segment.setWordCount(segment.getWordCount() + StrUtil.length(vo.getAnswer()));
                }
                this.updateById(segment);
                //字符长度差
                int wordCountChange = rawWordCount - segment.getWordCount();
                if (wordCountChange != 0) {
                    itemService.lambdaUpdate().eq(KnowledgeItem::getId, item.getId())
                            .set(KnowledgeItem::getWordCount, Math.max(0, item.getWordCount() + wordCountChange))
                            .update();
                }
                if (isParentChild && regenerateChild) {
                    EmbeddingModelSetting embeddingModel;
                    if (isHighQuality) {
                        if (ObjectNull.isNotNull(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel())) {
                            embeddingModel = new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(),
                                    knowledge.getEmbeddingModel());
                        } else {
                            //使用系统默认
                            embeddingModel = new EmbeddingModelSetting();
                        }
                    } else {
                        throw new BusinessException("知识库索引模式不是高质量模式，不可生成子块");
                    }

                    if (Objects.isNull(item.getProcessRule())) {
                        throw new BusinessException("文档处理规则为空，处理失败");
                    }

                    //重新生成子块
                    vectorService.generateChildChunks(knowledge, item, segment, embeddingModel,
                            item.getProcessRule(), true);
                } else if (List.of(DocForm.TEXT_MODEL, DocForm.QA_MODEL).contains(item.getDocForm())) {
                    vectorService.updateSegmentVector(
                            knowledge,
                            segment,
                            vo.getKeywords()
                    );
                }
            }
        } catch (Exception e) {
            String error;
            if (e instanceof BusinessException) {
                error = e.getMessage();
                log.error("更新文本块失败：{}", e.getMessage());
            } else {
                error = ExceptionUtil.gatherInfo(e);
                log.error("更新文本块失败", e);
            }
            segment.setEnabled(false);
            segment.setDisabledTime(LocalDateTime.now());
            segment.setDisabledById(UserCurrentUtils.getUserId());
            segment.setStatus(ProcessStatus.ERROR);
            segment.setError(error);
            this.updateById(segment);
        }
        ItemSegment res = this.getExistById(segmentId);
        res.setChildChunks(chunkService.getSegmentChildChunks(segmentId, item.getProcessRule()));
        return res;
    }


    @Override
    @SafeVarargs
    public final ItemSegment getExistById(String id, SFunction<ItemSegment, ?>... columns) {
        ItemSegment segment;
        if (ObjectNull.isNull((Object) columns)) {
            segment = this.lambdaQuery().eq(ItemSegment::getId, id)
                    .select(columns)
                    .last("limit 1").one();
        } else {
            segment = getById(id);
        }
        return Optional.ofNullable(segment).orElseThrow(() -> new BusinessException("文本块不存在"));
    }

    @Override
    public String importSegments(String knowId, String itemId, List<SegmentImportVo> vos) {
        String jobId = IdGenerator.getIdStr();
        String cacheKey = RKey.SEGMENT_IMPORT_LOCK + jobId;
        redisUtils.set(cacheKey, "waiting");

        applicationContext.publishEvent(new ImportSegmentEvent(this, cacheKey, knowId, itemId, vos));

        return jobId;
    }
}