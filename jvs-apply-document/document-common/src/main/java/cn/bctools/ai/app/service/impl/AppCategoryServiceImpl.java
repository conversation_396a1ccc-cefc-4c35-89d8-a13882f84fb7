package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.data.AppCategory;
import cn.bctools.ai.app.entity.vo.AppCategoryInfoVO;
import cn.bctools.ai.app.entity.vo.AppCategoryListVO;
import cn.bctools.ai.app.entity.vo.AppListDetailVO;
import cn.bctools.ai.app.entity.vo.DeleteVO;
import cn.bctools.ai.app.mapper.AppCategoryMapper;
import cn.bctools.ai.app.service.AppCategoryService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 * 应用类别 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppCategoryServiceImpl extends ServiceImpl<AppCategoryMapper, AppCategory> implements AppCategoryService {


    public AppCategory create(AppCategoryInfoVO params) {

        // 检查排序是否已被使用
        AppCategory orderExit = this.lambdaQuery().eq(AppCategory::getSort, params.getSort()).one();
        if (orderExit != null) {
            throw new BusinessException("当前排序已存在数据，请重新输入");
        }

        AppCategory appCategory = new AppCategory();
        appCategory.setName(params.getName());
        appCategory.setDescription(params.getDescription());
        appCategory.setStatus(params.getStatus());
        appCategory.setSort(params.getSort());

        return this.save(appCategory) ? appCategory : null;
    }

    public AppCategory edit(String categoryId, AppCategoryInfoVO appCategoryInfoVO) {

        AppCategory appCategory = this.getById(categoryId);
        if (appCategory == null) {
            throw new BusinessException("当前数据不存在");
        }
        // 检查排序是否已被使用
        AppCategory orderExit = this.lambdaQuery()
                .eq(AppCategory::getSort, appCategoryInfoVO.getSort())
                .ne(AppCategory::getId, categoryId)
                .one();
        if (orderExit != null) {
            throw new BusinessException("当前排序已存在数据，请重新输入");
        }


        appCategory.setName(appCategoryInfoVO.getName());
        appCategory.setDescription(appCategoryInfoVO.getDescription());
        appCategory.setStatus(appCategoryInfoVO.getStatus());
        appCategory.setSort(appCategoryInfoVO.getSort());
        return this.updateById(appCategory) ? appCategory : null;

    }

    public AppCategory editStatus(String categoryId, Boolean status) {

        AppCategory appCategory = this.getById(categoryId);

        if (appCategory == null) {
            throw new BusinessException("当前数据不存在");
        }

        appCategory.setStatus(status);

        return this.updateById(appCategory) ? appCategory : null;
    }

    public void deleteCategories(DeleteVO param) {

        List<String> categoryIds = param.getIds();

        List<AppCategory> appCategories = this.listByIds(categoryIds);
        if (appCategories.size() != categoryIds.size()) {
            throw new BusinessException("当前数据不存在");
        }

        this.removeByIds(categoryIds);
    }


    public IPage<AppCategory> getLists(Page<AppCategory> page, AppCategoryListVO params) {

        return this.lambdaQuery()
                .like(StringUtils.isNotEmpty(params.getKeyword()), AppCategory::getName, params.getKeyword())
                .eq(params.getStatus() != null, AppCategory::getStatus, params.getStatus())
                .orderByAsc(AppCategory::getSort)
                .page(page);
    }


}
