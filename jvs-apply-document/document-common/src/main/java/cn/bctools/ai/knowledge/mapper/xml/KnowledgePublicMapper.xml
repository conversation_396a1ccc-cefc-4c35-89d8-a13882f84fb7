<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.knowledge.mapper.KnowledgePublicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.knowledge.entity.data.KnowledgePublic">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="know_id" property="knowId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="audit_status" property="auditStatus" />
        <result column="audit_opinion" property="auditOpinion" />
        <result column="audit_by" property="auditBy" />
        <result column="audit_by_id" property="auditById" />
        <result column="audit_time" property="auditTime" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, know_id, name, description, audit_status, audit_opinion, audit_by, audit_by_id, audit_time, create_by, create_by_id, create_time
    </sql>

</mapper>
