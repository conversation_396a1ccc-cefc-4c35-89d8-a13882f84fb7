package cn.bctools.ai.app.configs;

import cn.bctools.ai.app.interceptor.ApiInterceptor;
import cn.bctools.ai.app.interceptor.AppInterceptor;
import cn.bctools.ai.app.interceptor.BaseInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class AppWebMvcConfig implements WebMvcConfigurer {


    @Autowired
    private ApiInterceptor apiInterceptor;

    @Autowired
    private BaseInterceptor baseInterceptor;

    @Autowired
    private AppInterceptor appInterceptor;
    /**
     * 忽略权限校验
     */
    private static final String IGNORE_APP_PERMISSION_VERIFICATION_PATTERN = "/base/**";
    /**
     * 三方系统调用逻辑引擎的时候处理的方式
     */
    private static final String RULE_API_VERIFICATION_PATTERN = "/rule/openapi/**";
    /**
     * 默认放行的地址前缀
     */
    private static final List<String> DEFAULT_PERMIT_URLS = new ArrayList<>();

    static {
        String[] urlPatterns = {
                IGNORE_APP_PERMISSION_VERIFICATION_PATTERN,
                RULE_API_VERIFICATION_PATTERN,
                "/v1/**",
                "/api/**",
                "/api/passport",
                "/app-market",
                "/apps/create"};
        DEFAULT_PERMIT_URLS.addAll(Arrays.asList(urlPatterns));
        DEFAULT_PERMIT_URLS.add("/provider-models/**");
        DEFAULT_PERMIT_URLS.add("/model-providers/**");
        DEFAULT_PERMIT_URLS.add("/default-models/**");
    }

    private static final PathMatcher PATH_MATCHER = new AntPathMatcher();

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // api接口拦截器
        registry.addInterceptor(apiInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/passport")
                .excludePathPatterns("/app-market")
                .excludePathPatterns("/api/market-passport");

        // 基础拦截器
        registry.addInterceptor(baseInterceptor).addPathPatterns("/**");

        // 应用权限拦截器,拦截所有请求并单独处理每一个设计的权限，是否是具有权限
        registry.addInterceptor(appInterceptor)
                //放开地址
                .excludePathPatterns(DEFAULT_PERMIT_URLS)
                .pathMatcher(PATH_MATCHER)
                //其它全拦截
                .addPathPatterns("/**");
    }


}
