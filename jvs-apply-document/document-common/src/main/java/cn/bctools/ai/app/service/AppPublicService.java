package cn.bctools.ai.app.service;

import cn.bctools.ai.app.entity.data.AppPublic;
import cn.bctools.ai.app.entity.vo.app_public.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 应用公开表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface AppPublicService extends IService<AppPublic> {


    IPage<AppPublicApplyDetailVO> getApplyList(Page<AppPublicApplyDetailVO> page, AppPublicApplyListVO params);

    IPage<AppPublicApplyDetailVO> getMyApplyList(Page<AppPublicApplyDetailVO> page, AppPublicApplyListVO params);

    AppPublicVO appPublic(String appId);

    void apply(AppApplyVO params);

    void applyDown(String appId);

    void withdraw(String publicId);

    void deleteApply(String publicId);

    void examine(AppPublicExamineVO params);

}
