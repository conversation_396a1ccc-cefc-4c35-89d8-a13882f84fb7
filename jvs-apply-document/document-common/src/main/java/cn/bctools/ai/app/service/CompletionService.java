package cn.bctools.ai.app.service;


import cn.bctools.ai.app.entity.data.Conversation;
import cn.bctools.ai.app.entity.data.ConversationMessages;
import cn.bctools.ai.app.entity.dto.ModelBean;
import cn.bctools.ai.app.entity.dto.ModelConfigDTO;
import cn.bctools.ai.app.entity.vo.ChatMessageBodyVO;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.utils.*;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.model.ModelInstance;
import cn.bctools.model.ModelUtil;
import cn.bctools.model.model_runtime.enums.ModelType;
import cn.bctools.model.model_runtime.model_providers.ModelProviderExtension;
import cn.bctools.model.model_runtime.model_providers.__base.AIModel;
import cn.bctools.model.model_runtime.model_providers.__base.LargeLanguageModel;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class CompletionService {

    @Autowired
    CompletionUtil completionUtil;

    @Autowired
    ConversationService conversationService;

    @Autowired
    ConversationMessagesService conversationMessagesService;

    public Object completionWithStream(ChatMessageBodyVO param) {
        return completionUtil.completionWithStream(param);
    }

    public Object generate(ChatMessageBodyVO param, InvokeFrom invokeFrom) {
        return this.generate(param, UserCurrentUtils.getUserId(), invokeFrom);
    }

    public Object generate(ChatMessageBodyVO param, String userId, InvokeFrom invokeFrom) {

        String query = param.getQuery();
        if (StringUtils.isBlank(query)) {
            throw new IllegalArgumentException("请输入您的提问");
        }

        ModelConfigDTO model_config = param.getModel_config();
        if (model_config == null) {
            throw new IllegalArgumentException("缺少应用配置");
        }

        ModelBean modelInfo = model_config.getModel();
        if (modelInfo == null) {
            throw new IllegalArgumentException("缺少应用模型配置");
        }

        String modelName = modelInfo.getName();
        if (StringUtils.isBlank(modelName)) {
            throw new IllegalArgumentException("缺少应用模型名称");
        }

        ModelConfigDTO modelConfig = param.getModel_config();
        AppModelConfigUtil.config_validate(modelConfig, AppMode.CHAT);
        VariableUtil.validate(modelConfig.getUser_input_form());

        Conversation conversation;
        if (StrUtil.isNotEmpty(param.getConversation_id())) {
            conversation = conversationService.getById(param.getConversation_id());
        } else {
            conversation = conversationService.initConversation(param, userId, invokeFrom);
            // 自动生成会话名称
            conversation.setName(LLMGenerator.generateConversationName(query));
        }
        if (conversation == null) {
            throw new BusinessException("会话不存在");
        }
        conversation.setIp(HelperUtil.getIp());
        conversationService.updateById(conversation);

        param.setConversation_id(conversation.getId());

        // 转换用户输入变量
        param.getModel_config().setPre_prompt(VariableUtil.convertQuery(modelConfig.getUser_input_form(), param.getInputs(), modelConfig.getPre_prompt()));

        ConversationMessages message = conversationMessagesService.addMessage(param, UserCurrentUtils.getUserId(), conversation.getId(), invokeFrom);
        param.setMessage_id(message.getId());

        try {
            ModelBean model = modelConfig.getModel();
            ModelInstance instance = new ModelInstance(model.getProvider(), model.getName(), ModelType.LLM);
            return instance.invoke_llm(param);
        } catch (Exception e) {
            throw new BusinessException("请求失败" + e);
        }


    }


}
