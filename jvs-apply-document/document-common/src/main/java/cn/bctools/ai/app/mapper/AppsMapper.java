package cn.bctools.ai.app.mapper;

import cn.bctools.ai.app.entity.data.Apps;
import cn.bctools.ai.app.entity.vo.AppListDetailVO;
import cn.bctools.ai.app.entity.vo.AppListVO;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 应用 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface AppsMapper extends BaseMapper<Apps> {

    IPage<AppListDetailVO> getAppList(Page page, @Param("param") AppListVO param);

}
