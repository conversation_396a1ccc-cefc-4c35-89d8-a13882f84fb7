package cn.bctools.ai.stream.strategy;

import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.dto.StreamNodeExecutionDto;
import cn.bctools.ai.stream.enums.StreamEventType;
import cn.bctools.ai.stream.enums.StreamOutputType;
import reactor.core.publisher.Flux;

/**
 * 流式输出策略接口
 * 使用策略模式支持多种输出方式
 * 
 * <AUTHOR>
 */
public interface StreamOutputStrategy {
    
    /**
     * 获取策略支持的输出类型
     */
    StreamOutputType getOutputType();
    
    /**
     * 初始化流式输出
     * 
     * @param context 执行上下文
     */
    void initialize(StreamExecutionContext context);
    
    /**
     * 工作流开始事件
     * 
     * @param context 执行上下文
     * @param totalNodes 总节点数
     */
    void onWorkflowStarted(StreamExecutionContext context, int totalNodes);
    
    /**
     * 节点开始执行事件
     * 
     * @param context 执行上下文
     * @param nodeExecution 节点执行信息
     */
    void onNodeStarted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution);
    
    /**
     * 节点执行中事件
     * 
     * @param context 执行上下文
     * @param nodeExecution 节点执行信息
     */
    void onNodeRunning(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution);
    
    /**
     * 节点执行完成事件
     * 
     * @param context 执行上下文
     * @param nodeExecution 节点执行信息
     */
    void onNodeCompleted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution);
    
    /**
     * 节点执行失败事件
     * 
     * @param context 执行上下文
     * @param nodeExecution 节点执行信息
     */
    void onNodeFailed(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution);
    
    /**
     * 工作流完成事件
     * 
     * @param context 执行上下文
     * @param finalResult 最终结果
     */
    void onWorkflowCompleted(StreamExecutionContext context, Object finalResult);
    
    /**
     * 工作流失败事件
     * 
     * @param context 执行上下文
     * @param errorMessage 错误信息
     */
    void onWorkflowFailed(StreamExecutionContext context, String errorMessage);
    
    /**
     * 进度更新事件
     * 
     * @param context 执行上下文
     * @param progress 进度百分比
     * @param executedNodes 已执行节点数
     * @param totalNodes 总节点数
     */
    void onProgressUpdate(StreamExecutionContext context, int progress, int executedNodes, int totalNodes);
    
    /**
     * 通用事件处理
     * 
     * @param context 执行上下文
     * @param eventType 事件类型
     * @param data 事件数据
     */
    void onEvent(StreamExecutionContext context, StreamEventType eventType, Object data);
    
    /**
     * 获取Flux流（仅对支持Flux的策略有效）
     * 
     * @param context 执行上下文
     * @return Flux流
     */
    default Flux<Object> getFlux(StreamExecutionContext context) {
        throw new UnsupportedOperationException("This strategy does not support Flux output");
    }
    
    /**
     * 清理资源
     * 
     * @param context 执行上下文
     */
    void cleanup(StreamExecutionContext context);
    
    /**
     * 检查策略是否支持指定的执行上下文
     * 
     * @param context 执行上下文
     * @return 是否支持
     */
    default boolean supports(StreamExecutionContext context) {
        return true;
    }
}
