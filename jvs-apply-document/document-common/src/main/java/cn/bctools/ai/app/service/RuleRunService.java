package cn.bctools.ai.app.service;

import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.common.constant.SysConstant;
import cn.bctools.common.utils.*;
import cn.bctools.design.rule.RuleStartUtils;
import cn.bctools.design.rule.entity.*;
import cn.bctools.design.rule.service.RunLogService;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.redis.utils.RedisUtils;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleExceptionEnum;
import cn.bctools.rule.entity.enums.RunType;
import cn.bctools.rule.entity.enums.type.OutputType;
import cn.bctools.rule.entity.enums.type.RuleFile;
import cn.bctools.rule.error.MessageTipsDto;
import cn.bctools.rule.exception.RuleException;
import cn.bctools.rule.utils.RuleSystemThreadLocal;
import cn.bctools.rule.utils.UrlUtils;
import cn.bctools.rule.utils.dto.RuleExecDto;
import cn.bctools.rule.utils.html.HtmlGraph;
import cn.bctools.rule.utils.html.ResultDto;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import cn.bctools.stream.enums.StreamOutputType;
import cn.bctools.stream.manager.StreamExecutionManager;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@Service
@AllArgsConstructor
public class RuleRunService {
    /**
     * 规则启动工具类，用于启动逻辑引擎
     */
    private final RuleStartUtils ruleStartUtils;
    /**
     * 运行日志服务类，用于处理运行日志相关业务
     */
    private final RunLogService runLogService;
    private final RedisUtils redisUtils;
    private final OssTemplate ossTemplate;
    /**
     * 流式执行管理器
     */
    private final StreamExecutionManager streamExecutionManager;

    /**
     * 正则表达式，用于匹配 HTTP 方法
     */
    /**
     * 规则运行缓存 key 格式
     */
    private final static String RULE_KEY_FORMAT = "edf-ai:rule:run:key:%s";
    /**
     * HTTP 协议前缀
     */
    private static final String http = "http";

    /**
     * 根据应用 ID、规则 key 等参数运行逻辑流程，并返回执行结果
     *
     * @param appId       应用 ID
     * @param ruleKey     规则 key
     * @param variableMap 请求参数映射
     * @param po          规则设计对象
     * @param request     HTTP 请求对象
     * @param response    HTTP 响应对象
     * @return 逻辑执行结果
     */
    @NotNull
    public Object runApiRule(String appId, String ruleKey, Map<String, Object> variableMap, RuleDesignPo po, HttpServletRequest request, HttpServletResponse response) {
        //设置id值
        TenantContextHolder.setTenantId(po.getTenantId());
        //直接返回
        if (ObjectNull.isNull(po)) {
            return R.failed("凭证不正确");
        }
        //校验参照是否符合规范
        checkParameterIn(variableMap, po.getParameterIn(), request);
        //取缓存值
        String cacheKey = getRuleCacheKey(po, variableMap, po.getParameterIn());
        if (ObjectNull.isNotNull(cacheKey)) {
            Object cache = redisUtils.get(cacheKey);
            if (ObjectNull.isNotNull(cache)) {
                return R.ok(cache);
            }
        }
        //根据参数获取的值
        RuleSystemThreadLocal.setParameterSelectedOption(variableMap);

        log.info("执行逻辑, ruleKey: {}, 租户id: {}", ruleKey, po.getTenantId());
        // 获取逻辑流程运行时参数
        // 1.获取数据库参数
        Map<String, Object> ruleVariable = new HashMap<>(16);
        if (po.getParameterPos() != null) {
            ruleVariable.putAll((po.getParameterPos()));
        }
        // 2.获取请求体参数
        if (ObjectUtils.isNotEmpty(variableMap)) {
            ruleVariable.putAll(variableMap);
        }
        // 3.获取请求路径上的参数(优先级最高)
        Map<String, Object> urlParams = UrlUtils.getUrlParams();
        if (ObjectUtils.isNotEmpty(urlParams)) {
            ruleVariable.putAll(urlParams);
        }
        RunLogPo logPo = runLogService.create(appId, po.getSecret(), RunType.API, ruleVariable, po.getReqType(), po.getReqType(), po.getSync());
        TenantContextHolder.setTenantId(po.getTenantId());
        logPo.setTenantId(po.getTenantId());
        runLogService.updateById(logPo);
        ruleVariable.put("ruleKey", ruleKey);
        RuleExecuteDto data = new RuleExecuteDto().setReqVariableMap(variableMap).setVariableMap(ruleVariable);
        String key = String.format(RULE_KEY_FORMAT, logPo.getId());
        SystemThreadLocal.set("redisKey", key);
        redisUtils.set(key, data, Long.valueOf(60 * 5));
        SecurityContext context = SecurityContextHolder.getContext();
        Authentication authentication = context.getAuthentication();
        RuleExecDto ruleExecDto = new RuleExecDto().setExecuteDto(data).setType(RunType.API).setSecret(po.getSecret()).setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));
        if (po.getSync()) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.getContext().setAuthentication(authentication);
                ruleStartUtils.start(po, logPo, ruleExecDto);
                redisUtils.set(key, data, Long.valueOf(60 * 5));
            }, RuleStartUtils.EXECUTOR);
            //返回执行日志对象
            return R.ok(logPo.getId());
        } else {
            Object ruleReturn = getRuleReturn(response, po, logPo, data, ruleExecDto);
            if (ruleReturn instanceof Flux) {
                //流式返回
                return ruleReturn;
            }

            // 检查是否需要流式输出
            if (isStreamingRequest(request)) {
                return handleStreamingResponse(appId, ruleKey, variableMap, po, request, response, ruleExecDto);
            }
            R r = (R) ruleReturn;
            if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getException())
                    && ruleExecDto.getExecuteDto().getException().getCode() == -1007) {
                Object rtnData = null;
                Object msgValue = ruleExecDto.getExecuteDto().getEndResult().getValue();
                if (msgValue instanceof MessageTipsDto) {
                    rtnData = Optional.of((MessageTipsDto) msgValue).map(MessageTipsDto::getData).orElse(null);
                }
                return R.failed(rtnData, ruleExecDto.getExecuteDto().getException().getCode(),
                        ruleExecDto.getExecuteDto().getException().getMessage());
            }
            //返回执行日志对象
            if (ObjectNull.isNull(data.getEndResult())) {
                return R.ok();
            }
            if (ObjectNull.isNotNull(cacheKey)) {
                //默认为存储 1 小时
                redisUtils.set(cacheKey, r.getData(), Duration.ofMinutes(po.getCacheMinute()));
            }
            return R.ok(r.getData(), r.getMsg());
        }
    }

    /**
     * 根据逻辑执行结果，返回不同类型的响应
     *
     * @param response    HTTP 响应对象
     * @param po          规则设计对象
     * @param logPo       运行日志对象
     * @param data        规则执行数据对象
     * @param ruleExecDto 规则执行 DTO 对象
     * @return 逻辑执行结果响应
     */
    private Object getRuleReturn(HttpServletResponse response, RuleDesignPo po, RunLogPo logPo,
                                 RuleExecuteDto data, RuleExecDto ruleExecDto) {
        ruleStartUtils.start(po, logPo, ruleExecDto);
        //如果最后的返回为流式返回结果,则直接导出文件结果
        if (ObjectNull.isNotNull(data.getEndResult()) && ClassType.文件.equals(data.getEndResult().getClassType())) {
            //处理流式输出
            RuleFile value = (RuleFile) data.getEndResult().getValue();
            //判断最后输出节点的类型, 看是否是文件输出类型,如果是就以文件形式的Post结果输出如果是异步
            if (value.getOutputType().equals(OutputType.download)) {
                response.setHeader("output_format", URLUtil.encode(value.getOriginalName()));
            }
            //兼容预览和下载格式
            response.setHeader("output_type", value.getOutputType().toString());
            if (!value.getUrl().startsWith(http)) {
                value.setUrl(ossTemplate.fileLink(value.getFileName(), value.getBucketName()));
            }
            return R.ok(value);
        }
        //LLM流式返回
        if (ObjectNull.isNotNull(data.getEndResult()) && data.getEndResult().getValue() instanceof Flux<?>) {
            //处理响应类型
            response.setContentType(MediaType.TEXT_EVENT_STREAM_VALUE);
            return data.getEndResult().getValue();
        }
        //返回执行日志对象
        //获取同步返回结果
        R r = R.ok().setMsg("");
        //如果最后一个节点为消息节点
        if (ruleExecDto.getExecuteDto().getStats() && ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getMessageResult())) {
            if (ruleExecDto.getExecuteDto().getException() != null && ruleExecDto.getExecuteDto().getException().getCode() == -1007) {
                Object rtnData = null;
                Object msgValue = Optional.of(ruleExecDto.getExecuteDto().getEndResult()).map(ResultDto::getValue).orElse(null);
                if (msgValue instanceof MessageTipsDto) {
                    response.setHeader("output_status", ((MessageTipsDto) msgValue).getOnOff().toString());
                    response.setHeader("message_close", ((MessageTipsDto) msgValue).getOff().toString());
                    rtnData = Optional.of((MessageTipsDto) msgValue).map(MessageTipsDto::getData).orElse(null);
                }
                r = R.failed(rtnData, ruleExecDto.getExecuteDto().getException().getCode(), ruleExecDto.getExecuteDto().getException().getMessage());
            } else {
                //成功的返回消息
                r = R.ok(ruleExecDto.getExecuteDto().getMessageResult()).setMsg(ruleExecDto.getExecuteDto().getSyncMessageTips());
            }
        } else if (!ruleExecDto.getExecuteDto().getStats()) {
            //写入消息状态,末认所有都是返回成功状态
            response.setHeader("output_status", ruleExecDto.getExecuteDto().getStats().toString());
            ResultDto endResult = ruleExecDto.getExecuteDto().getEndResult();
            if (endResult.getValue() instanceof MessageTipsDto) {
                MessageTipsDto value = (MessageTipsDto) endResult.getValue();
                response.setHeader("message_close", String.valueOf(value.getOff()));
                r = R.ok().setMsg(ruleExecDto.getExecuteDto().getSyncMessageTips()).setData(value.getData());
            } else {
                r = R.ok().setMsg(ruleExecDto.getExecuteDto().getErrorMessage());
            }
        } else if (ruleExecDto.getExecuteDto().getStats() && ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getErrorMessage())) {
            response.setHeader("output_status", String.valueOf(false));
            r = R.ok().setMsg(ruleExecDto.getExecuteDto().getErrorMessage());
        } else if (ObjectNull.isNotNull(data.getEndResult())) {
            Object value = data.getEndResult().getValue();
            if (value instanceof MessageTipsDto) {
                response.setHeader("output_status", ((MessageTipsDto) value).getOnOff().toString());
                response.setHeader("message_close", ((MessageTipsDto) value).getOff().toString());
                r.setData(((MessageTipsDto) value).getData()).setMsg(((MessageTipsDto) value).getMessage());
            } else {
                r.setData(data.getEndResult().getValue());
            }
        }
        return r;
    }

    /**
     * 根据参数获取缓存的 key
     *
     * @param po          规则设计对象
     * @param variableMap 请求参数对象值
     * @param parameterIn 所有的参数校验规则
     * @return 缓存 key，如果不存在则返回 null
     */
    private String getRuleCacheKey(RuleDesignPo po, Map<String, Object> variableMap, RuleParameterInDto parameterIn) {
        if (ObjectNull.isNotNull(po.getCacheMinute(), parameterIn)) {
            List<Object> list = new ArrayList<>();
            if (ObjectNull.isNotNull(parameterIn.getQueryList())) {
                parameterIn.getQueryList().stream().filter(e -> ObjectNull.isNotNull(e.getCache())).filter(ParameterMap::getCache).map(e -> JvsJsonPath.read(variableMap, e.getKey())).forEach(list::add);
            }
            if (ObjectNull.isNotNull(parameterIn.getHeaderList())) {
                parameterIn.getHeaderList().stream().filter(e -> ObjectNull.isNotNull(e.getCache())).filter(ParameterMap::getCache).map(e -> JvsJsonPath.read(variableMap, e.getKey())).forEach(list::add);
            }
            getBodyCacheKey(parameterIn.getBodyList(), variableMap, list);
            if (ObjectNull.isNotNull(list)) {
                String key = list.stream().map(Object::toString).collect(Collectors.joining("."));
                return SysConstant.redisKey("rule:cache", po.getSecret() + key);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 获取请求参数中的缓存是否命中缓存数据
     *
     * @param bodyList    body 结构
     * @param variableMap 请求参数变量
     * @param list        数据列表
     */
    private void getBodyCacheKey(List<BodyInDto> bodyList, Map<String, Object> variableMap, List<Object> list) {
        if (ObjectNull.isNotNull(bodyList)) {
            for (BodyInDto bodyInDto : bodyList) {
                if (ObjectNull.isNotNull(bodyInDto.getCache()) && bodyInDto.getCache()) {
                    Object read = JvsJsonPath.read(variableMap, bodyInDto.getPath());
                    list.add(read);
                }
                if (ObjectNull.isNotNull(bodyInDto.getChildren())) {
                    getBodyCacheKey(bodyInDto.getChildren(), variableMap, list);
                }
            }
        }
    }

    /**
     * 校验请求参数是否符合规范
     *
     * @param variableMap 请求参数
     * @param parameterIn 校验规则
     * @param request     HTTP 请求对象
     */
    private void checkParameterIn(Map<String, Object> variableMap, RuleParameterInDto parameterIn, HttpServletRequest request) {
        //校验类型
        if (ObjectNull.isNull(parameterIn)) {
            return;
        }
        //参数
        for (ParameterMap parameterMap : parameterIn.getHeaderList()) {
            if (ObjectNull.isNotNull(parameterMap.getNecessity()) && parameterMap.getNecessity()) {
                String header = request.getHeader(parameterMap.getKey());
                if (ObjectNull.isNull(header)) {
                    throw new RuleException(RuleExceptionEnum.入参校验不通过, parameterMap.getExplain() + "参数不存在");
                } else {
                    variableMap.put(parameterMap.getKey(), header);
                }
            } else {
                Optional<String> header = Optional.ofNullable(request.getHeader(parameterMap.getKey()));
                if (header.isPresent()) {
                    variableMap.put(parameterMap.getKey(), header.get());
                }
            }
        }
        //参数
        for (ParameterMap parameterMap : parameterIn.getQueryList()) {
            if (ObjectNull.isNotNull(parameterMap.getNecessity(), parameterMap.getKey()) && parameterMap.getNecessity()) {
                String parameter = request.getParameter(parameterMap.getKey());
                if (ObjectNull.isNull(parameter)) {
                    throw new RuleException(RuleExceptionEnum.入参校验不通过, parameterMap.getExplain() + "参数不存在");
                } else {
                    variableMap.put(parameterMap.getKey(), parameter);
                }
            } else {
                Optional<String> parameter = Optional.ofNullable(request.getParameter(parameterMap.getKey()));
                if (parameter.isPresent()) {
                    variableMap.put(parameterMap.getKey(), parameter.get());
                }
            }
        }
        String method = request.getMethod();
        if (ObjectNull.isNotNull(parameterIn.getMethod()) && !parameterIn.getMethod().toString().equals(method)) {
            throw new RuleException(RuleExceptionEnum.入参校验不通过, "请求类型不正常");
        }
        List<BodyInDto> bodyList = parameterIn.getBodyList();
        if (HttpMethod.PUT.equals(parameterIn.getMethod()) || HttpMethod.POST.equals(parameterIn.getMethod())) {
            recursiveVerification(variableMap, bodyList);
        }
    }

    /**
     * 递归校验请求体参数
     *
     * @param variableMap 请求参数
     * @param bodyList    请求体参数列表
     */
    private static void recursiveVerification(Map<String, Object> variableMap, List<BodyInDto> bodyList) {
        //处理body
        for (BodyInDto bodyInDto : bodyList) {
            //取值是否能取到
            Object read = JvsJsonPath.read(variableMap, bodyInDto.getPath());
            if (ObjectNull.isNotNull(bodyInDto.getNecessity()) && bodyInDto.getNecessity()) {
                if (ObjectNull.isNull(read)) {
                    throw new RuleException(RuleExceptionEnum.入参校验不通过, bodyInDto.getLabel() + "必填写");
                }
            } else {
                //设置默认值
                if (ObjectNull.isNotNull(bodyInDto.getDefaultValue())) {
                    JvsJsonPath.set(variableMap, bodyInDto.getPath(), bodyInDto.getDefaultValue());
                }
            }
            //正则校验
            if (ObjectNull.isNotNull(bodyInDto.getRule())) {
                if (!Pattern.compile(bodyInDto.getRule()).matcher(read.toString()).matches()) {
                    throw new RuleException(RuleExceptionEnum.入参校验不通过, bodyInDto.getLabel() + "校验不通过");
                }
            }
            if (ObjectNull.isNotNull(bodyInDto.getChildren())) {
                recursiveVerification(variableMap, bodyInDto.getChildren());
            }
        }
    }

    /**
     * 检查是否为流式请求
     */
    private boolean isStreamingRequest(HttpServletRequest request) {
        // 检查Accept头
        String accept = request.getHeader("Accept");
        if (accept != null) {
            if (accept.contains("text/event-stream") || accept.contains("application/stream+json")) {
                return true;
            }
        }

        // 检查自定义流式输出头
        String streamType = request.getHeader("X-Stream-Type");
        return streamType != null;
    }

    /**
     * 处理流式响应
     */
    private Object handleStreamingResponse(String appId, String ruleKey, Map<String, Object> variableMap,
                                           RuleDesignPo po, HttpServletRequest request, HttpServletResponse response,
                                           RuleExecDto ruleExecDto) {
        try {
            // 确定流式输出类型
            StreamOutputType outputType = determineStreamOutputType(request);

            // 创建流式执行上下文
            StreamExecutionContext context = streamExecutionManager.createContext(
                    appId,
                    po.getSecret(),
                    variableMap,
                    outputType
            );

            context.setRequest(request).setResponse(response);

            // 根据输出类型返回相应的响应
            switch (outputType) {
                case FLUX:
                    return handleFluxResponse(context, po, ruleExecDto);
                case SSE:
                    return handleSseResponse(context, po, ruleExecDto);
                case WEBSOCKET:
                    // WebSocket需要通过WebSocket端点连接
                    return R.ok("请通过WebSocket端点连接: /ws/rule-execution/" + context.getExecutionId());
                default:
                    // 默认使用控制台输出，同步执行
                    return executeRuleSync(po, ruleExecDto);
            }

        } catch (Exception e) {
            log.error("处理流式响应失败", e);
            return R.failed("流式执行失败: " + e.getMessage());
        }
    }

    /**
     * 确定流式输出类型
     */
    private StreamOutputType determineStreamOutputType(HttpServletRequest request) {
        // 检查自定义头
        String streamType = request.getHeader("X-Stream-Type");
        if (streamType != null) {
            try {
                return StreamOutputType.fromCode(streamType);
            } catch (IllegalArgumentException e) {
                log.warn("无效的流式输出类型: {}", streamType);
            }
        }

        // 检查Accept头
        String accept = request.getHeader("Accept");
        if (accept != null) {
            if (accept.contains("text/event-stream")) {
                return StreamOutputType.SSE;
            }
            if (accept.contains("application/stream+json")) {
                return StreamOutputType.FLUX;
            }
        }

        // 默认使用控制台输出
        return StreamOutputType.CONSOLE;
    }

    /**
     * 处理Flux响应
     */
    private Flux<Object> handleFluxResponse(StreamExecutionContext context, RuleDesignPo po, RuleExecDto ruleExecDto) {
        // 获取Flux流
        Flux<Object> flux = streamExecutionManager.getFlux(context);

        // 异步执行规则
        CompletableFuture.runAsync(() -> {
            try {
                ruleStartUtils.start(po, null, ruleExecDto);
            } catch (Exception e) {
                log.error("Flux流式执行失败", e);
                streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            }
        });

        return flux;
    }

    /**
     * 处理SSE响应
     */
    private Object handleSseResponse(StreamExecutionContext context, RuleDesignPo po, RuleExecDto ruleExecDto) {
        // 异步执行规则
        CompletableFuture.runAsync(() -> {
            try {
                ruleStartUtils.start(po, null, ruleExecDto);
            } catch (Exception e) {
                log.error("SSE流式执行失败", e);
                streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            }
        });

        // 返回SSE发射器
        return context.getSseEmitter();
    }

    /**
     * 同步执行规则
     */
    private R executeRuleSync(RuleDesignPo po, RuleExecDto ruleExecDto) {
        ruleStartUtils.start(po, null, ruleExecDto);

        if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getException()) &&
                ruleExecDto.getExecuteDto().getException().getCode() == -1007) {
            Object rtnData = null;
            Object msgValue = ruleExecDto.getExecuteDto().getEndResult().getValue();
            if (msgValue instanceof MessageTipsDto) {
                rtnData = Optional.of((MessageTipsDto) msgValue).map(MessageTipsDto::getData).orElse(null);
            }
            return R.failed(rtnData, ruleExecDto.getExecuteDto().getException().getCode(),
                    ruleExecDto.getExecuteDto().getException().getMessage());
        }

        if (ObjectNull.isNull(ruleExecDto.getExecuteDto().getEndResult())) {
            return R.ok();
        }

        return R.ok(ruleExecDto.getExecuteDto().getEndResult().getValue());
    }
}