package cn.bctools.ai.knowledge.core.eventlistener.event;

import cn.bctools.ai.knowledge.entity.vo.SegmentImportVo;
import lombok.Data;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * 导入文本块事件
 */
@Getter
public class ImportSegmentEvent extends ApplicationEvent {

    private final String cacheKey;

    private final String knowId;

    private final String itemId;

    private final List<SegmentImportVo> vos;

    public ImportSegmentEvent(Object source, String cacheKey, String knowId, String itemId, List<SegmentImportVo> vos) {
        super(source);
        this.cacheKey = cacheKey;
        this.knowId = knowId;
        this.itemId = itemId;
        this.vos = vos;
    }

}