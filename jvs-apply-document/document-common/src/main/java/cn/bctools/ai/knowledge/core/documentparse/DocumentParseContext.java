package cn.bctools.ai.knowledge.core.documentparse;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DocumentParseContext {
    private static final Map<String, IDocumentParse> PARSE_MAP = new HashMap<>();

    static {
        PARSE_MAP.put("local", new LocalDocumentParse());
        PARSE_MAP.put("s3", new UrlDocumentParse());
    }

    public static IDocumentParse get(String parseType){
        return PARSE_MAP.getOrDefault(parseType, new UrlDocumentParse());
    }
}