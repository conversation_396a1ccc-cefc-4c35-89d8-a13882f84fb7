package cn.bctools.ai.app.permission;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.Map;

/**
 * 检查应用请求地址权限的处理器接口
 *
 * <AUTHOR>
 * @date 2025/6/5
 */
public interface BasePermissionHandlerHandler {
    /**
     * The constant PATH_MATCHER.
     */
    PathMatcher PATH_MATCHER = new AntPathMatcher();

    /**
     * 检查应用是否有权限
     * 如果异常直接报错返回
     *
     * @param userDto            用户对象
     * @param appId              the app id
     * @param app                应用
     * @param requestUri         请求对象
     * @param variablesAttribute 请求参数
     * @return the boolean
     * @throws BusinessException the business exception
     */
    boolean check(UserDto userDto, String appId, AppDetail app, String requestUri, Map<String, Object> variablesAttribute) throws BusinessException;

}
