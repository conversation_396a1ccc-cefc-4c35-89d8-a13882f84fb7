package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.data.AppPublicCategory;
import cn.bctools.ai.app.mapper.AppPublicCategoryMapper;
import cn.bctools.ai.app.service.AppPublicCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 上架应用关联的应用类别 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppPublicCategoryServiceImpl extends ServiceImpl<AppPublicCategoryMapper, AppPublicCategory> implements AppPublicCategoryService {

}
