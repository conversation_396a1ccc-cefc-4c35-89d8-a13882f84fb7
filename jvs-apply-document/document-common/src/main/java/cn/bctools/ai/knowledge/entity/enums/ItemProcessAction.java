package cn.bctools.ai.knowledge.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 更新知识库，文档处理操作类型
 */
@Getter
@AllArgsConstructor
public enum ItemProcessAction {
    //新增
    ADD("add"),
    //修改
    UPDATE("update"),
    //删除
    REMOVE("remove");

    @JsonValue
    @EnumValue
    private final String value;
}