package cn.bctools.ai.knowledge.service.impl;

import cn.bctools.ai.knowledge.entity.constant.MetaBuiltInField;
import cn.bctools.ai.knowledge.entity.constant.RKey;
import cn.bctools.ai.knowledge.entity.data.KnowMetadata;
import cn.bctools.ai.knowledge.entity.data.KnowMetadataBinding;
import cn.bctools.ai.knowledge.entity.data.Knowledge;
import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.vo.DocumentMetaDateSetVo;
import cn.bctools.ai.knowledge.entity.vo.MetadataCreateVo;
import cn.bctools.ai.knowledge.entity.vo.MetadataDetail;
import cn.bctools.ai.knowledge.mapper.KnowMetadataMapper;
import cn.bctools.ai.knowledge.service.KnowMetadataBindingService;
import cn.bctools.ai.knowledge.service.KnowMetadataService;
import cn.bctools.ai.knowledge.service.KnowledgeItemService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 知识库元数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KnowMetadataServiceImpl extends ServiceImpl<KnowMetadataMapper, KnowMetadata> implements KnowMetadataService {

    private final RedisUtils redisUtils;

    private final KnowMetadataBindingService bindingService;

    private final KnowledgeItemService itemService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowMetadata create(String knowId, MetadataCreateVo vo) {
        //检验是否与内置属性名称重复
        if (MetaBuiltInField.ALL.contains(vo.getType())) {
            throw new BusinessException("元数据名称与内置属性名称重复");
        }
        //检验名称是否重复
        boolean exists = this.lambdaQuery().eq(KnowMetadata::getKnowId, knowId)
                .eq(KnowMetadata::getName, vo.getName()).exists();
        if (exists) {
            throw new BusinessException("存在相同名称的元数据");
        }

        KnowMetadata meta = new KnowMetadata();
        meta.setKnowId(knowId);
        meta.setName(vo.getName());
        meta.setType(vo.getType());
        this.save(meta);

        return meta;
    }

    @Override
    public List<KnowMetadata> getKnowMetas(Knowledge knowledge) {
        List<KnowMetadata> list = this.lambdaQuery()
                .eq(KnowMetadata::getKnowId, knowledge.getId())
                .select(KnowMetadata::getId, KnowMetadata::getKnowId, KnowMetadata::getType, KnowMetadata::getName)
                .list();
        if (ObjectNull.isNotNull(list)) {
            //设置元数据绑定计数
            List<String> metaIds = list.stream().map(KnowMetadata::getId).toList();
            Map<String, Integer> countMap = bindingService.getMetaBindingCounts(metaIds);
            list.forEach(e -> e.setCount(countMap.getOrDefault(e.getId(), 0)));
        }
//        if (knowledge.getBuiltInFieldEnabled()) {
//            //添加内置元数据
//            list.addAll(MetaBuiltInField.BUILD_IN_METAS);
//        }
        return list;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowMetadata rename(String knowId, String metaId, String name) {
        //判断是否存在
        KnowMetadata metadata = this.getExist(knowId, metaId);
        //判断是否有重名元数据
        boolean existsSameName = this.lambdaQuery().eq(KnowMetadata::getKnowId, knowId)
                .ne(KnowMetadata::getId, metaId)
                .eq(KnowMetadata::getName, name).exists();
        if (existsSameName) {
            throw new BusinessException("存在相同名称的元数据");
        }

        if (MetaBuiltInField.ALL.contains(name)) {
            throw new BusinessException("元数据名称与内置属性名称重复");
        }

        try {
            this.checkMetadataLock(knowId, null);
            String oldName = metadata.getName();
            metadata.setName(name);

            //更新相关文档
            List<String> itemIds = bindingService.lambdaQuery()
                    .eq(KnowMetadataBinding::getMetadataId, metaId)
                    .select(KnowMetadataBinding::getKiId)
                    .list()
                    .stream().map(KnowMetadataBinding::getKiId).toList();
            if (ObjectNull.isNotNull(itemIds)) {
                List<KnowledgeItem> items = itemService.lambdaQuery().in(KnowledgeItem::getId, itemIds)
                        .isNotNull(KnowledgeItem::getDocMetadata)
                        .select(KnowledgeItem::getId, KnowledgeItem::getDocMetadata)
                        .list();
                if (ObjectNull.isNotNull(items)) {
                    for (KnowledgeItem item : items) {
                        JSONObject docMetadata = item.getDocMetadata();
                        docMetadata.put(name, docMetadata.get(oldName));
                        docMetadata.remove(oldName);
                    }
                    itemService.updateBatchById(items);
                }
            }
            this.updateById(metadata);
            return metadata;
        } finally {
            redisUtils.del(RKey.METADATA_LOCK + knowId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String knowId, String metaId) {
        try {
            this.checkMetadataLock(knowId, null);
            KnowMetadata metadata = this.getExist(knowId, metaId);
            this.removeById(metaId);

            //处理关联文档元数据信息
            List<String> itemIds = bindingService.lambdaQuery()
                    .eq(KnowMetadataBinding::getMetadataId, metaId)
                    .select(KnowMetadataBinding::getKiId)
                    .list()
                    .stream().map(KnowMetadataBinding::getKiId).toList();
            if (ObjectNull.isNotNull(itemIds)) {
                List<KnowledgeItem> items = itemService.lambdaQuery().in(KnowledgeItem::getId, itemIds)
                        .isNotNull(KnowledgeItem::getDocMetadata)
                        .select(KnowledgeItem::getId, KnowledgeItem::getDocMetadata)
                        .list();
                if (ObjectNull.isNotNull(items)) {
                    for (KnowledgeItem item : items) {
                        JSONObject docMetadata = item.getDocMetadata();
                        docMetadata.remove(metadata.getName());
                    }
                    itemService.updateBatchById(items);
                }
            }
        } finally {
            redisUtils.del(RKey.METADATA_LOCK + knowId);
        }
    }

    @Override
    public KnowMetadata getExist(String knowId, String metaId) {
        KnowMetadata knowMetadata = this.lambdaQuery().eq(KnowMetadata::getKnowId, knowId)
                .eq(KnowMetadata::getId, metaId).last("limit 1").one();
        if (Objects.isNull(knowMetadata)) {
            throw new BusinessException("找不到元信息");
        }

        return knowMetadata;

    }


    @Override
    public void checkMetadataLock(String knowId, String docId) {
        String lockKey;
        if (StrUtil.isNotEmpty(knowId)) {
            lockKey = RKey.METADATA_LOCK + knowId;
            if (redisUtils.exists(lockKey)) {
                throw new BusinessException("其他的知识库元数据操作正在执行中，请稍等片刻");
            }
            redisUtils.set(lockKey, 1, 60 * 60L);
        }

        if (StrUtil.isNotEmpty(docId)) {
            lockKey = RKey.METADATA_LOCK + docId;
            if (redisUtils.exists(lockKey)) {
                throw new BusinessException("其他的文档元数据操作正在执行中，请稍等片刻");
            }
            redisUtils.set(lockKey, 1, 60 * 60L);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDocsMetadata(Knowledge knowledge, List<DocumentMetaDateSetVo> vos) {
        List<KnowledgeItem> updateItems = new ArrayList<>();
        List<KnowMetadataBinding> addBindings = new ArrayList<>();
        for (DocumentMetaDateSetVo vo : vos) {
            try {
                this.checkMetadataLock(null, vo.getItemId());
                KnowledgeItem item = itemService.getExistById(vo.getItemId());
                JSONObject docMetadata = new JSONObject();
                vo.getMetadataList().forEach(e -> docMetadata.put(e.getName(), e.getValue()));
                if (knowledge.getBuiltInFieldEnabled()) {
                    MetaBuiltInField.fillDocBuiltInMetas(docMetadata, item);
                }
                item.setDocMetadata(docMetadata);
                updateItems.add(item);

                //处理元数据信息绑定数据，先删除后添加
                bindingService.lambdaUpdate().eq(KnowMetadataBinding::getKiId, item.getId()).remove();
                for (MetadataDetail metadataDetail : vo.getMetadataList()) {
                    KnowMetadataBinding binding = new KnowMetadataBinding();
                    binding.setKnowId(knowledge.getId());
                    binding.setKiId(item.getId());
                    binding.setMetadataId(metadataDetail.getId());
                    addBindings.add(binding);
                }

            } finally {
                redisUtils.del(RKey.METADATA_LOCK + vo.getItemId());
            }

            itemService.updateBatchById(updateItems);
            bindingService.saveBatch(addBindings);
        }
    }

    @Override
    public List<KnowMetadata> getDocMetas(String id) {
        List<String> metaIds = bindingService.lambdaQuery().eq(KnowMetadataBinding::getKiId, id)
                .list().stream().map(KnowMetadataBinding::getMetadataId).toList();
        if (ObjectNull.isNotNull(metaIds)) {
            return this.lambdaQuery().in(KnowMetadata::getId, metaIds)
                    .select(KnowMetadata::getId, KnowMetadata::getKnowId, KnowMetadata::getName, KnowMetadata::getType)
                    .list();
        }
        return new ArrayList<>();
    }
}