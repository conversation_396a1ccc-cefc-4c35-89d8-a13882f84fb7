package cn.bctools.ai.tag.entity.vo;

import cn.bctools.ai.tag.enums.TagType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TagInfoVO {

    @ApiModelProperty("标签名称")
    @NotBlank(message = "标签名称不能为空")
    private String name;

    @ApiModelProperty("标签类型")
    @NotNull(message = "标签类型不能为空")
    private TagType type;


}
