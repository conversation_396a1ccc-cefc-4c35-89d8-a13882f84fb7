package cn.bctools.ai.stream.enums;

import cn.bctools.stream.constants.StreamConstants;
import lombok.Getter;

/**
 * 流式输出事件类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum StreamEventType {

    /**
     * 工作流开始
     */
    WORKFLOW_STARTED(StreamConstants.EventTypeCodes.WORKFLOW_STARTED, "工作流开始"),

    /**
     * 节点开始执行
     */
    NODE_STARTED(StreamConstants.EventTypeCodes.NODE_STARTED, "节点开始执行"),

    /**
     * 节点执行中
     */
    NODE_RUNNING(StreamConstants.EventTypeCodes.NODE_RUNNING, "节点执行中"),

    /**
     * 节点执行完成
     */
    NODE_COMPLETED(StreamConstants.EventTypeCodes.NODE_COMPLETED, "节点执行完成"),

    /**
     * 节点执行失败
     */
    NODE_FAILED(StreamConstants.EventTypeCodes.NODE_FAILED, "节点执行失败"),

    /**
     * 工作流完成
     */
    WORKFLOW_COMPLETED(StreamConstants.EventTypeCodes.WORKFLOW_COMPLETED, "工作流完成"),

    /**
     * 工作流失败
     */
    WORKFLOW_FAILED(StreamConstants.EventTypeCodes.WORKFLOW_FAILED, "工作流失败"),

    /**
     * 进度更新
     */
    PROGRESS_UPDATE(StreamConstants.EventTypeCodes.PROGRESS_UPDATE, "进度更新"),

    /**
     * 错误信息
     */
    ERROR(StreamConstants.EventTypeCodes.ERROR, "错误信息"),

    /**
     * 调试信息
     */
    DEBUG(StreamConstants.EventTypeCodes.DEBUG, "调试信息"),

    /**
     * 自定义事件
     */
    CUSTOM(StreamConstants.EventTypeCodes.CUSTOM, "自定义事件");
    
    private final String code;
    private final String description;
    
    StreamEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static StreamEventType fromCode(String code) {
        for (StreamEventType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return CUSTOM; // 默认返回自定义事件
    }
}