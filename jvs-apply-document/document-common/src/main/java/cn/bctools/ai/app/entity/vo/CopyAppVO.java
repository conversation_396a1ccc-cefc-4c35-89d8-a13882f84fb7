package cn.bctools.ai.app.entity.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class CopyAppVO {

    private String id;

    @NotEmpty(message = "应用名称不能为空")
    private String name;

    private String icon;

    private String desc;

    private String configId;

    // 是否同知识库设置
    private Boolean syncDataset;

    // 是否同步标签
    private Boolean syncLabel;



}
