package cn.bctools.ai.knowledge.entity.bean.docmetadata;

import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * 其他文档类型 Other document types
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(MetaOther.TYPE)
@Accessors(chain = true)
@ApiModel("其他文档类型 Other document types")
public class MetaOther extends MetaBase {
    public static final String TYPE = "other";

    @ApiModelProperty("其他文档信息")
    Map<String, Object> otherInfos;
}