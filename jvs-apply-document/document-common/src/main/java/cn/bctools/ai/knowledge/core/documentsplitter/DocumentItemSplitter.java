package cn.bctools.ai.knowledge.core.documentsplitter;

import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.ai.knowledge.entity.bean.processrule.Rules;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DocumentItemSplitter {

    /**
     * Bean名称前缀
     */
    String BEAN_NAME_PREFIX = "DocumentSplitter_";

    List<KDocument> split(KDocument document, Rules rule);

}