package cn.bctools.ai.knowledge.core.textsplitter;


import java.util.List;

/**
 * 增强递归分割器
 * <AUTHOR>
 */
public class EnhanceRecursiveCharacterTextSplitter extends RecursiveCharacterTextSplitter {

    public EnhanceRecursiveCharacterTextSplitter(int chunkSize, int chunkOverlap, Boolean keepSeparator,
                                                 Boolean addStartIndex, List<String> separators) {
        super(chunkSize, chunkOverlap, keepSeparator, addStartIndex, separators);
    }
}