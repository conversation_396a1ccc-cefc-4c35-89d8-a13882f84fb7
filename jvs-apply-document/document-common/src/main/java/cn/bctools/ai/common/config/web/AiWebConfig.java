package cn.bctools.ai.common.config.web;

import cn.bctools.ai.knowledge.interceptor.KnowInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web服务配置
 *
 * <AUTHOR>
 */
@Configuration
public class AiWebConfig implements WebMvcConfigurer {

    @Autowired
    KnowInterceptor knowInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(knowInterceptor)
                .addPathPatterns("/knowledge/**")
                .addPathPatterns("/document/**")
                .addPathPatterns("/segment/**")
                .addPathPatterns("/chunk/**")
                .addPathPatterns("/know-metadata/**");
    }

}