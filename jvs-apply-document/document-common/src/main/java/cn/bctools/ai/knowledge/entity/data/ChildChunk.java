package cn.bctools.ai.knowledge.entity.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serial;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_child_chunks")
@ApiModel(value="ChildChunks对象", description="")
@With
@AllArgsConstructor
@NoArgsConstructor
public class ChildChunk implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "知识库id")
    @TableField("know_id")
    private String knowId;

    @ApiModelProperty(value = "条目id")
    @TableField("ki_id")
    private String kiId;

    @ApiModelProperty(value = "分块id")
    @TableField("segment_id")
    private String segmentId;

    @TableField("position")
    private Integer position;

    @TableField("content")
    private String content;

    @TableField("word_count")
    private Integer wordCount;

    @TableField("index_node_id")
    private String indexNodeId;

    @TableField("index_node_hash")
    private String indexNodeHash;

    @TableField("type")
    private String type;

    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_by_id", fill = FieldFill.INSERT_UPDATE)
    private String updateById;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField("indexing_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime indexingTime;

    @TableField("completed_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedTime;

    @TableField("error")
    private String error;

}