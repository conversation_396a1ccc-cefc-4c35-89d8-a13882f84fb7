package cn.bctools.ai.app.entity.data;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("conversation_messages")
@ApiModel(value="ConversationMessages对象", description="消息表")
public class ConversationMessages implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "会话id")
    @TableField("conversation_id")
    private String conversationId;

    @ApiModelProperty(value = "应用id")
    @TableField("app_id")
    private String appId;

    @ApiModelProperty(value = "父级消息id")
    @TableField("parent_message_id")
    private String parentMessageId;

    @ApiModelProperty(value = "模型提供商")
    @TableField("model_provider")
    private String modelProvider;

    @ApiModelProperty(value = "模型id")
    @TableField("model_id")
    private String modelId;

    @ApiModelProperty(value = "模型配置")
    @TableField("override_model_config")
    private String overrideModelConfig;

    @ApiModelProperty(value = "ip")
    @TableField("ip")
    private String ip;

    @ApiModelProperty(value = "分享次数")
    @TableField("share_num")
    private String shareNum;

    @ApiModelProperty(value = "输入")
    @TableField("inputs")
    private String inputs;

    @ApiModelProperty(value = "问题")
    @TableField("query")
    private String query;

    @ApiModelProperty(value = "消息")
    @TableField("message")
    private String message;

    @ApiModelProperty(value = "消息tokens")
    @TableField("message_tokens")
    private BigDecimal messageTokens;

    @ApiModelProperty(value = "思考")
    @TableField("thinking")
    private String thinking;

    @ApiModelProperty(value = "答案")
    @TableField("answer")
    private String answer;

    @ApiModelProperty(value = "答案tokens")
    @TableField("answer_tokens")
    private BigDecimal answerTokens;

    @ApiModelProperty(value = "答案单价")
    @TableField("answer_unit_price")
    private BigDecimal answerUnitPrice;

    @ApiModelProperty(value = "响应延迟")
    @TableField("provider_response_latency")
    private Double providerResponseLatency;

    @ApiModelProperty(value = "总价格")
    @TableField("total_price")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "币种")
    @TableField("currency")
    private String currency;

    @ApiModelProperty(value = "来源")
    @TableField("from_source")
    private String fromSource;

    @ApiModelProperty(value = "调用来源")
    @TableField("invoke_from")
    private String invokeFrom;

    @ApiModelProperty(value = "来源用户id (临时用户等)")
    @TableField("from_end_user_id")
    private String fromEndUserId;

    @ApiModelProperty(value = "来源账号id")
    @TableField("from_account_id")
    private String fromAccountId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;


}