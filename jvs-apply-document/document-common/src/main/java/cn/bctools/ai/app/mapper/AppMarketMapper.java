package cn.bctools.ai.app.mapper;

import cn.bctools.ai.app.entity.data.AppMarket;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarketFilter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 应用市场表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface AppMarketMapper extends BaseMapper<AppMarket> {

    AppMarkerListVO getAppInfo(String id);

    IPage<AppMarkerListVO> getList(Page<AppMarkerListVO> page, AppMarketFilter params);

}
