package cn.bctools.ai.app.mapper;

import cn.bctools.ai.app.entity.data.AppPublicCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 上架应用关联的应用类别 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface AppPublicCategoryMapper extends BaseMapper<AppPublicCategory> {

}
