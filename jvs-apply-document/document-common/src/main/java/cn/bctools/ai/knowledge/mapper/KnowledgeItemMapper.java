package cn.bctools.ai.knowledge.mapper;

import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.enums.DocForm;
import cn.bctools.ai.knowledge.entity.vo.KnowDocumentCountVo;
import cn.bctools.ai.knowledge.entity.vo.KnowWordCountSumVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 知识库-条目 | Knowledge Base Item Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface KnowledgeItemMapper extends BaseMapper<KnowledgeItem> {

    @Select(value = "select max(position) from ai_knowledge_item where know_id = #{knowledgeId} and del_flag = 0")
    Integer maxPosition(@Param(value = "knowledgeId") String knowledgeId);

    @Select(value = "select sum(word_count) from ai_knowledge_item where know_id = #{knowledgeId} and del_flag = 0")
    Integer getKnowWordCount(@Param(value = "knowledgeId") String knowledgeId);

    @Select("""
            <script>
            select know_id,count(*) as count
            from ai_knowledge_item 
            where know_id in 
                <foreach item="id" collection="knowIds" open="(" separator="," close=")">
                  #{id}
                </foreach>
                and del_flag = 0
            group by know_id
            </script>
            """)
    List<KnowDocumentCountVo> getKnowDocumentCounts(@Param("knowIds") List<String> knowIds);

    @Select("""
            <script>
            select know_id,sum(word_count) as word_count_sum
            from ai_knowledge_item 
            where know_id in 
                <foreach item="id" collection="knowIds" open="(" separator="," close=")">
                  #{id}
                </foreach>
                and del_flag = 0
            group by know_id
            </script>
            """)
    List<KnowWordCountSumVo> getKnowWordCounts(List<String> knowIds);

    @Select("""
            <script>
             SELECT know_id,doc_form
                FROM (
                    SELECT know_id,doc_form,
                           ROW_NUMBER() OVER (PARTITION BY know_id ORDER BY update_time DESC) AS rn
                    FROM ai_knowledge_item
                    WHERE know_id IN 
                        <foreach item="id" collection="knowIds" open="(" separator="," close=")">
                          #{id}
                        </foreach>
                    AND doc_form IS NOT NULL
                    and del_flag = 0
                ) t
                WHERE rn = 1
             </script>
            """)
    List<KnowledgeItem> getLatestDocForm(@Param("knowIds") List<String> knowIds);

    //i->ai_knowledge_item      s->ai_item_segments
    @Select("""
            <script>
            select i.*
                <if test='connect'> , ifnull(s.total_hit_count,0) as total_hit_count </if>
            from ai_knowledge_item i 
                <if test='connect'>
                    left join (
                        select ki_id,sum(hit_count) as total_hit_count
                        from ai_item_segments
                        group by ki_id
                    ) s on i.id = s.ki_id
                </if>
            where ${ew.sqlSegment}
            </script>
            """)
    Page<KnowledgeItem> getDocPage(@Param("page") Page<KnowledgeItem> page,
                                   @Param("ew") QueryWrapper<KnowledgeItem> query
            , @Param("connect") boolean connect);

    //i->ai_knowledge_item      s->ai_item_segments
    @Select("""
            <script>
            select i.id,i.tenant_id,i.dc_id,i.know_id,i.bucket_name,i.file_path,i.original_name,i.name,i.position
              ,i.word_count,i.tokens,i.indexing_status,i.mode,i.doc_form,i.doc_language,i.enabled,i.is_paused,
               i.is_archived
                <if test='connect'> , ifnull(s.total_hit_count,0) as total_hit_count </if>
            from ai_knowledge_item i 
                <if test='connect'>
                    left join (
                        select ki_id,sum(hit_count) as total_hit_count
                        from ai_item_segments
                        group by ki_id
                    ) s on i.id = s.ki_id
                </if>
            where ${ew.sqlSegment}
            </script>
            """)
    Page<KnowledgeItem> getPublicDocPage(@Param("page") Page<KnowledgeItem> page,
                                         @Param("ew") QueryWrapper<KnowledgeItem> query
            , @Param("connect") boolean connect);

    @Select("""
            <script>
            select know_id,count(*) as count
            from ai_knowledge_item 
            where know_id in 
                <foreach item="id" collection="knowIds" open="(" separator="," close=")">
                  #{id}
                </foreach>
                and indexing_status = 'completed'
                and enabled = 1
                and is_archived = 0
                and del_flag = 0
            group by know_id
            </script>
            """)
    List<KnowDocumentCountVo> getAvailableDocumentCounts(@Param("knowIds") List<String> knowIds);
}