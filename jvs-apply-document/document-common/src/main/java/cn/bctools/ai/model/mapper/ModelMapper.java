package cn.bctools.ai.model.mapper;

import cn.bctools.ai.model.entity.data.Model;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 模型表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface ModelMapper extends BaseMapper<Model> {

}