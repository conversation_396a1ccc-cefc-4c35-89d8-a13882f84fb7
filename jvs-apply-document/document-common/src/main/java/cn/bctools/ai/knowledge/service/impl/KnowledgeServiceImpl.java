package cn.bctools.ai.knowledge.service.impl;

import cn.bctools.ai.knowledge.core.context.KnowContext;
import cn.bctools.ai.knowledge.core.eventlistener.event.BeforeDeleteKnowEvent;
import cn.bctools.ai.knowledge.core.eventlistener.event.DealKnowVectorIndexEvent;
import cn.bctools.ai.knowledge.core.rag.IndexRunner;
import cn.bctools.ai.knowledge.entity.bean.document.ExtractSetting;
import cn.bctools.ai.knowledge.entity.bean.document.IndexingEstimateDetail;
import cn.bctools.ai.knowledge.entity.bean.knowledge.ExternalKnowledgeInfo;
import cn.bctools.ai.knowledge.entity.bean.knowledge.RetrievalSetting;
import cn.bctools.ai.knowledge.entity.bean.processrule.ProcessRule;
import cn.bctools.ai.knowledge.entity.constant.IndexingTechnique;
import cn.bctools.ai.knowledge.entity.constant.MetaBuiltInField;
import cn.bctools.ai.knowledge.entity.constant.RKey;
import cn.bctools.ai.knowledge.entity.data.*;
import cn.bctools.ai.knowledge.entity.enums.DocForm;
import cn.bctools.ai.knowledge.entity.enums.ItemProcessAction;
import cn.bctools.ai.knowledge.entity.enums.ProcessStatus;
import cn.bctools.ai.knowledge.entity.enums.StatusAction;
import cn.bctools.ai.knowledge.entity.vo.*;
import cn.bctools.ai.knowledge.mapper.KnowledgeMapper;
import cn.bctools.ai.knowledge.service.*;
import cn.bctools.ai.tag.entity.data.TagBingings;
import cn.bctools.ai.tag.entity.data.Tags;
import cn.bctools.ai.tag.enums.TagType;
import cn.bctools.ai.tag.service.TagBingingsService;
import cn.bctools.ai.tag.service.TagsService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.document.entity.DcLibrary;
import cn.bctools.document.service.DcLibraryService;
import cn.bctools.model.ModelInstance;
import cn.bctools.model.entity.data.TenantDefaultModels;
import cn.bctools.model.model_runtime.enums.ModelType;
import cn.bctools.model.model_runtime.model_providers.ModelProviderFactory;
import cn.bctools.model.service.TenantDefaultModelsService;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.dto.BaseFile;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.*;


/**
 * <p>
 * 知识库 | Knowledge Base 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Slf4j
@Service
@AllArgsConstructor
public class KnowledgeServiceImpl extends ServiceImpl<KnowledgeMapper, Knowledge> implements KnowledgeService {

    private final ExternalKnowledgeApisService externalKnowledgeApisService;
    private final ExternalKnowledgeBindingsService externalKnowledgeBindingsService;
    private final KnowledgeItemService itemService;
    private final ItemSegmentsService segmentsService;
    private final AppKnowledgeJoinsService appKnowledgeJoinsService;
    private final TagsService tagsService;
    private final TagBingingsService tagBingingsService;
    private final ApplicationContext applicationContext;
    private final IndexRunner indexRunner;
    private final KnowledgeQueriesService queriesService;
    private final KnowledgeAuthService authService;
    private final KnowledgePublicService publicService;
    private final DcLibraryService dcLibraryService;
    private final KnowledgeLogService logService;
    private final KnowMetadataService metadataService;
    private final RedisUtils redisUtils;
    private final TenantDefaultModelsService defaultModelsService;

    @Override
    public Page<KnowDetailRespVo> searchMine(Page<Knowledge> page, String keyword, List<String> tagIds) {
        Page<KnowDetailRespVo> res = new Page<>(page.getCurrent(), page.getSize(), 0);

        LambdaQueryChainWrapper<Knowledge> pageWrapper = this.getPageWrapper(keyword, tagIds);
        if (Objects.isNull(pageWrapper)) {
            return res;
        }

        pageWrapper.eq(Knowledge::getCreateById, UserCurrentUtils.getUserId());

        pageWrapper.page(page);
        if (ObjectNull.isNull(page.getRecords())) {
            return res;
        }

        List<String> knowIds = page.getRecords().stream().map(Knowledge::getId).toList();

        //共享知识库
        List<String> shareKnowIds = authService.getShareKnowIds(knowIds);
        //公开知识库
        List<String> publicKnowIds = publicService.getPublicKnowIds(knowIds);
        List<KnowDetailRespVo> list = this.handlePageData(page.getRecords(), false);
        for (KnowDetailRespVo respVo : list) {
            //设置是否共享
            respVo.setIsShare(shareKnowIds.contains(respVo.getId()));

            //设置是否公开
            respVo.setIsPublic(publicKnowIds.contains(respVo.getId()));
        }
        res.setRecords(list);
        res.setTotal(page.getTotal());
        return res;
    }

    @Override
    public Page<KnowDetailRespVo> searchShare(Page<Knowledge> page, String keyword, List<String> tagIds) {
        Page<KnowDetailRespVo> res = new Page<>(page.getCurrent(), page.getSize(), 0);

        LambdaQueryChainWrapper<Knowledge> pageWrapper = this.getPageWrapper(keyword, tagIds);

        if (Objects.isNull(pageWrapper)) {
            return res;
        }

        //获取我有权限的知识库(共享给当前用户的)
        List<String> shareKnowIds = authService.getUserPermittedKnowIds(UserCurrentUtils.getUserId());
        if (ObjectNull.isNull(shareKnowIds)) {
            return res;
        }
        pageWrapper.in(Knowledge::getId, shareKnowIds);

        pageWrapper.page(page);

        if (ObjectNull.isNull(page.getRecords())) {
            return res;
        }

        List<KnowDetailRespVo> list = this.handlePageData(page.getRecords(), false);
        res.setRecords(list);
        res.setTotal(page.getTotal());

        return res;
    }

    @Override
    public Page<KnowDetailRespVo> searchPublic(Page<Knowledge> page, String keyword, List<String> tagIds) {
        Page<KnowDetailRespVo> res = new Page<>(page.getCurrent(), page.getSize(), 0);

        LambdaQueryChainWrapper<Knowledge> pageWrapper = this.getPageWrapper(keyword, tagIds);

        if (Objects.isNull(pageWrapper)) {
            return res;
        }

        pageWrapper.page(page);

        if (ObjectNull.isNull(page.getRecords())) {
            return res;
        }

        List<KnowDetailRespVo> list = this.handlePageData(page.getRecords(), true);
        res.setRecords(list);
        res.setTotal(page.getTotal());

        return res;
    }

    //处理分页数据
    private List<KnowDetailRespVo> handlePageData(List<Knowledge> knowledgeList, Boolean isPublic) {
        List<String> knowIds = knowledgeList.stream().map(Knowledge::getId).toList();

        // 分别采用单次查询获取，减少sql查询次数
        //内容形式
        Map<String, DocForm> latestDocForms = itemService.getLatestDocForms(knowIds);
        //文档数
        Map<String, Integer> knowDocumentCounts = itemService.getKnowDocumentCounts(knowIds);
        //字符数
        Map<String, Integer> knowWordCounts = itemService.getKnowWordCounts(knowIds);
        //关联应用数
        Map<String, Integer> knowAppCounts = appKnowledgeJoinsService.getKnowAppCounts(knowIds);
        //标签
        Map<String, List<Tags>> knowTags = this.getKnowTags(knowIds);

        List<KnowDetailRespVo> list = BeanCopyUtil.copys(knowledgeList, KnowDetailRespVo.class);
        for (KnowDetailRespVo respVo : list) {
            respVo.setDocForm(latestDocForms.getOrDefault(respVo.getId(), null));
            respVo.setDocumentCount(knowDocumentCounts.getOrDefault(respVo.getId(), 0));
            respVo.setWordCount(knowWordCounts.getOrDefault(respVo.getId(), 0));
            respVo.setAppCount(knowAppCounts.getOrDefault(respVo.getId(), 0));
            respVo.setTags(knowTags.getOrDefault(respVo.getId(), new ArrayList<>()));

            //非公开返回设置信息
            if (!isPublic) {
                //设置检索模型
                if (Objects.isNull(respVo.getRetrievalModel())) {
                    respVo.setRetrievalModel(RetrievalSetting.DEFAULT_SETTING);

                    //设置外部检索模型
                    RetrievalSetting externalRetrievalModel = new RetrievalSetting();
                    externalRetrievalModel.setTopK(2);
                    externalRetrievalModel.setScoreThreshold(0.0F);
                    respVo.setExternalRetrievalModel(externalRetrievalModel);
                } else {
                    respVo.setExternalRetrievalModel(respVo.getRetrievalModel());
                }

                //设置外部知识库信息
                respVo.setExternalKnowledgeInfo(this.externalKnowledgeInfo(respVo));

                if (IndexingTechnique.isHighQuality(respVo.getIndexingTechnique())) {
                    //todo 检验当前知识库embedding模型配置是否在系统可用embedding模型中

                } else {
                    respVo.setEmbeddingAvailable(true);
                }
                respVo.setEmbeddingAvailable(true);

            }
        }

        return list;
    }

    //返回空则表示不可能查到数据
    private LambdaQueryChainWrapper<Knowledge> getPageWrapper(String keyword, List<String> tagIds) {
        LambdaQueryChainWrapper<Knowledge> wrapper = this.lambdaQuery();

        //根据标签查询
        if (ObjectNull.isNotNull(tagIds)) {
            List<String> tagBindKnowIds = this.getTagBindKnowIds(tagIds);
            if (ObjectNull.isNull(tagBindKnowIds)) {
                return null;
            }
            wrapper.in(Knowledge::getId, tagBindKnowIds);
        }

        //关键字查询
        if (StrUtil.isNotEmpty(keyword)) {
            wrapper.and(e -> e
                    .like(Knowledge::getName, keyword)).or().like(Knowledge::getDescription, keyword);
        }

        //默认按照创建时间降序
        wrapper.orderByDesc(Knowledge::getCreateTime);

        return wrapper;
    }


    @Override
    public Knowledge create(Knowledge knowledge) {
        save(knowledge);
        return getById(knowledge.getId());
    }

    @Override
    public Knowledge createEmptyKnow(CreateEmptyKnowReqVo vo) {
        if (StrUtil.isEmpty(vo.getProvider())) {
            vo.setProvider("vendor");
        }

        Knowledge knowledge = BeanCopyUtil.copy(vo, Knowledge.class);

        if (IndexingTechnique.isHighQuality(vo.getIndexingTechnique())) {
            TenantDefaultModels defaultModel = defaultModelsService.getDefaultModel(ModelType.TEXT_EMBEDDING);
            if (Objects.nonNull(defaultModel)) {
                knowledge.setEmbeddingModelProvider(defaultModel.getProviderName());
                knowledge.setEmbeddingModel(defaultModel.getModelName());
            }
        }

        this.save(knowledge);

        if (Objects.equals(vo.getProvider(), "external") && Objects.nonNull(vo.getExternalKnowledgeApiId())) {
            //对接外部知识库api
            externalKnowledgeApisService
                    .getExistById(vo.getExternalKnowledgeApiId());
            ExternalKnowledgeBindings bindings = new ExternalKnowledgeBindings();
            bindings.setKnowId(knowledge.getId());
            bindings.setExternalKnowledgeApiId(vo.getExternalKnowledgeApiId());
            bindings.setExternalKnowledgeId(vo.getExternalKnowledgeId());
            externalKnowledgeBindingsService.save(bindings);
        }

        return knowledge;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Knowledge update(String knowId, KnowledgeUpdateReqVo vo) {
        Knowledge knowledge = KnowContext.getKnowledge();
        vo.setId(knowId);
        //外部知识库修改
        if (knowledge.isExternal()) {
            RetrievalSetting externalRetrievalModel = vo.getExternalRetrievalModel();
            if (Objects.nonNull(externalRetrievalModel)) {
                knowledge.setRetrievalModel(externalRetrievalModel);
            }
            if (StrUtil.isNotEmpty(vo.getName())) {
                knowledge.setName(vo.getName());
            }
            knowledge.setDescription(vo.getDescription());

            if (Objects.isNull(vo.getExternalKnowledgeId())) {
                throw new BusinessException("更新外部知识库请传入外部知识库id");
            }

            if (Objects.isNull(vo.getExternalKnowledgeApiId())) {
                throw new BusinessException("更新外部知识库请传入外部知识库信息id");
            }

            ExternalKnowledgeBindings bindings = externalKnowledgeBindingsService.lambdaQuery()
                    .eq(ExternalKnowledgeBindings::getKnowId, vo.getExternalKnowledgeId())
                    .last("limit 1")
                    .one();
            if (Objects.isNull(bindings)) {
                throw new BusinessException("知识库与外部知识库绑定信息不存在");
            }

            this.updateById(knowledge);

            if (!Objects.equals(bindings.getExternalKnowledgeApiId(), vo.getExternalKnowledgeApiId())
                    || !Objects.equals(bindings.getExternalKnowledgeId(), vo.getExternalKnowledgeId())) {
                bindings.setExternalKnowledgeId(vo.getExternalKnowledgeId());
                bindings.setExternalKnowledgeApiId(vo.getExternalKnowledgeApiId());
                externalKnowledgeBindingsService.save(bindings);
            }
        } else {
            if (StrUtil.isEmpty(vo.getName())) {
                throw new BusinessException("名称不能为空");
            }
            BeanUtil.copyProperties(vo, knowledge);
            ItemProcessAction action = null;
            if (!Objects.equals(knowledge.getIndexingTechnique(), vo.getIndexingTechnique())) {
                if (Objects.equals(IndexingTechnique.ECONOMY, vo.getIndexingTechnique())) {
                    action = ItemProcessAction.REMOVE;
                    knowledge.setEmbeddingModel("");
                    knowledge.setEmbeddingModelProvider("");
                } else if (IndexingTechnique.isHighQuality(vo.getIndexingTechnique())) {
                    action = ItemProcessAction.ADD;
                    knowledge.setEmbeddingModelProvider(vo.getEmbeddingModelProvider());
                    knowledge.setEmbeddingModel(vo.getEmbeddingModel());

                }
            } else {
                if (!Objects.equals(vo.getEmbeddingModelProvider(), knowledge.getEmbeddingModelProvider())
                        || !Objects.equals(vo.getEmbeddingModel(), knowledge.getEmbeddingModel())) {
                    action = ItemProcessAction.UPDATE;

                    knowledge.setEmbeddingModelProvider(vo.getEmbeddingModelProvider());
                    knowledge.setEmbeddingModel(vo.getEmbeddingModel());

                }
            }
            this.updateById(knowledge);
            publicService.lambdaUpdate().eq(KnowledgePublic::getKnowId, knowId)
                    .set(KnowledgePublic::getName, knowledge.getName())
                    .set(KnowledgePublic::getDescription, knowledge.getDescription())
                    .update();
            if (Objects.nonNull(action)) {
                applicationContext.publishEvent(new DealKnowVectorIndexEvent(this, knowledge.getId(), action));
            }
        }

        return knowledge;
    }

    @SafeVarargs
    @Override
    public final Knowledge getExistById(String id, SFunction<Knowledge, ?>... columns) {
        Knowledge knowledge;
        if (ObjectNull.isNull((Object) columns)) {
            knowledge = this.lambdaQuery().eq(Knowledge::getId, id)
                    .select(columns)
                    .last("limit 1").one();
        } else {
            knowledge = getById(id);
        }
        return Optional.ofNullable(knowledge).orElseThrow(() -> new BusinessException("知识库不存在"));
    }

    @Override
    public void delete(String knowId) {
        Knowledge knowledge = this.getExistById(knowId);

        //删除关联数据
        this.applicationContext.publishEvent(new BeforeDeleteKnowEvent(this, knowledge));

        this.removeById(knowId);
    }


    @Override
    public void checkKnowModelSetting(Knowledge knowledge) {
        if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique())) {
            new ModelInstance(knowledge.getEmbeddingModelProvider(),
                    knowledge.getEmbeddingModel(),
                    ModelType.TEXT_EMBEDDING);
        }
    }

    @Override
    public void checkEmbeddingModelSetting(String embeddingModelProvider, String embeddingModel) {
        //检验是否有对应设置的embedding模型
        new ModelInstance(embeddingModelProvider,
                embeddingModel,
                ModelType.TEXT_EMBEDDING);
    }

    @Override
    public KnowDetailRespVo getDetail(String knowId) {
        Knowledge knowledge = KnowContext.getKnowledge();
        KnowDetailRespVo res = BeanCopyUtil.copy(knowledge, KnowDetailRespVo.class);
        //docForm
        res.setDocForm(itemService.getLatestDocForm(knowId));
        //关联app数量
        res.setAppCount(appKnowledgeJoinsService.getKnowAppCount(knowId));
        //文档数量
        res.setDocumentCount(itemService.getKnowDocumentCount(knowId));
        //字符数量
        res.setAppCount(itemService.getKnowWordCount(knowId));
        //标签
        res.setTags(this.getKnowTags(knowId));
        //设置检索模型
        if (Objects.isNull(res.getRetrievalModel())) {
            res.setRetrievalModel(RetrievalSetting.DEFAULT_SETTING);

            //设置外部检索模型
            RetrievalSetting externalRetrievalModel = new RetrievalSetting();
            externalRetrievalModel.setTopK(2);
            externalRetrievalModel.setScoreThreshold(0.0F);
            res.setExternalRetrievalModel(externalRetrievalModel);
        } else {
            res.setExternalRetrievalModel(res.getRetrievalModel());
        }

        //设置外部知识库信息
        res.setExternalKnowledgeInfo(this.externalKnowledgeInfo(knowledge));

        //todo 检验当前知识库embedding模型配置是否在系统可用embedding模型中
        if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique())) {

        }
        res.setEmbeddingAvailable(true);
        return res;
    }

    //获取包含对应标签的知识库id数组
    private List<String> getTagBindKnowIds(List<String> tagIds) {
        if (ObjectNull.isNull(tagIds)) {
            return new ArrayList<>();
        }
        return tagBingingsService.lambdaQuery()
                .in(TagBingings::getTagId, tagIds)
                .select(TagBingings::getTargetId).list()
                .stream().map(TagBingings::getTargetId).toList();
    }

    //获取知识库标签信息
    private List<Tags> getKnowTags(String knowId) {
        List<String> tagIds = tagBingingsService.lambdaQuery().select(TagBingings::getTagId)
                .eq(TagBingings::getTargetId, knowId).list()
                .stream().map(TagBingings::getTagId).toList();

        if (ObjectNull.isNull(tagIds)) {
            return List.of();
        }
        List<Tags> list = tagsService.lambdaQuery().eq(Tags::getType, TagType.KNOWLEDGE.value)
                .in(Tags::getId, tagIds).select(Tags::getId, Tags::getName, Tags::getType).list();
        return list;
    }

    //获取知识库与标签列表映射map
    private Map<String, List<Tags>> getKnowTags(List<String> knowIds) {
        if (ObjectNull.isNull(knowIds)) {
            return Map.of();
        }
        Map<String, List<Tags>> res = new HashMap<>();
        knowIds.forEach(e -> res.put(e, getKnowTags(e)));
        return res;
    }

    private Integer getAvailableSegmentCount(String knowId) {
        Long count = segmentsService.lambdaQuery()
                .eq(ItemSegment::getStatus, ProcessStatus.COMPLETED)
                .eq(ItemSegment::getEnabled, true)
                .eq(ItemSegment::getKiId, knowId).count();
        return count.intValue();
    }

    private Integer getAvailableDocumentCount(String knowId) {
        Long count = itemService.lambdaQuery()
                .eq(KnowledgeItem::getEnabled, true)
                .eq(KnowledgeItem::getIndexingStatus, ProcessStatus.COMPLETED)
                .eq(KnowledgeItem::getKnowId, knowId).count();
        return count.intValue();
    }

    private ExternalKnowledgeInfo externalKnowledgeInfo(Knowledge knowledge) {
        if (!knowledge.isExternal()) {
            return null;
        }

        ExternalKnowledgeBindings bindings = externalKnowledgeBindingsService.lambdaQuery()
                .eq(ExternalKnowledgeBindings::getKnowId, knowledge.getId())
                .last("limit 1").one();

        if (Objects.isNull(bindings)) {
            return null;
        }

        ExternalKnowledgeApis api = externalKnowledgeApisService.lambdaQuery()
                .eq(ExternalKnowledgeApis::getId, bindings.getExternalKnowledgeApiId())
                .last("limit 1").one();
        if (Objects.isNull(api)) {
            return null;
        }

        ExternalKnowledgeInfo res = new ExternalKnowledgeInfo();
        res.setExternalKnowledgeId(bindings.getExternalKnowledgeId());
        res.setExternalKnowledgeApiId(api.getId());
        res.setExternalKnowledgeName(api.getName());
        res.setExternalKnowledgeApiEndpoint(Optional.ofNullable(JSON.parseObject(api.getSettings()))
                .map(e -> e.getString("endpoint")).orElse(""));
        return res;
    }

    @Override
    public IndexingEstimateDetail indexingEstimate(KnowEstimateUploadVo vo) {
        this.estimateArgsValidate(vo);
        List<ExtractSetting> extractSettings = new ArrayList<>();
        if (ObjectNull.isNotNull(vo.getItemIds())) {
            //获取文件信息
            List<KnowledgeItem> items = itemService.lambdaQuery().in(KnowledgeItem::getId, vo.getItemIds()).list();
            for (KnowledgeItem item : items) {
                ExtractSetting setting = new ExtractSetting();
                BaseFile baseFile = item.extractFileInfo();
                setting.setSourceInfo(baseFile);
                setting.setDocForm(vo.getDocForm());
                extractSettings.add(setting);
            }
        } else if (ObjectNull.isNotNull(vo.getFiles())) {
            for (BaseFile file : vo.getFiles()) {
                ExtractSetting setting = new ExtractSetting();
                setting.setSourceInfo(file);
                setting.setDocForm(vo.getDocForm());
                extractSettings.add(setting);
            }
        } else if (ObjectNull.isNotNull(vo.getDcIds())) {
            dcLibraryService.lambdaQuery().in(DcLibrary::getId, vo.getDcIds())
                    .isNotNull(DcLibrary::getNameSuffix)
                    .select(DcLibrary::getId, DcLibrary::getBucketName, DcLibrary::getFilePath)
                    .list().forEach(e -> {
                        ExtractSetting setting = new ExtractSetting();
                        BaseFile baseFile = new BaseFile();
                        baseFile.setBucketName(e.getBucketName());
                        baseFile.setFileName(e.getFilePath());
                        baseFile.setOriginalName(e.getName());
                        setting.setDocForm(vo.getDocForm());
                        setting.setSourceInfo(baseFile);
                        extractSettings.add(setting);
                    });

        } else {
            throw new BusinessException("请传入文档关联文件信息");
        }
        IndexingEstimateDetail detail = indexRunner.indexingEstimate(extractSettings, vo.getProcessRule(), vo.getDocForm(),
                vo.getDocLanguage(), vo.getKnowId(), vo.getIndexingTechnique());
        return detail;
    }

    //检验模拟分段参数
    private void estimateArgsValidate(KnowEstimateUploadVo vo) {
        if (ObjectNull.isNull(vo.getItemIds()) && ObjectNull.isNull(vo.getFiles()) && ObjectNull.isNull(vo.getDcIds())) {
            throw new BusinessException("请传入文档id数组或文件信息数组");
        }

        //检验处理规则
        ProcessRule.validate(vo.getProcessRule());

    }

    @Override
    public void getQueryPage(String knowId, Page<KnowledgeQuery> page) {
        queriesService.lambdaQuery().eq(KnowledgeQuery::getKnowId, knowId)
                .orderByDesc(KnowledgeQuery::getCreateTime)
                .page(page);
    }

    @Override
    public void logPage(String knowId,Page<KnowledgeLog> page) {
        logService.lambdaQuery().eq(KnowledgeLog::getKnowId,knowId).orderByDesc(KnowledgeLog::getOperTime).page(page);
//        if (ObjectNull.isNull(page.getRecords())) {
//            return;
//        }
//        List<String> itemIds = page.getRecords().stream()
//                .filter(e -> ObjectNull.isNotNull(e.getKiIds()))
//                .flatMap(e -> e.getKiIds().stream()).toList();
//        if (ObjectNull.isNull(itemIds)) {
//            return;
//        }
//        Map<String, String> itemNameMap = itemService.lambdaQuery()
//                .in(KnowledgeItem::getId, itemIds)
//                .select(KnowledgeItem::getId, KnowledgeItem::getName)
//                .list()
//                .stream()
//                .collect(Collectors.toMap(KnowledgeItem::getId, KnowledgeItem::getName));
//        page.getRecords().forEach(e -> {
//            if (ObjectNull.isNotNull(e.getKiIds())) {
//                List<String> docNameList = new ArrayList<>();
//                e.getKiIds().forEach(v -> docNameList.add(itemNameMap.get(v)));
//                Joiner joiner = Joiner.on(",").skipNulls();
//                e.setDocName(joiner.join(docNameList));
//            }
//        });
    }

    @Override
    public KnowMetaInfoVo getKnowMetas(Knowledge knowledge) {
        KnowMetaInfoVo vo = new KnowMetaInfoVo();
        List<KnowMetadata> knowMetas = metadataService.getKnowMetas(knowledge);
        vo.setDocMetadata(knowMetas);
        vo.setBuildInFieldEnabled(knowledge.getBuiltInFieldEnabled());
        return vo;
    }

    @Override
    public void onOff(Knowledge knowledge, StatusAction statusAction) {
        switch (statusAction) {
            case ENABLE -> this.enableBuiltInField(knowledge);
            case DISABLE -> this.disableBuiltInField(knowledge);
            default -> {
            }
        }
    }

    //启用内置元数据属性
    private void enableBuiltInField(Knowledge knowledge) {
        if (knowledge.getBuiltInFieldEnabled()) {
            return;
        }
        try {
            metadataService.checkMetadataLock(knowledge.getId(), null);
            this.lambdaUpdate().eq(Knowledge::getId, knowledge.getId())
                    .set(Knowledge::getBuiltInFieldEnabled, true).update();

            //生成内置属性值
            List<KnowledgeItem> items = itemService.getAvailableDocument(knowledge.getId());

            if (ObjectNull.isNotNull(items)) {
                for (KnowledgeItem item : items) {
                    JSONObject metadata = item.getDocMetadata();
                    if (Objects.isNull(item.getDocMetadata())) {
                        metadata = new JSONObject();
                    }
                    MetaBuiltInField.fillDocBuiltInMetas(metadata, item);
                    item.setDocMetadata(metadata);
                }
                itemService.updateBatchById(items);
            }

        } finally {
            redisUtils.del(RKey.METADATA_LOCK + knowledge.getId());
        }
    }

    //禁用内置元数据属性
    private void disableBuiltInField(Knowledge knowledge) {
        if (!knowledge.getBuiltInFieldEnabled()) {
            return;
        }
        try {
            metadataService.checkMetadataLock(knowledge.getId(), null);
            this.lambdaUpdate().eq(Knowledge::getId, knowledge.getId())
                    .set(Knowledge::getBuiltInFieldEnabled, false).update();

            //生成内置属性值
            List<KnowledgeItem> items = itemService.lambdaQuery()
                    .eq(KnowledgeItem::getKnowId, knowledge.getId())
                    .eq(KnowledgeItem::getIndexingStatus, ProcessStatus.COMPLETED)
                    .eq(KnowledgeItem::getEnabled, true)
                    .eq(KnowledgeItem::getIsArchived, false)
                    .isNotNull(KnowledgeItem::getDocMetadata)
                    .select(KnowledgeItem::getId, KnowledgeItem::getDocMetadata).list();

            if (ObjectNull.isNotNull(items)) {
                for (KnowledgeItem item : items) {
                    MetaBuiltInField.ALL.forEach(field -> {
                        item.getDocMetadata().remove(field);
                    });
                }
                itemService.updateBatchById(items);
            }

        } finally {
            redisUtils.del(RKey.METADATA_LOCK + knowledge.getId());
        }
    }


}