package cn.bctools.ai.stream.config;

import cn.bctools.ai.stream.constants.StreamConstants;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 流式执行配置属性
 * 将配置属性对象化，避免硬编码
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "stream.execution")
public class StreamExecutionProperties {
    
    /**
     * 默认输出类型
     */
    private String defaultOutputType = StreamConstants.StreamTypeCodes.CONSOLE;
    
    /**
     * 上下文过期时间（分钟）
     */
    private Integer contextExpireMinutes = 30;
    
    /**
     * 异步执行线程池大小
     */
    private Integer asyncPoolSize = 10;
    
    /**
     * 清理任务间隔（分钟）
     */
    private Integer cleanupIntervalMinutes = 5;
    
    /**
     * 默认超时时间（秒）
     */
    private Integer defaultTimeoutSeconds = StreamConstants.Defaults.DEFAULT_TIMEOUT_SECONDS;
    
    /**
     * 最大超时时间（秒）
     */
    private Integer maxTimeoutSeconds = StreamConstants.Defaults.MAX_TIMEOUT_SECONDS;
    
    /**
     * WebSocket配置
     */
    private WebSocketProperties websocket = new WebSocketProperties();
    
    /**
     * SSE配置
     */
    private SseProperties sse = new SseProperties();
    
    /**
     * Flux配置
     */
    private FluxProperties flux = new FluxProperties();
    
    /**
     * WebSocket配置属性
     */
    @Data
    public static class WebSocketProperties {
        /**
         * WebSocket路径
         */
        private String path = StreamConstants.Paths.WEBSOCKET_PREFIX + StreamConstants.Paths.WEBSOCKET_WORKFLOW;
        
        /**
         * 允许的源
         */
        private String[] allowedOrigins = {"*"};
        
        /**
         * 消息缓冲区大小
         */
        private Integer messageBufferSize = 8192;
        
        /**
         * 连接超时时间（毫秒）
         */
        private Long connectionTimeoutMs = StreamConstants.Defaults.DEFAULT_WEBSOCKET_TIMEOUT_MS;
    }
    
    /**
     * SSE配置属性
     */
    @Data
    public static class SseProperties {
        /**
         * 默认超时时间（毫秒）
         */
        private Long defaultTimeoutMs = StreamConstants.Defaults.DEFAULT_SSE_TIMEOUT_MS;
        
        /**
         * 最大超时时间（毫秒）
         */
        private Long maxTimeoutMs = 1800000L;
        
        /**
         * 是否启用心跳
         */
        private Boolean heartbeatEnabled = true;
        
        /**
         * 心跳间隔（毫秒）
         */
        private Long heartbeatIntervalMs = 30000L;
    }
    
    /**
     * Flux配置属性
     */
    @Data
    public static class FluxProperties {
        /**
         * 缓冲区大小
         */
        private Integer bufferSize = StreamConstants.Defaults.DEFAULT_FLUX_BUFFER_SIZE;
        
        /**
         * 背压策略
         */
        private String backpressureStrategy = "BUFFER";
        
        /**
         * 超时时间（秒）
         */
        private Integer timeoutSeconds = StreamConstants.Defaults.DEFAULT_FLUX_TIMEOUT_SECONDS;
        
        /**
         * 是否启用错误恢复
         */
        private Boolean errorRecoveryEnabled = true;
    }
}
