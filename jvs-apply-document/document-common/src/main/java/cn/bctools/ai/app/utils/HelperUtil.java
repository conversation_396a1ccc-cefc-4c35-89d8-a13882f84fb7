package cn.bctools.ai.app.utils;

import cn.bctools.ai.app.entity.vo.api.StreamingResponseVO;
import cn.bctools.web.utils.IpUtil;
import cn.bctools.web.utils.WebUtils;
import dev.langchain4j.internal.Json;
import dev.langchain4j.model.openai.internal.chat.ChatCompletionChoice;
import dev.langchain4j.model.openai.internal.chat.ChatCompletionResponse;
import dev.langchain4j.model.openai.internal.chat.Delta;

import java.util.List;
import java.util.Random;

public class HelperUtil {

    public static String generateString(int n) {
        Random random = new Random();

        String LETTERS_DIGITS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder result = new StringBuilder(n);
        for (int i = 0; i < n; i++) {
            result.append(LETTERS_DIGITS.charAt(random.nextInt(LETTERS_DIGITS.length())));
        }
        return result.toString();
    }

    public static StreamingResponseVO getChatCompletionResponseContent(String partialResponse, String conversationId, String messageId, String event) {

        StreamingResponseVO s1 = new StreamingResponseVO();
        s1.setEvent(event);
        s1.setConversation_id(conversationId);
        s1.setMessage_id(messageId);

        String answer = "";
        if (null == partialResponse) {
            return s1;
        }
        ChatCompletionResponse response = Json.fromJson(partialResponse, ChatCompletionResponse.class);
        if (response != null) {
            List<ChatCompletionChoice> choices = response.choices();
            if (choices != null && !choices.isEmpty()) {
                ChatCompletionChoice chatCompletionChoice = choices.get(0);
                if (chatCompletionChoice != null) {
                    Delta delta = chatCompletionChoice.delta();
                    if (delta != null) {
                        if (delta.reasoningContent() != null) {
                            s1.setThinking(delta.reasoningContent());
                        } else if (delta.content() != null) {
                            s1.setAnswer(delta.content());
                        }
                    }
                }
            }
        }
//        System.out.println("partialResponse:\n " + response.choices());
        return s1;
    }


    public static String getIp() {
        return IpUtil.getIpAddr(WebUtils.getRequest());
    }

    public static int compareVersions(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");

        int length = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < length; i++) {
            int part1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int part2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;

            if (part1 < part2) {
                return -1;
            } else if (part1 > part2) {
                return 1;
            }
        }
        return 0;
    }

}
