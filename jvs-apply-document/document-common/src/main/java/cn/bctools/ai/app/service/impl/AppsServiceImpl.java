package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.entity.DefaultConfig;
import cn.bctools.ai.app.entity.data.AppPublic;
import cn.bctools.ai.app.entity.dto.SiteDTO;
import cn.bctools.ai.app.entity.vo.*;
import cn.bctools.ai.app.events.AppWasCreatedEvent;
import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.data.AppModelConfigs;
import cn.bctools.ai.app.entity.data.Apps;
import cn.bctools.ai.app.entity.data.Sites;
import cn.bctools.ai.app.entity.dto.ModelConfigDTO;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.mapper.AppsMapper;
import cn.bctools.ai.app.service.AppModelConfigsService;
import cn.bctools.ai.app.service.AppPublicService;
import cn.bctools.ai.app.service.SitesService;
import cn.bctools.ai.app.service.AppsService;
import cn.bctools.ai.app.utils.AppModelConfigUtil;
import cn.bctools.ai.app.utils.HelperUtil;
import cn.bctools.ai.tag.entity.data.TagBingings;
import cn.bctools.ai.tag.entity.data.Tags;
import cn.bctools.ai.tag.service.TagBingingsService;
import cn.bctools.ai.tag.service.TagsService;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.JvsAppSecretUtils;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.database.util.IdGenerator;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.design.rule.entity.RuleType;
import cn.bctools.design.rule.service.RuleDesignService;
import cn.bctools.model.ModelInstance;
import cn.bctools.model.entity.vo.VoiceVO;
import cn.bctools.model.model_runtime.enums.ModelType;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.rule.utils.html.HtmlGraph;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.*;

/**
 * <p>
 * 应用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppsServiceImpl extends ServiceImpl<AppsMapper, Apps> implements AppsService {

    AppModelConfigsService appModelConfigsService;

    SitesService sitesService;

    ApplicationEventPublisher eventPublisher;

    TagsService tagsService;

    TagBingingsService tagBingingsService;

    OssTemplate ossTemplate;

    RuleDesignService ruleDesignService;

    @Override
    public IPage<AppListDetailVO> getAppList(Page<AppListDetailVO> page, AppListVO params) {

        params.setUserId(UserCurrentUtils.getUserId());
        IPage<AppListDetailVO> apps = baseMapper.getAppList(page, params);

        List<Tags> tags = tagsService.lambdaQuery().list();
        Map<String, Tags> tagMap = tags.stream().collect(java.util.stream.Collectors.toMap(Tags::getId, Tags -> Tags));

        apps.getRecords().forEach(app -> {
            app.setIconUrl(ossTemplate.fileLink(app.getIcon(), "edf-ai"));

            if (app.getTagIds() == null) {
                app.setTags(new ArrayList<>());
                return;
            }

            List<String> tagIds = List.of(app.getTagIds().split(","));
            app.setTags(tagIds.stream().map(tagMap::get).toList());
        });

        return apps;
    }

    public AppDetail getAppInfo(String appId) {

        Apps apps = getMyApp(appId);

        AppDetail appDetail = BeanCopyUtil.copy(apps, AppDetail.class);

        appDetail.setIconUrl(ossTemplate.fileLink(apps.getIcon(), "edf-ai"));

        if (apps.getMode().equals(AppMode.CHAT.value) || apps.getMode().equals(AppMode.COMPLETION.value)) {
            AppModelConfigs config = appModelConfigsService.getById(apps.getAppModelConfigId());
            if (config == null) {
                throw new RuntimeException("应用配置不存在");
            }

            ModelConfigDTO configDTO = AppModelConfigUtil.convert(config);
            appDetail.setModel_config(configDTO);
        }

        Sites site = sitesService.lambdaQuery().eq(Sites::getAppId, apps.getId()).one();
        if (null == site) {
            throw new RuntimeException("site is null");
        }
        String icon = site.getIcon();
        if (StringUtils.isBlank(icon)) {
            icon = appDetail.getIcon();
        }
        site.setIconUrl(ossTemplate.fileLink(icon, "edf-ai"));
        appDetail.setSites(site);

        return appDetail;

    }

    public AppDetail getAppInfoByConfigId(String appId, String configId) {

        Apps apps = getMyApp(appId);

        AppDetail appDetail = BeanCopyUtil.copy(apps, AppDetail.class);

        appDetail.setIconUrl(ossTemplate.fileLink(apps.getIcon(), "edf-ai"));

        if (apps.getMode().equals(AppMode.CHAT.value) || apps.getMode().equals(AppMode.COMPLETION.value)) {
            AppModelConfigs config = appModelConfigsService.getById(configId);
            if (config == null) {
                throw new RuntimeException("应用配置不存在");
            }

            ModelConfigDTO configDTO = AppModelConfigUtil.convert(config);
            appDetail.setModel_config(configDTO);
        }

        Sites site = sitesService.lambdaQuery().eq(Sites::getAppId, apps.getId()).one();
        if (null == site) {
            throw new RuntimeException("site is null");
        }
        String icon = site.getIcon();
        if (StringUtils.isBlank(icon)) {
            icon = appDetail.getIcon();
        }
        site.setIconUrl(ossTemplate.fileLink(icon, "edf-ai"));
        appDetail.setSites(site);

        return appDetail;

    }

    private Apps getMyApp(String appId) {
        Apps apps = lambdaQuery().eq(Apps::getId, appId).eq(Apps::getDelFlag, false).one();

        if (apps == null) {
            throw new RuntimeException("应用不存在");
        }

        return apps;
    }

    @Override
    public Apps create(AppInfoVO app) {
        UserDto currentUser=UserCurrentUtils.getCurrentUser();
        if(null==currentUser){
            throw new BusinessException("用户未登录");
        }
        if (app.getMode() == null) {
            throw new BusinessException("请选择应用模式");
        }

        Apps apps = new Apps();
        apps.setName(app.getName());
        apps.setMode(app.getMode().getValue());
        apps.setDescription(app.getDescription());
        if (app.getIcon() == null || app.getIcon().isEmpty()) {
            app.setIcon(DefaultConfig.icon);
        }
        apps.setIcon(app.getIcon());
        save(apps);
        //todo 读取默认模型配置
        if (app.getMode().equals(AppMode.CHAT)) {
            //添加初始化配置
            AppModelConfigs defaultConfigs = appModelConfigsService.addDefaultConfig(apps.getId());
            apps.setAppModelConfigId(defaultConfigs.getId());
            updateById(apps);
        } else if (app.getMode().equals(AppMode.WORKFLOW)) {

        } else if (app.getMode().equals(AppMode.ADVANCED_CHAT)) {

        } else if (app.getMode().equals(AppMode.COMPLETION)) {
            //添加初始化配置
            AppModelConfigs defaultConfigs = appModelConfigsService.addDefaultConfig(apps.getId());
            apps.setAppModelConfigId(defaultConfigs.getId());
            updateById(apps);
        } else if (app.getMode().equals(AppMode.AGENT_CHAT)) {
            throw new BusinessException("不支持的应用模式");
        } else {
            throw new BusinessException("不支持的应用模式");
        }
        //创建网站记录
        eventPublisher.publishEvent(new AppWasCreatedEvent(this, apps));
        return apps;
    }

    @Override
    public Apps edit(String appId, AppInfoVO app) {

        Apps apps = getMyApp(appId);

        if (!Objects.equals(apps.getCreateById(), UserCurrentUtils.getUserId())) {
            throw new RuntimeException("无权限修改应用");
        }

        apps.setName(app.getName());
        apps.setDescription(app.getDescription());
        if (app.getIcon() == null || app.getIcon().isEmpty()) {
            apps.setIcon(DefaultConfig.icon);
        }
        apps.setIcon(app.getIcon());
        updateById(apps);

        return apps;
    }

    @Override
    public Apps copy(String appId, CopyAppVO newApp) {
        Apps app = lambdaQuery().eq(Apps::getId, appId).one();

        if (app == null) {
            throw new RuntimeException("应用不存在");
        }

        app.setId(null);
        app.setName(newApp.getName());
        app.setIcon(newApp.getIcon());
        app.setUpdateTime(null);
        app.setCreateTime(null);
        app.setCreateById(UserCurrentUtils.getUserId());
        app.setCreateBy(UserCurrentUtils.getUserId());
        app.setUpdateBy(UserCurrentUtils.getUserId());
        app.setPublicId(null);
        app.setPublicId2(null);
        save(app);

        String targetConfigId = newApp.getConfigId();

        // 复制应用配置
        if (app.getMode().equals(AppMode.CHAT.value)) {
            if (targetConfigId == null) {
                targetConfigId = app.getAppModelConfigId();
            }
            AppModelConfigs newConfig = appModelConfigsService.copyConfig(targetConfigId);
            if (newConfig != null) {
                app.setAppModelConfigId(newConfig.getId());
                updateById(app);

                if (newApp.getSyncDataset() == null || newApp.getSyncDataset()) {
                    if (newConfig.getDataset_configs() != null && !newConfig.getDataset_configs().isEmpty()) {
                        newConfig.setDataset_configs("");
                        appModelConfigsService.updateById(newConfig);
                    }
                }

            }

            Sites site = sitesService.lambdaQuery().eq(Sites::getAppId, appId).one();
            site.setId(null);
            site.setAppId(app.getId());
            site.setTitle(app.getName());
            site.setIcon(app.getIcon());
            site.setCode(HelperUtil.generateString(16));
            site.setUpdateTime(null);
            site.setCreateTime(null);
            site.setCreateById(UserCurrentUtils.getUserId());
            site.setCreateBy(UserCurrentUtils.getUserId());
            site.setUpdateBy(UserCurrentUtils.getUserId());
            sitesService.save(site);
        }

        // 复制标签绑定
        if (newApp.getSyncLabel() == null || newApp.getSyncLabel()) {
            List<TagBingings> oldBindTags = tagBingingsService.lambdaQuery().eq(TagBingings::getTargetId, appId).list();
            if (oldBindTags != null && !oldBindTags.isEmpty()) {
                List<TagBingings> newBindTags = oldBindTags.stream().map(tag -> {
                    tag.setId(null);
                    tag.setTargetId(app.getId());
                    return tag;
                }).toList();
                tagBingingsService.saveOrUpdateBatch(newBindTags);
            }
        }

        return app;
    }


    @Override
    public boolean delete(String appId) {
        Apps apps = getMyApp(appId);

        if (!Objects.equals(apps.getCreateById(), UserCurrentUtils.getUserId())) {
            throw new RuntimeException("无权限修改应用");
        }

        if (StringUtils.isNotEmpty(apps.getPublishConfigId())) {
            throw new RuntimeException("删除已上架应用前，需先申请下架应用");
        }

        // todo 删除应用编排，网站配置，聊天记录

        return removeById(apps.getId());
    }


    @Override
    public AppModelConfigs addNewConfigVersion(String appId, ModelConfigDTO params) {

        if (params.getVersion() == null || params.getVersion().isEmpty()) {
            throw new RuntimeException("请输入版本号");
        }

        AppModelConfigs config = BeanCopyUtil.copy(params, AppModelConfigs.class);
        config.setAppId(appId);

        Apps app = AppDetailContext.getObject();

        if (!Objects.equals(app.getCreateById(), UserCurrentUtils.getUserId())) {
            throw new RuntimeException("无权限修改应用");
        }

        // 上一个版本id
        AppModelConfigs previousVersion = appModelConfigsService.getById(app.getAppModelConfigId());
        if (previousVersion != null) {
            config.setPreviousVersion(app.getAppModelConfigId());
            config.setSort(previousVersion.getSort() + 1);
            appModelConfigsService.save(config);
        }

        app.setAppModelConfigId(config.getId());
        this.updateById(app);
        AppDetailContext.clear();

        return config;


    }


    @Override
    public Sites updateSite(String appId, SiteDTO params) {

        Sites sites = sitesService.lambdaQuery().eq(Sites::getAppId, appId).one();


        if (sites == null) {
            return null;
        }

        String siteId = sites.getId();
        sites = BeanCopyUtil.copy(params, Sites.class);


        sitesService.updateById(sites);

        return sites;
    }

    @Override
    public Sites resetAccessToken(String appId) {

        Sites sites = sitesService.lambdaQuery().eq(Sites::getAppId, appId).one();

        if (sites == null) {
            return null;
        }

        sites.setCode(HelperUtil.generateString(16));
        sitesService.updateById(sites);

        return sites;
    }


    @Override
    public IPage<VersionInfo> getVersions(Page<AppModelConfigs> page, String appId) {

        AppDetail app = AppDetailContext.getObject();

        IPage<VersionInfo> versionInfoPage = appModelConfigsService.lambdaQuery().eq(AppModelConfigs::getAppId, appId).orderByDesc(AppModelConfigs::getCreateTime).page(page).convert(item -> {
            VersionInfo versionInfo = new VersionInfo();
            versionInfo.setId(item.getId());
            versionInfo.setVersion(item.getVersion());
            versionInfo.setPublicVersion(app.getPublicId() !=null && app.getPublicId().equals(item.getId()));
            versionInfo.setCurrentVersion(app.getAppModelConfigId().equals(item.getId()));
            versionInfo.setCreateTime(item.getCreateTime());
            versionInfo.setCreateBy(item.getCreateBy());
            return versionInfo;
        });


        return versionInfoPage;
    }

    public ModelConfigDTO getVersion(String appId, String version) {

        AppModelConfigs config = appModelConfigsService.lambdaQuery().eq(AppModelConfigs::getAppId, appId).eq(AppModelConfigs::getId, version).eq(AppModelConfigs::getDelFlag, false).one();

        if (config == null) {
            throw new RuntimeException("版本不存在");
        }

        return AppModelConfigUtil.convert(config);

    }

    public boolean updateSiteEnable(String appId, Boolean enable) {

        Apps app = getMyApp(appId);

        if (!Objects.equals(app.getCreateById(), UserCurrentUtils.getUserId())) {
            throw new RuntimeException("无权限修改应用");
        }

        app.setEnableSite(enable);

        return updateById(app);
    }

    public boolean updateApiEnable(String appId, Boolean enable) {

        Apps app = getMyApp(appId);

        if (!Objects.equals(app.getCreateById(), UserCurrentUtils.getUserId())) {
            throw new RuntimeException("无权限修改应用");
        }

        app.setEnableApi(enable);

        return updateById(app);
    }

    public List<VoiceVO> getVoices(String language) {
        ModelInstance instance = new ModelInstance(ModelType.TTS);
        return instance.getModelVoices(language);
    }

    public void recoverConfig(String appId, String version) {

        AppDetail app = AppDetailContext.getObject();

        if (app.getMode().equals(AppMode.CHAT.value)) {
            recoverChatConfig(app, version);
        }


    }

    // 恢复聊天助手的配置
    private void recoverChatConfig(AppDetail app, String version) {

        String appId = app.getId();

        AppModelConfigs config = appModelConfigsService.lambdaQuery().eq(AppModelConfigs::getAppId, appId).eq(AppModelConfigs::getId, version).one();

        if (config == null) {
            throw new RuntimeException("版本不存在");
        }

        String targetVersion = config.getVersion();

        // 上架版本
        String publicId = app.getPublicId();
        if (publicId != null) {
            AppPublicService appPublicService = SpringContextUtil.getBean(AppPublicService.class);
            AppPublic appPublic = appPublicService.lambdaQuery().eq(AppPublic::getId, publicId).one();
            String publicVersion = appPublic.getAppVersion();

            int compare = HelperUtil.compareVersions(publicVersion, targetVersion);

            if (compare < 1) {
                throw new RuntimeException("上架版本大于恢复版本，如需恢复，请先申请下架应用");
            }
        }


        app.setAppModelConfigId(version);
        this.updateById(app);

        // 删除大于当前的版本
        Integer sort = config.getSort();
        appModelConfigsService.lambdaUpdate()
                .eq(AppModelConfigs::getAppId, appId)
                .gt(AppModelConfigs::getSort, sort)
                .remove();

    }


}