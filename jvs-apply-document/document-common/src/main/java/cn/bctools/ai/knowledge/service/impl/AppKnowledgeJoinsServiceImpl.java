package cn.bctools.ai.knowledge.service.impl;

import cn.bctools.ai.knowledge.entity.data.AppKnowledgeJoins;
import cn.bctools.ai.knowledge.mapper.AppKnowledgeJoinsMapper;
import cn.bctools.ai.knowledge.service.AppKnowledgeJoinsService;
import cn.bctools.common.utils.ObjectNull;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 知识库-应用关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppKnowledgeJoinsServiceImpl extends ServiceImpl<AppKnowledgeJoinsMapper, AppKnowledgeJoins> implements AppKnowledgeJoinsService {

    @Override
    public Boolean knowUseCheck(String knowId) {
        return this.lambdaQuery().eq(AppKnowledgeJoins::getKnowId, knowId).count() > 0;
    }

    @Override
    public Integer getKnowAppCount(String knowId) {
        Long count = this.lambdaQuery().eq(AppKnowledgeJoins::getKnowId, knowId).count();
        return count.intValue();
    }

    @Override
    public Map<String,Integer> getKnowAppCounts(List<String> knowIds) {
        if (ObjectNull.isNull(knowIds)){
            return Map.of();
        }
        Map<String, Integer> knowAppCounts = baseMapper.getKnowAppCounts(knowIds);
        if (ObjectNull.isNull(knowAppCounts)){
            return Map.of();
        }
        return knowAppCounts;
    }
}