<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.knowledge.mapper.KnowledgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.knowledge.entity.data.Knowledge">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="title" property="name" />
        <result column="remark" property="description" />
        <result column="is_public" property="isPublic" />
        <result column="is_strict" property="isStrict" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_id" property="updateById" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, title, remark, is_public, is_strict, ingest_max_overlap, ingest_model_name, ingest_model_id, retrieve_max_results, retrieve_min_score, query_llm_temperature, star_count, item_count, embedding_count, del_flag, query_system_message, create_by, create_by_id, create_time, update_by, update_by_id, update_time
    </sql>

</mapper>