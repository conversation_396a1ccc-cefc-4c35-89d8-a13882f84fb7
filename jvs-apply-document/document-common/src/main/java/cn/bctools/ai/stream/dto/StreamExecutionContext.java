package cn.bctools.ai.stream.dto;

import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.common.entity.dto.UserDto;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.socket.WebSocketSession;
import reactor.core.publisher.FluxSink;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 流式执行上下文
 * 包含执行过程中需要的所有上下文信息
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class StreamExecutionContext {
    
    /**
     * 执行ID（唯一标识）
     */
    private String executionId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 规则密钥
     */
    private String ruleSecret;
    
    /**
     * 用户信息
     */
    private UserDto user;
    
    /**
     * HTTP请求对象
     */
    private HttpServletRequest request;
    
    /**
     * HTTP响应对象
     */
    private HttpServletResponse response;
    
    /**
     * 输入参数
     */
    private Map<String, Object> inputParams;
    
    /**
     * 流式输出类型
     */
    private StreamOutputType outputType;
    
    /**
     * 是否启用流式输出
     */
    private boolean streamingEnabled = true;
    
    /**
     * 执行开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * WebSocket会话（仅WebSocket输出时使用）
     */
    private WebSocketSession webSocketSession;
    
    /**
     * SSE发射器（仅SSE输出时使用）
     */
    private SseEmitter sseEmitter;
    
    /**
     * Flux发射器（仅Flux输出时使用）
     */
    private FluxSink<Object> fluxSink;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes = new ConcurrentHashMap<>();
    
    /**
     * 执行统计信息
     */
    private ExecutionStats stats = new ExecutionStats();
    
    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }
    
    /**
     * 设置属性
     */
    public StreamExecutionContext setAttribute(String key, Object value) {
        attributes.put(key, value);
        return this;
    }
    
    /**
     * 移除属性
     */
    public Object removeAttribute(String key) {
        return attributes.remove(key);
    }
    
    /**
     * 检查是否包含属性
     */
    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }
    
    /**
     * 执行统计信息
     */
    @Data
    @Accessors(chain = true)
    public static class ExecutionStats {
        /**
         * 总节点数
         */
        private int totalNodes = 0;
        
        /**
         * 已执行节点数
         */
        private int executedNodes = 0;
        
        /**
         * 失败节点数
         */
        private int failedNodes = 0;
        
        /**
         * 执行进度百分比
         */
        private int progress = 0;
        
        /**
         * 总执行时间（毫秒）
         */
        private long totalDuration = 0;
        
        /**
         * 平均节点执行时间（毫秒）
         */
        private long averageNodeDuration = 0;
        
        /**
         * 更新进度
         */
        public void updateProgress() {
            if (totalNodes > 0) {
                progress = (executedNodes * 100) / totalNodes;
            }
        }
        
        /**
         * 增加已执行节点数
         */
        public void incrementExecutedNodes() {
            executedNodes++;
            updateProgress();
        }
        
        /**
         * 增加失败节点数
         */
        public void incrementFailedNodes() {
            failedNodes++;
            incrementExecutedNodes();
        }
        
        /**
         * 更新平均执行时间
         */
        public void updateAverageDuration(long nodeDuration) {
            if (executedNodes > 0) {
                totalDuration += nodeDuration;
                averageNodeDuration = totalDuration / executedNodes;
            }
        }
    }
}
