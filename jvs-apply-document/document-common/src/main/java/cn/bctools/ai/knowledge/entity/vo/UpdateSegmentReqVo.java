package cn.bctools.ai.knowledge.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * 更新文本块入参vo
 */
@Data
public class UpdateSegmentReqVo {

    @NotEmpty(message = "内容不能为空")
    private String content;

    private String answer;

    private Set<String> keywords;

    private Boolean regenerateChildChunks = false;

    @ApiModelProperty("是否启用文本块，不传则不处理")
    private Boolean enabled;
}