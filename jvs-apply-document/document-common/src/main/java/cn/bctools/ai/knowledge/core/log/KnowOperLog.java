package cn.bctools.ai.knowledge.core.log;

import cn.bctools.ai.knowledge.annotation.KnowLog;
import cn.bctools.ai.knowledge.core.context.KnowContext;
import cn.bctools.ai.knowledge.entity.data.Knowledge;
import cn.bctools.ai.knowledge.entity.data.KnowledgeLog;
import cn.bctools.ai.knowledge.service.KnowledgeLogService;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.web.utils.IpUtil;
import cn.bctools.web.utils.WebUtils;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 知识库操作日志记录
 *
 * <AUTHOR>
 */
@Component(IKonwLog.BEAN_NAME_PREFIX + KnowOperLog.ID)
@RequiredArgsConstructor
public class KnowOperLog implements IKonwLog {

    public final static String ID = "know";

    private final KnowledgeLogService logService;

    @Override
    public void saveLog(JoinPoint point, KnowLog knowLog, String operation, LocalDateTime logTime, Object result) {
        Knowledge knowledge = KnowContext.getKnowledge();
        if (Objects.isNull(knowLog)) {
            return;
        }

        if (ObjectNull.isNull(knowledge)) {
            return;
        }

        KnowledgeLog log = new KnowledgeLog();
        log.setOperation(operation);
        log.setOperTime(logTime);
        log.setOperIp(IpUtil.getIpAddr(WebUtils.getRequest()));
        log.setKnowId(knowledge.getId());
        log.setUserId(UserCurrentUtils.getUserId());
        log.setUserName(UserCurrentUtils.getRealName());
        logService.save(log);
    }
}