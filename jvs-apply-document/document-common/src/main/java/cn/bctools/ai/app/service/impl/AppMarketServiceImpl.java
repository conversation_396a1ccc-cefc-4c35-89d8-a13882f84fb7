package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.data.AppMarket;
import cn.bctools.ai.app.entity.data.AppPublic;
import cn.bctools.ai.app.entity.data.AppPublicCategory;
import cn.bctools.ai.app.entity.data.Apps;
import cn.bctools.ai.app.entity.vo.CopyAppVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerEditVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarketFilter;
import cn.bctools.ai.app.entity.vo.app_market.DownAppVO;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.mapper.AppMarketMapper;
import cn.bctools.ai.app.service.AppMarketService;
import cn.bctools.ai.app.service.AppPublicCategoryService;
import cn.bctools.ai.app.service.AppPublicService;
import cn.bctools.ai.app.service.AppsService;
import cn.bctools.common.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 应用市场表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppMarketServiceImpl extends ServiceImpl<AppMarketMapper, AppMarket> implements AppMarketService {

    AppsService appsService;

    AppPublicService appPublicService;

    AppPublicCategoryService appPublicCategoryService;

    @Override
    public void editApp(String appId, String publicId, AppMarkerEditVO params) {

        syncDataToApp (appId, publicId, params, "edit");

        if (params.getAllowGuest() != null) {
            this.updateAllowGuest(appId, publicId, params.getAllowGuest());
        }
    }


    @Override
    public void upApp(String appId, String publicId, AppMarkerEditVO params) {

        AppMarket appMarket = this.lambdaQuery()
                .eq(AppMarket::getAppId, appId)
                .eq(AppMarket::getPublicId, publicId)
                .one();
        if (appMarket == null) {
            throw new BusinessException("应用不存在");
        }
        if (appMarket.getStatus()) {
            throw new BusinessException("应用已上架");
        }

        syncDataToApp (appId, publicId, params, "down");

        if (params.getAllowGuest() != null) {
            this.updateAllowGuest(appId, publicId, params.getAllowGuest());
        }

        this.updateStatus(appId, publicId, true);
    }

    @Override
    public void downApp(DownAppVO params) {

        List<AppMarket> lists = this.list(new LambdaQueryWrapper<AppMarket>()
                .in(AppMarket::getId, params.getIds()));

        lists.forEach(e -> {
            this.updateStatus(e.getAppId(), e.getPublicId(), false);
            appsService.lambdaUpdate()
                    .eq(Apps::getId, e.getAppId())
                    .set(Apps::getPublicId, "")
                    .update();
        });

    }

    @Transactional
    public void syncDataToApp(String appId, String publicId, AppMarkerEditVO params, String type) {
        AppPublic appPublic = appPublicService.getById(publicId);
        if (appPublic == null) {
            throw new BusinessException("没有上架版本");
        }
        if (!appPublic.getAppId().equals(appId)) {
            throw new BusinessException("没有上架版本");
        }
        Apps app = appsService.getById(appId);
        if (app == null) {
            throw new BusinessException("应用不存在");
        }

        // 更新上架信息
        appPublic.setAppName(params.getAppName());
        appPublic.setAppIcon(params.getAppIcon());
        appPublic.setAppDesc(params.getAppDesc());
        appPublicService.updateById(appPublic);

        // 更新应用信息
        app.setName(params.getAppName());
        app.setDescription(params.getAppDesc());
        app.setIcon(params.getAppIcon());
        if (type.equals("up")) {
            app.setPublicId(publicId);
        }
        if (type.equals("down")) {
            app.setPublicId("");
        }
        appsService.updateById(app);


        List<String> appCategoryIds = params.getAppCategoryIds();
        if (appCategoryIds != null ) {
           if (appCategoryIds.isEmpty()) {
               appPublicCategoryService.remove(new QueryWrapper<AppPublicCategory>().eq("public_id", publicId));
           } else {
               appPublicCategoryService.remove(new QueryWrapper<AppPublicCategory>().eq("public_id", publicId).notIn("category_id", appCategoryIds));
               List<String> oldList = appPublicCategoryService.list(new QueryWrapper<AppPublicCategory>().eq("public_id", publicId)).stream().map(AppPublicCategory::getCategoryId).toList();
               appCategoryIds.forEach(categoryId -> {
                   if (!oldList.contains(categoryId)) {
                       AppPublicCategory appPublicCategory = new AppPublicCategory();
                       appPublicCategory.setPublicId(publicId);
                       appPublicCategory.setAppId(appId);
                       appPublicCategory.setCategoryId(categoryId);
                       appPublicCategory.setStatus(true);
                       appPublicCategoryService.save(appPublicCategory);
                   }
               });
           }
        }

    }


    /**
     * 修改状态；1上架；0下架
     * @param appId 应用id
     * @param publicId 上架id
     * @param status 状态
     */
    @Override
    public void updateStatus(String appId, String publicId, Boolean status) {
        this.lambdaUpdate()
                .eq(AppMarket::getAppId, appId)
                .eq(AppMarket::getPublicId, publicId)
                .set(AppMarket::getStatus, status)
                .update();
    }

    @Override
    public void updateAllowGuest(String appId, String publicId, Boolean status) {
        this.lambdaUpdate()
                .eq(AppMarket::getAppId, appId)
                .eq(AppMarket::getPublicId, publicId)
                .set(AppMarket::getAllowGuest, status)
                .update();
    }


    @Override
    public IPage<AppMarkerListVO> getList(Page<AppMarkerListVO> page, AppMarketFilter params) {
        return baseMapper.getList(page, params);
    }

    @Override
    public AppMarkerListVO getAppInfo(String id) {
        return baseMapper.getAppInfo(id);
    }

    @Override
    public void quote(CopyAppVO params) {
        AppMarkerListVO app = this.getAppInfo(params.getId());
        if (app == null) {
            throw new BusinessException("应用不存在");
        }


        if (app.getAppMode().equals(AppMode.CHAT.value)) {
            params.setConfigId(app.getAppConfigId());
        } else {
            params.setConfigId(app.getAppWorkflowId());
        }

        params.setSyncDataset(false);
        params.setSyncLabel(false);

        Apps newApps = appsService.copy(app.getAppId(), params);

    }

}
