package cn.bctools.ai.app.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class VersionInfo {


    private String id;

    private String appId;

    private String version;

    private boolean isPublicVersion;

    private boolean isCurrentVersion;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createBy;

}
