package cn.bctools.ai.app.service;

import cn.bctools.ai.app.entity.data.AppMarket;
import cn.bctools.ai.app.entity.vo.CopyAppVO;
import cn.bctools.ai.app.entity.vo.app_market.*;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerEditVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarketFilter;
import cn.bctools.ai.app.entity.vo.app_market.DownAppVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 应用市场表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface AppMarketService extends IService<AppMarket> {

    void editApp(String appId, String publicId, AppMarkerEditVO params);

    void upApp(String appId, String publicId, AppMarkerEditVO params);

    void downApp(DownAppVO params);

    // 上架或者下架应用
    void updateStatus(String appId, String publicId, Boolean status);

    void updateAllowGuest(String appId, String publicId, Boolean status);

    AppMarkerListVO getAppInfo(String id);

    IPage<AppMarkerListVO> getList(Page<AppMarkerListVO> page, AppMarketFilter params);

    void quote(CopyAppVO params);

}
