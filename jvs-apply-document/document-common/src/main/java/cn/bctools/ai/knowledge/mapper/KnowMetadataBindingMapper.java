package cn.bctools.ai.knowledge.mapper;

import cn.bctools.ai.knowledge.entity.data.KnowMetadataBinding;
import cn.bctools.ai.knowledge.entity.vo.MetaBindingCountVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 知识库元信息关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface KnowMetadataBindingMapper extends BaseMapper<KnowMetadataBinding> {

    //获取元数据绑定文档计数
    @Select("""
            <script>
            select metadata_id,count(*) as count
            from ai_know_metadata_binding 
            where metadata_id in 
                <foreach item="id" collection="metaIds" open="(" separator="," close=")">
                  #{id}
                </foreach>
            group by metadata_id
            </script>
            """)
    List<MetaBindingCountVo> getMetaBindingCounts(@Param("metaIds") List<String> metaIds);
}