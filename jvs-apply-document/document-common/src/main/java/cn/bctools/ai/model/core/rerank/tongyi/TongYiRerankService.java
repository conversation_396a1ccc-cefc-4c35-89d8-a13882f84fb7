package cn.bctools.ai.model.core.rerank.tongyi;

import cn.bctools.ai.common.util.HttpReqUtil;
import cn.bctools.ai.knowledge.entity.bean.document.RerankDocument;
import cn.bctools.ai.knowledge.entity.bean.document.RerankResult;
import cn.bctools.common.utils.ObjectNull;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 通义重排序服务
 *
 * <AUTHOR>
 */
//@Service
@Deprecated
public class TongYiRerankService {

    public RerankResult invoke(String model, String query, List<String> docs, Float scoreThreshold, Integer topN) {
        if (ObjectNull.isNull(docs)) {
            return new RerankResult(model, List.of());
        }

        Map<String, Object> params = new HashMap<>();
        params.put("model", "gte-rerank");
        params.put("input", Map.of("query", query,
                "documents", docs));
        params.put("parameters", Map.of("return_documents", true,
                "top_n", topN));

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer sk-623eb96409874092993fe7fd7d42e33d");

        JSONObject jsonObject = HttpReqUtil.postJson(
                "https://dashscope.aliyuncs.com/api/v1/services/rerank/text-rerank/text-rerank",
                params, JSONObject.class, false, headers,true);

        JSONObject output = jsonObject.getJSONObject("output");
        if (Objects.isNull(output)) {
            return new RerankResult(model, List.of());
        }

        JSONArray results = output.getJSONArray("results");
        boolean flag = Objects.nonNull(scoreThreshold);
        List<RerankDocument> rerankDocuments = new ArrayList<>();
        for (int i = 0; i < results.size(); i++) {
            JSONObject result = results.getJSONObject(i);
            RerankDocument rerankDocument = new RerankDocument(result.getInteger("index"),
                    (String) result.getByPath("document.text"),
                    result.getFloat("relevance_score"));
            if (flag) {
                if (rerankDocument.getScore() > scoreThreshold) {
                    rerankDocuments.add(rerankDocument);
                }
            }else {
                rerankDocuments.add(rerankDocument);
            }
        }

        return new RerankResult(model, rerankDocuments);
    }

}