package cn.bctools.ai.knowledge.entity.bean.docmetadata;

import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * 元数据类型- GitHub同步文档 GitHub document
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(MetaSyncedFromGithub.TYPE)
@Accessors(chain = true)
@ApiModel("元数据- GitHub同步文档 GitHub document")
public class MetaSyncedFromGithub extends MetaBase {
    public static final String TYPE = "synced_from_github";

    @ApiModelProperty("仓库名称")
    private String repositoryName;

    @ApiModelProperty("仓库描述")
    private String repositoryDescription;

    @ApiModelProperty("仓库拥有者或组织")
    private String repositoryOwnerOrOrganization;

    @ApiModelProperty("代码文件名称")
    private String codeFileName;

    @ApiModelProperty("代码文件路径")
    private String codeFilePath;

    @ApiModelProperty("编程语言")
    private String programmingLanguage;

    @ApiModelProperty("github链接地址")
    private String githubLink;

    @ApiModelProperty("开源协议")
    private String openSourceLicense;

    @ApiModelProperty("提交日期")
    private String commitDate;

    @ApiModelProperty("提交人")
    private String commitAuthor;

}