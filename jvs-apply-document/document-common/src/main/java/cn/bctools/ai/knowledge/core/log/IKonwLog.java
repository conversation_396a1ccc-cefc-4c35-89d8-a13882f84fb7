package cn.bctools.ai.knowledge.core.log;

import cn.bctools.ai.knowledge.annotation.KnowLog;
import org.aspectj.lang.JoinPoint;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 知识库日志记录
 */
public interface IKonwLog {

    /**
     * Bean名称前缀
     */
    String BEAN_NAME_PREFIX = "KnowLog_";

    void saveLog(JoinPoint point, KnowLog knowLog,String operation, LocalDateTime logTime, Object result);
}