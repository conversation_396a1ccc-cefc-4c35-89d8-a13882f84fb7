package cn.bctools.ai.knowledge.entity.bean.keyword;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * 关键词索引表数据详情
 */
@NoArgsConstructor
@Data
public class KeywordTableData {

    /**
     * indexId
     */
    @JSONField(name = "index_id")
    private String indexId;
    /**
     * file
     * bucketName#@#filepath
     */
    @JSONField(name = "file")
    private String file;
    /**
     * summary
     */
    @JSONField(name = "summary")
    private String summary;
    /**
     * table
     */
    @JSONField(name = "table")
    private Map<String, Set<String>> table;

    public static final String SPLIT_TAG = "#@#";
}