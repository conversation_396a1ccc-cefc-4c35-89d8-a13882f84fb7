package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.data.Apps;
import cn.bctools.ai.app.entity.data.Sites;
import cn.bctools.ai.app.mapper.SitesMapper;
import cn.bctools.ai.app.service.SitesService;
import cn.bctools.ai.app.utils.HelperUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 网站表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class SitesServiceImpl extends ServiceImpl<SitesMapper, Sites> implements SitesService {

    public Sites addDefault(Apps apps) {

        Sites sites = new Sites();
        sites.setAppId(apps.getId());
        sites.setTitle(apps.getName());
        sites.setCode(HelperUtil.generateString(16));
        sites.setDefaultLanguage("zh-Hans");

        return save(sites) ? sites : null;
    }

}
