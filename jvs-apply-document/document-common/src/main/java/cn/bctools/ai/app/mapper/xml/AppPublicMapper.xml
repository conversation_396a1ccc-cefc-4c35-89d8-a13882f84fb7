<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.app.mapper.AppPublicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.app.entity.data.AppPublic">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_mode" property="appMode" />
        <result column="app_name" property="appName" />
        <result column="app_icon" property="appIcon" />
        <result column="app_desc" property="appDesc" />
        <result column="app_version" property="appVersion" />
        <result column="app_config_id" property="appConfigId" />
        <result column="app_workflow_id" property="appWorkflowId" />
        <result column="audit_status" property="auditStatus" />
        <result column="audit_opinion" property="auditOpinion" />
        <result column="audit_by" property="auditBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, app_mode, app_name, app_icon, app_desc, app_version, app_config_id, app_workflow_id, audit_status, audit_opinion, audit_by, create_time, update_time, create_by, update_by, del_flag
    </sql>

    <select id="getMyApplyList" resultType="cn.bctools.ai.app.entity.vo.app_public.AppPublicApplyDetailVO">
        select
            aap.*, GROUP_CONCAT(aac.name) AS category_infos
        from ai_app_public aap
        join ai_apps aa on aa.id = aap.app_id
        left join ai_app_public_category aapc on aapc.public_id = aap.id and aapc.del_flag = 0
        left join ai_app_category aac on aac.id = aapc.category_id
        where aap.del_flag = 0
        <if test="params.userId != null and params.userId != ''">
            and aap.create_by_id = #{params.userId}
        </if>
        <if test="params.type != null and params.type != ''">
            and aap.type = #{params.type}
        </if>
        <if test="params.appMode != null and params.appMode != ''">
            and aa.mode = #{params.appMode}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            and aap.audit_status = #{params.auditStatus}
        </if>
        <if test="params.keyword != null and params.keyword != ''">
            and aa.name like concat('%',#{params.keyword},'%')
        </if>
        group by aap.id
        order by aap.create_time desc
    </select>

</mapper>
