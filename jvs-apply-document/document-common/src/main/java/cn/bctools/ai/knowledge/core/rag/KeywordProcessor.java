package cn.bctools.ai.knowledge.core.rag;

import cn.bctools.ai.common.util.DefaultVUtil;
import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.ai.knowledge.entity.bean.keyword.KeywordTable;
import cn.bctools.ai.knowledge.entity.bean.keyword.KeywordTableData;
import cn.bctools.ai.knowledge.entity.constant.KeywordTableStorageType;
import cn.bctools.ai.knowledge.entity.constant.RKey;
import cn.bctools.ai.knowledge.entity.data.ItemSegment;
import cn.bctools.ai.knowledge.entity.data.KnowledgeKeywordTable;
import cn.bctools.ai.knowledge.service.ItemSegmentsService;
import cn.bctools.ai.knowledge.service.KnowledgeItemService;
import cn.bctools.ai.knowledge.service.KnowledgeKeywordTablesService;
import cn.bctools.ai.knowledge.util.KeywordExtractor;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.oss.template.OssTemplate;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 文档关键词处理器
 * //todo 目前默认使用单类型hanlp处理，若后续需要扩展，自定义实现接口扩展，当前不做扩展预留
 * //todo 若想使用成员变量，可以声明为   @Scope("prototype") 每次创建新对象
 * //todo 注入使用@Lookup或者 @Autowired private ObjectFactory<KeywordProcessor> beanFactory;
 */
@Data
@RequiredArgsConstructor
@Component
@Slf4j
public class KeywordProcessor {

    private final KnowledgeItemService itemService;

    private final KnowledgeKeywordTablesService keywordTablesService;

    private final ItemSegmentsService segmentsService;

    private final RedissonClient redissonClient;

    private final OssTemplate ossTemplate;

    @Value("${knowledge.keyword_source_type:database}")
    private String keywordSourceType;

    // 默认提取的关键词数量
    private static final int MAX_KEYWORD_PER_CHUNK = 10;


    public void create(String knowId, List<KDocument> documents) {
        String lockKey = RKey.KEYWORD_INDEXING_LOCK + knowId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(10 * 60, 10 * 60, TimeUnit.SECONDS);
            if (locked) {
                Map<String, Set<String>> table = this.getKeywordTable(knowId);
                for (KDocument doc : documents) {
                    Set<String> keywords = KeywordExtractor.extractKeywords(doc.text(), MAX_KEYWORD_PER_CHUNK);

                    if (Objects.nonNull(doc.metadata())) {
                        // 更新文本块关键词
                        this.updateSegmentKeywords(knowId, doc.getDocId(), keywords);
                        //将提取的关键词添加到关键词表
                        this.addTextToKeywordTable(table, doc.getDocId(), keywords);
                    }
                }

                //保存更新后的关键词表到数据库或文件
                this.saveKeywordTable(knowId, table);
            }

        } catch (Exception e) {
            log.error("create处理关键词记录出错，知识库id:( {} )", knowId, e);
        } finally {
            if (lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    //根据传入关键词数组差异判断更新关键词表
    public void addTexts(String knowId, List<KDocument> documents, List<Set<String>> keywordList) {
        String lockKey = RKey.KEYWORD_INDEXING_LOCK + knowId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(10 * 60, 10 * 60, TimeUnit.SECONDS);
            if (locked) {
                Map<String, Set<String>> keywordTable = this.getKeywordTable(knowId);
                boolean flag = ObjectNull.isNotNull(keywordList);
                for (int i = 0; i < documents.size(); i++) {
                    KDocument doc = documents.get(i);
                    Set<String> keywords;
                    //判断是否有传入关键词列表，传入则不处理
                    if (flag) {
                        keywords = keywordList.get(i);
                        if (ObjectNull.isNull(keywords)) {
                            keywords = KeywordExtractor.extractKeywords(doc.text(), MAX_KEYWORD_PER_CHUNK);
                        }
                    } else {
                        keywords = KeywordExtractor.extractKeywords(doc.text(), MAX_KEYWORD_PER_CHUNK);
                    }
                    if (Objects.nonNull(doc.metadata())) {
                        this.updateSegmentKeywords(knowId, doc.getDocId(), keywords);
                        this.addTextToKeywordTable(keywordTable, doc.getDocId(), keywords);
                    }
                }
                this.saveKeywordTable(knowId, keywordTable);
            }
        } catch (Exception e) {
            log.error("addTexts处理关键词记录出错，知识库id:( {} )", knowId, e);
        } finally {
            if (lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }


    //获取知识库关键词表信息
    public Map<String, Set<String>> getKeywordTable(String knowId) {
        KnowledgeKeywordTable keywordTable = keywordTablesService.lambdaQuery().eq(KnowledgeKeywordTable::getKnowId, knowId)
                .last("limit 1").one();
        if (Objects.nonNull(keywordTable)) {
            return Optional.ofNullable(keywordTable.parseKeywordTable())
                    .map(KeywordTable::getData)
                    .map(KeywordTableData::getTable).orElse(new HashMap<>());
        } else {
            KnowledgeKeywordTable table = new KnowledgeKeywordTable();
            table.setKnowId(knowId);
            table.setDataSourceType(keywordSourceType);
            Map<String, Object> map = null;
            if (Objects.equals(keywordSourceType, KeywordTableStorageType.DATABASE)) {
                map = Map.of("__type__", "keyword_table",
                        "__data__", Map.of("index_id", knowId,
                                "summary", "",
                                "table", new HashMap<>()));
            } else if (Objects.equals(keywordSourceType, KeywordTableStorageType.FILE)) {
                map = Map.of("__type__", "keyword_table",
                        "__data__", Map.of("index_id", knowId,
                                "summary", "",
                                "file", ""));
            }
            table.setKeywordTable(JSON.toJSONString(map));
            keywordTablesService.save(table);
        }
        return new HashMap<>();
    }

    //更新文本块关键词
    //todo 优化，上层调用改为批量
    public void updateSegmentKeywords(String knowId, String indexNodeId, Set<String> keywords) {
        segmentsService.lambdaUpdate().eq(ItemSegment::getKnowId, knowId)
                .eq(ItemSegment::getIndexNodeId, indexNodeId)
                .set(ItemSegment::getKeywords, JSON.toJSONString(keywords))
                .update();
    }


    //将提取的关键词添加到关键词表
    public void addTextToKeywordTable(Map<String, Set<String>> table, String indexNodeId, Set<String> keywords) {
        for (String keyword : keywords) {
            if (!table.containsKey(keyword)) {
                table.put(keyword, new HashSet<>());
            }
            table.get(keyword).add(indexNodeId);
        }
    }

    //保存关键词索引表
    @SuppressWarnings("unchecked")
    public void saveKeywordTable(String knowId, Map<String, Set<String>> table) {
        KnowledgeKeywordTable keywordTable = keywordTablesService.lambdaQuery()
                .eq(KnowledgeKeywordTable::getKnowId, knowId)
                .select(KnowledgeKeywordTable::getId,
                        KnowledgeKeywordTable::getKnowId,
                        KnowledgeKeywordTable::getDataSourceType)
                .last("limit 1").one();
        Map<String, Object> storeMap = Map.of("__type__", "keyword_table",
                "__data__", Map.of("index_id", knowId,
                        "summary", "",
                        "table", table));
        if (Objects.equals(keywordSourceType, "file")) {
            String fileName = "/document/know/keyword_table/" + knowId + ".txt";
            String file = "jvs-public" + KeywordTableData.SPLIT_TAG + "/document/know/keyword_table/" + knowId + ".txt";
            ((Map<String, Object>) storeMap.get("__data__")).put("file", file);
            ossTemplate.putFile("jvs-public", "document/know/keyword_table/",
                    fileName, new ByteArrayInputStream(JSON.toJSONBytes(storeMap)));
        }
        //database
        else {
            keywordTable.setKeywordTable(JSON.toJSONString(storeMap));
            keywordTablesService.saveOrUpdate(keywordTable);
        }
    }

    //根据id删除关键词表
    @SneakyThrows
    public void deleteByIds(String knowId, List<String> indexNodeIds) {
        String lockKey = RKey.KEYWORD_INDEXING_LOCK + knowId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(10 * 60, 10 * 60, TimeUnit.SECONDS);
            if (locked) {
                Map<String, Set<String>> keywordTable = this.getKeywordTable(knowId);
                if (Objects.nonNull(keywordTable)) {
                    keywordTable = this.deleteIdsFromKeywordTable(keywordTable, indexNodeIds);
                }
                this.saveKeywordTable(knowId, keywordTable);
            }
        } catch (Exception e) {
            log.error("deleteByIds删除关键词记录出错，知识库id:( {} )", knowId, e);
            throw e;
        } finally {
            if (lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    //根据id删除对应索引关键词数据，返回处理好的关键词表
    private Map<String, Set<String>> deleteIdsFromKeywordTable(Map<String, Set<String>> keywordTable, List<String> indexNodeIds) {
        HashSet<String> nodeIdsToDelete = new HashSet<>(indexNodeIds);
        Set<String> keywordsToDelete = new HashSet<>();
        for (String key : keywordTable.keySet()) {
            Set<String> nodeIdxes = keywordTable.get(key);
            if (ObjectNull.isNotNull(nodeIdxes)) {
                if (ObjectNull.isNotNull(CollUtil.intersection(nodeIdsToDelete, nodeIdxes))) {
                    nodeIdxes.removeAll(nodeIdsToDelete);
                    keywordTable.put(key, nodeIdxes);
                    if (ObjectNull.isNull(nodeIdxes)) {
                        keywordsToDelete.add(key);
                    }
                }
            }
        }
        keywordsToDelete.forEach(keywordTable::remove);
        return keywordTable;
    }

    //删除知识库关键词表
    public void delete(String knowId) {
        String lockKey = RKey.KEYWORD_INDEXING_LOCK + knowId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(10 * 60, 10 * 60, TimeUnit.SECONDS);
            if (locked) {
                KnowledgeKeywordTable keywordTable = keywordTablesService.lambdaQuery()
                        .eq(KnowledgeKeywordTable::getKnowId, knowId)
                        .select(KnowledgeKeywordTable::getId, KnowledgeKeywordTable::getDataSourceType)
                        .last("limit 1").one();
                if (Objects.nonNull(keywordTable)) {
                    if (Objects.equals(KeywordTableStorageType.DATABASE, keywordTable.getDataSourceType())) {
                        //database数据库存储类型
                        keywordTablesService.removeById(keywordTable.getId());
                    } else if (Objects.equals(KeywordTableStorageType.FILE, keywordTable.getDataSourceType())) {
                        //处理文件存储类型 删除对应文件或设置为空
                        keywordTable = keywordTablesService.lambdaQuery()
                                .eq(KnowledgeKeywordTable::getKnowId, knowId)
                                .select(KnowledgeKeywordTable::getId, KnowledgeKeywordTable::getDataSourceType,
                                        KnowledgeKeywordTable::getKeywordTable)
                                .last("limit 1").one();
                        KeywordTable table = JSON.parseObject(keywordTable.getKeywordTable(), KeywordTable.class);
                        String fileInfo = table.getData().getFile();
                        String[] split = fileInfo.split(KeywordTableData.SPLIT_TAG);
                        if (split.length == 2) {
                            ossTemplate.delete(split[0], split[1]);
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("delete删除关键词记录出错，知识库id:( {} )", knowId, e);
        } finally {
            if (lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    //根据查询值检索关键词表对应文本块索引id数组 index_node_id
    public List<String> retrieveIdsByQuery(Map<String, Set<String>> keywordTable, String query, Integer k) {
        Set<String> keywords = KeywordExtractor.extractKeywords(query);
        Map<String, Integer> chunkIndicesCount = new HashMap<>();
        Set<String> keywordsList = keywords.stream().filter(keywordTable::containsKey).collect(Collectors.toSet());
        for (String keyword : keywordsList) {
            Set<String> indices = keywordTable.get(keyword);
            if (ObjectNull.isNotNull(indices)) {
                for (String index : indices) {
                    chunkIndicesCount.put(index, chunkIndicesCount.getOrDefault(index, 0) + 1);
                }
            }
        }

        //根据权重排序
        List<String> sortedChunkIndices = chunkIndicesCount.entrySet()
                .stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .map(Map.Entry::getKey).toList();
        return sortedChunkIndices.subList(0, Math.min(k, sortedChunkIndices.size()));
    }

    //搜索
    public List<KDocument> search(String knowId, String query, Integer topK, List<String> documentIdsFilter) {
        Map<String, Set<String>> keywordTable = this.getKeywordTable(knowId);
        List<String> sortedChunkIndices =
                this.retrieveIdsByQuery(keywordTable, query, DefaultVUtil.get(topK, 4));

        List<KDocument> documents = new ArrayList<>();
        for (String docId : sortedChunkIndices) {
            ItemSegment segment = segmentsService.lambdaQuery()
                    .eq(ItemSegment::getKnowId, knowId)
                    .eq(ItemSegment::getIndexNodeId, docId)
                    .in(ObjectNull.isNotNull(documentIdsFilter), ItemSegment::getIndexNodeId, documentIdsFilter)
                    .last("limit 1").one();

            if (Objects.nonNull(segment)) {
                KDocument doc = KDocument.from(segment.getContent());
                doc.setDocId(docId);
                doc.setHash(segment.getIndexNodeHash());
                doc.setKnowId(segment.getKnowId());
                doc.setItemId(segment.getKiId());
                documents.add(doc);
            }
        }

        return documents;
    }

}