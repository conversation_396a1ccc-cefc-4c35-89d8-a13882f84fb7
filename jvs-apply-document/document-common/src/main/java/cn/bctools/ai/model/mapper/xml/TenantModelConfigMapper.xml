<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.model.mapper.TenantModelConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.model.entity.data.TenantModelConfig">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="provider_name" property="providerName" />
        <result column="model_name" property="modelName" />
        <result column="model_type" property="modelType" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_id" property="updateById" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, provider_name, model_name, model_type, create_by, create_by_id, create_time, update_by, update_by_id, update_time, del_flag
    </sql>

</mapper>