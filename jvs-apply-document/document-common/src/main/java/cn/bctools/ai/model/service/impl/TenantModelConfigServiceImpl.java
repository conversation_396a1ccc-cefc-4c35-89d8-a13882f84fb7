package cn.bctools.ai.model.service.impl;

import cn.bctools.ai.model.entity.data.TenantModelConfig;
import cn.bctools.ai.model.mapper.TenantModelConfigMapper;
import cn.bctools.ai.model.service.TenantModelConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 租户模型设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class TenantModelConfigServiceImpl extends ServiceImpl<TenantModelConfigMapper, TenantModelConfig> implements TenantModelConfigService {

}