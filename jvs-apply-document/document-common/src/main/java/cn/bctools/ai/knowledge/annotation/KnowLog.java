package cn.bctools.ai.knowledge.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * 知识库日志记录注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface KnowLog {

    /**
     * 操作名称
     */
    String operation() default "";

    /**
     * 操作名称 SpEL 表达式开启
     */
    boolean operationSpEL() default false;

    /**
     * 转换操作名称SpEL失败降级处理
     */
    String backUpOperation() default "";

    /**
     * 日志记录器类型
     */
    String type() default "";

    /**
     * 是否批量操作
     */
    boolean multi() default false;

    /**
     * 从响应获取属性路径
     */
    String responseParamPath() default "";

}