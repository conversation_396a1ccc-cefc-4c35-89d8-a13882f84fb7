package cn.bctools.ai.app.component;


import cn.bctools.ai.app.entity.data.ConversationMessages;
import cn.bctools.ai.app.entity.vo.AiResponseVO;
import cn.bctools.ai.app.service.ConversationMessagesService;

import com.alibaba.fastjson2.JSON;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.openai.internal.OpenAiUtils;
import dev.langchain4j.model.openai.internal.chat.Message;
import dev.langchain4j.model.output.TokenUsage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class MessageComponent {

    @Autowired
    ConversationMessagesService conversationMessagesService;

    @Async
    public void saveAnswer(AiResponseVO response) {

        String messageId = response.getMessageId();
        if (messageId == null) {
            return;
        }

        ConversationMessages message = conversationMessagesService.getById(messageId);
        if (message == null) {
            return;
        }

        AiMessage aiMessage = response.getAiMessage();
        TokenUsage tokenUsage = response.getTokenUsage();
        String responseText = aiMessage.text();

        String thinking = "";
        // 提取 <think></think> 的内容
        if (aiMessage.text().contains("<think>")) {
            thinking = responseText.substring(responseText.indexOf("<think>") + 7, responseText.indexOf("</think>"));
        }
        // 去除 <think></think> 以及里面的内容
        responseText = responseText.replaceAll("<think>.*?</think>", "");

        message.setAnswer(responseText);
        message.setThinking(thinking);

        message.setProviderResponseLatency(response.getProviderResponseLatency());


        List<Message> messages = OpenAiUtils.toOpenAiMessages(response.getSendMessages());
        message.setMessage(JSON.toJSONString(messages));
        message.setMessageTokens(BigDecimal.valueOf(tokenUsage.inputTokenCount()));
        message.setAnswerTokens(BigDecimal.valueOf(tokenUsage.totalTokenCount()));
        // todo 保存使用价格

        conversationMessagesService.updateById(message);
    }





}
