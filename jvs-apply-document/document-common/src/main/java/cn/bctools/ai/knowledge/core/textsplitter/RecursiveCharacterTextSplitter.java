package cn.bctools.ai.knowledge.core.textsplitter;

import cn.bctools.ai.common.util.TokenCalculateUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.hutool.core.util.StrUtil;

import java.util.*;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * 递归字符文本分段器。
 * 通过递归尝试使用不同的分隔符来分割文本，直到块大小合适。
 */
public class RecursiveCharacterTextSplitter extends TextSplitter {

    protected List<String> separators;

    /**
     * 构造函数。
     *
     * @param chunkSize     块的最大大小。
     * @param chunkOverlap  块之间的重叠大小。
     * @param keepSeparator 是否保留分隔符。
     * @param addStartIndex 是否添加起始索引到元数据。
     * @param separators    尝试分割文本的分隔符列表。
     */
    public RecursiveCharacterTextSplitter(int chunkSize, int chunkOverlap, Boolean keepSeparator,
                                          Boolean addStartIndex, List<String> separators) {
        super(chunkSize, chunkOverlap, keepSeparator, addStartIndex);
        if (ObjectNull.isNull(separators)) {
            separators = new ArrayList<>(Arrays.asList("\n\n", "\n", " ", ""));
        }
        this.separators = separators;
    }

    /**
     * 将单个文本字符串分割成更小的块
     * 尝试使用分隔符列表中的分隔符递归地分割文本
     *
     * @param text       输入文本。
     * @param separators 递归分隔符
     * @return 分割后的文本块字符串列表。
     */
    public List<String> splitText(String text, List<String> separators) {
        List<String> finalChunks = new ArrayList<>();
        // 找到第一个在文本中存在的分隔符，或者使用最后一个分隔符 (通常是 "")
        // 默认使用最后一个分隔符
        String separator = separators.get(separators.size() - 1);
        List<String> newSeparators = new ArrayList<>();

        // 用于下一层递归的分隔符列表
        for (int i = 0; i < separators.size(); i++) {
            String s = separators.get(i);
            // 如果分隔符是空字符串，直接使用并中断查找
            if (StrUtil.isEmpty(s)) {
                separator = s;
                break;
            }
            try {
                // 正则表达式进行查找
                if (Pattern.compile(s).matcher(text).find()) {
                    // 找到匹配的分隔符
                    separator = s;
                    // 下一级分隔符是当前分隔符之后的所有
                    newSeparators = new ArrayList<>(separators.subList(i + 1, separators.size()));
                    // 找到后退出循环
                    break;
                }
            } catch (Exception e) {
                // 如果分隔符字符串不是有效的正则表达式，退化到简单查找或跳过
                // 退化到简单包含检查
                if (text.contains(s)) {
                    separator = s;
                    newSeparators = new ArrayList<>(separators.subList(i + 1, separators.size()));
                    break;
                }
            }
        }

        // 使用选定的分隔符分割文本
        List<String> splits = splitTextWithRegex(text, separator, this.keepSeparator);

        // 存储小于 chunk_size 的分割块
        List<String> goodSplits = new ArrayList<>();
        // 缓存 goodSplits 的长度
        List<Integer> goodSplitsLengths = new ArrayList<>();

        String finalSeparator = Boolean.TRUE.equals(this.keepSeparator) ? "" : separator;

        // 遍历分割后的块及其长度
        for (String s : splits) {
            int sLen = TokenCalculateUtil.getTokenNumOfContent(s);

            // 使用小于等于，因为 chunk_size 是最大大小
            if (sLen <= this.chunkSize) {
                // 如果块小于或等于最大大小，添加到 goodSplits 列表
                goodSplits.add(s);
                goodSplitsLengths.add(sLen);
            } else {
                // 如果块大于最大大小
                // 先处理 goodSplits 中累积的块
                if (ObjectNull.isNotNull(goodSplits)) {
                    // 合并 goodSplits 中的块，并添加到最终结果中
                    List<String> mergedText = mergeSplits(goodSplits, finalSeparator, goodSplitsLengths);
                    finalChunks.addAll(mergedText);
                    // 清空 goodSplits 列表
                    goodSplits = new ArrayList<>();
                    goodSplitsLengths = new ArrayList<>();
                }

                // 处理当前这个过大的块
                if (ObjectNull.isNull(newSeparators)) {
                    // 如果没有下一级分隔符可尝试，直接将此大块作为一个最终块
                    finalChunks.add(s);
                } else {
                    // 如果有下一级分隔符，递归地分割此大块
                    List<String> otherInfo = splitText(s, newSeparators);
                    finalChunks.addAll(otherInfo);
                }
            }
        }

        // 处理循环结束后 goodSplits 中可能剩余的块
        if (ObjectNull.isNotNull(goodSplits)) {
            List<String> mergedText = mergeSplits(goodSplits, finalSeparator, goodSplitsLengths);
            finalChunks.addAll(mergedText);
        }

        return finalChunks;
    }

    @Override
    public List<String> splitText(String text) {
        return this.splitText(text, this.separators);
    }

    //    /**
//     * 静态工厂方法，根据规则创建分段器。
//     * 对应 Python 代码中的 from_encoder 方法。
//     *
//     * @param rule 包含分段规则的 Rules 对象。
//     *             需要从规则中获取 Segmentation 配置以及 EmbeddingModelInstance (如果需要)。
//     * @return 配置好的 RecursiveCharacterTextSplitter 实例。
//     */
//    public static RecursiveCharacterTextSplitter fromEncoder(
//            Rules rule) {
//
//        // 从 规则 中提取分段参数，提供默认值
//        Segmentation segmentation = rule.getSegmentation();
//
//        // 默认值示例
//        int chunkSize = segmentation.getMaxTokens() != null ? segmentation.getMaxTokens() : 500;
//        // 默认值示例
//        int chunkOverlap = segmentation.getChunkOverlap() != null ? segmentation.getChunkOverlap() : 50;
//        List<String> separators = segmentation.getSeparatorList();
//        // Python 代码中没有直接看到 keep_separator 和 add_start_index 参数的传递，
//        // 这里提供默认值，实际应根据 Dify 源码确定是否通过 kwargs 传递
//        // 默认不保留分隔符
//        boolean keepSeparator = segmentation.getKeepSeparator() != null ? segmentation.getKeepSeparator() : false;
//        // 默认不添加起始索引
//        boolean addStartIndex = segmentation.getAddStartIndex() != null ? segmentation.getAddStartIndex() : false;
//
//        // 处理 FixedRecursiveCharacterTextSplitter 似乎有一个固定的 separator 优先使用
//        // 如果 分段规则 中提供了 "fixed_separator"，则将其添加到分隔符列表的最前面
//        String fixedSeparator = segmentation.getFixedSeparator();
//        if (StrUtil.isNotEmpty(fixedSeparator)) {
//            // 确保 \n 被正确处理
//            fixedSeparator = fixedSeparator.replace("\\n", "\n");
//            List<String> combinedSeparators = new ArrayList<>();
//            combinedSeparators.add(fixedSeparator);
//            combinedSeparators.addAll(separators);
//            separators = combinedSeparators;
//        }
//
//        // 获取 token 计算函数
//        Function<List<String>, int[]> lengthFunction = TokenCalculateUtil::getTokenArrayOfContents;
//
//
//        // 根据 分段规则 创建 RecursiveCharacterTextSplitter 实例
//        return null;
//    }
//
//    /**
//     * 将文档列表分割成块。
//     *
//     * @param documents 输入文档列表。
//     * @return 分割后的文档块列表。
//     */
//    @Override
//    public List<KDocument> splitDocuments(List<KDocument> documents) {
//        List<String> texts = new ArrayList<>();
//        List<JSONObject> metadatas = new ArrayList<>();
//        for (KDocument doc : documents) {
//            texts.add(doc.text());
//            metadatas.add(doc.metadata() != null ? doc.metadata() : new JSONObject());
//        }
//
//        return createDocuments(texts, metadatas);
//    }
//
//    /**
//     * 从文本列表创建文档块。
//     * 遍历每个文本，使用 {@code splitText} 分割，并为每个块创建新的 Document 对象。
//     *
//     * @param texts     输入文本列表。
//     * @param metadatas 对应文本的元数据列表。
//     * @return 新创建的 Document 块列表。
//     */
//    public List<KDocument> createDocuments(List<String> texts, List<JSONObject> metadatas) {
//        List<KDocument> documents = new ArrayList<>();
//        List<JSONObject> newMetadatas = metadatas != null ? metadatas : new ArrayList<>(Collections.nCopies(texts.size(), new JSONObject()));
//
//        for (int i = 0; i < texts.size(); i++) {
//            String text = texts.get(i);
//            // 获取对应文本的元数据
//            Map<String, Object> metadata = newMetadatas.get(i);
//
//            // 用于计算 start_index
//            int index = -1;
//            // 使用 splitText 方法分割单个文本
//            List<String> chunks = splitText(text);
//
//            for (String chunk : chunks) {
//                // 深度复制元数据，确保每个块都有独立的元数据副本
//                JSONObject chunkMetadata = BeanCopyUtil.copy(metadata, JSONObject.class);
//
//                // 如果需要添加起始索引，计算并添加到元数据
//                if (this.addStartIndex) {
//                    // 在原文本中查找当前块的起始位置，从上次找到的位置之后开始查找
//                    index = text.indexOf(chunk, index + 1);
//                    // 如果找不到 (理论上不应该发生)，可以设置为 -1 或处理错误\
//                    // Fallback
//                    chunkMetadata.put("start_index", index);
//                }
//
//                // 创建新的 Document 对象表示当前块
//                KDocument newDoc = KDocument.from(chunk, chunkMetadata);
//                documents.add(newDoc);
//            }
//        }
//        return documents;
//    }
//
//
//    /**
//     * 将单个文本字符串分割成更小的块。
//     * 调用内部递归方法 {@code recursiveSplitText}。
//     *
//     * @param text 输入文本。
//     * @return 分割后的文本块字符串列表。
//     */
//    @Override
//    public List<String> splitText(String text) {
//        if (text == null) {
//            return new ArrayList<>();
//        }
//        // 调用递归分割方法，传入分隔符列表
//        return recursiveSplitText(text, this.separators);
//    }
//
//    /**
//     * 递归分割文本的核心逻辑。
//     * 尝试使用分隔符列表中的分隔符递归地分割文本。
//     * 对应 Python 代码中的 _split_text 方法。
//     *
//     * @param text       当前需要分割的文本段。
//     * @param separators 当前递归层级可用的分隔符列表。
//     * @return 分割后的文本块列表。
//     */
//    private List<String> recursiveSplitText(String text, List<String> separators) {
//        List<String> finalChunks = new ArrayList<>();
////         找到第一个在文本中存在的分隔符，或者使用最后一个分隔符 (通常是 "")
////         默认使用最后一个分隔符
//        String separator = separators.get(separators.size() - 1);
//        // 用于下一层递归的分隔符列表
//        List<String> newSeparators = new ArrayList<>();
//
//        // 遍历当前层级的分隔符，找到第一个匹配的
//        for (int i = 0; i < separators.size(); i++) {
//            String s = separators.get(i);
//            // 如果分隔符是空字符串，直接使用并中断查找
//            if (s.isEmpty()) {
//                separator = s;
//                // 按字符分割没有下一级分隔符
//                newSeparators = new ArrayList<>();
//                break;
//            }
//            // 检查分隔符是否在文本中存在 (使用简单的 contains，Python 使用 re.search)
//            // 为了更接近 Python 的 re.search 行为，可以使用 Pattern.compile(s).matcher(text).find()
//            // 但对于简单的分隔符字符串，contains 也可行。为了简化，我们先用 contains。
//            // 如果需要精确匹配 Python 的 re.search，应使用正则。此处为简化实现。
//            try {
//                // 尝试编译为正则表达式进行查找，更接近 Python 的 re.search
//                // 注意 Pattern.LITERAL 会将整个模式视为字面量，如果分隔符本身是正则需要移除 Pattern.LITERAL
//                // 移除 Pattern.LITERAL
//                if (Pattern.compile(s).matcher(text).find()) {
//                    // 找到匹配的分隔符
//                    separator = s;
//                    // 下一级分隔符是当前分隔符之后的所有
//                    newSeparators = new ArrayList<>(separators.subList(i + 1, separators.size()));
//                    // 找到后退出循环
//                    break;
//                }
//            } catch (Exception e) {
//                // 如果分隔符字符串不是有效的正则表达式，退化到简单查找或跳过
//                // 或者记录警告
//                // 退化到简单包含检查
//                if (text.contains(s)) {
//                    separator = s;
//                    newSeparators = new ArrayList<>(separators.subList(i + 1, separators.size()));
//                    break;
//                }
//            }
//        }
//
//        // 使用选定的分隔符分割文本
//        List<String> splits = splitTextWithRegex(text, separator, this.keepSeparator);
//
//        // 存储小于 chunk_size 的分割块
//        List<String> goodSplits = new ArrayList<>();
//        // 缓存 goodSplits 的长度
//        List<Integer> goodSplitsLengths = new ArrayList<>();
//
//        // 获取所有分割块的长度
//        // 使用注入的 lengthFunction
//        int[] sLens = {};
//
//        // 遍历分割后的块及其长度
//        for (int i = 0; i < splits.size(); i++) {
//            String s = splits.get(i);
//            int sLen = sLens[i];
//
//            // 使用小于等于，因为 chunk_size 是最大大小
//            if (sLen <= this.chunkSize) {
//                // 如果块小于或等于最大大小，添加到 goodSplits 列表
//                goodSplits.add(s);
//                goodSplitsLengths.add(sLen);
//            } else {
//                // 如果块大于最大大小
//                // 先处理 goodSplits 中累积的块
//                if (!goodSplits.isEmpty()) {
//                    // 合并 goodSplits 中的块，并添加到最终结果中
//                    List<String> mergedText = mergeSplits(goodSplits, separator, goodSplitsLengths);
//                    finalChunks.addAll(mergedText);
//                    // 清空 goodSplits 列表
//                    goodSplits = new ArrayList<>();
//                    goodSplitsLengths = new ArrayList<>();
//                }
//
//                // 处理当前这个过大的块
//                if (newSeparators.isEmpty()) {
//                    // 如果没有下一级分隔符可尝试，直接将此大块作为一个最终块
//                    finalChunks.add(s);
//                } else {
//                    // 如果有下一级分隔符，递归地分割此大块
//                    List<String> otherInfo = recursiveSplitText(s, newSeparators);
//                    finalChunks.addAll(otherInfo);
//                }
//            }
//        }
//
//        // 处理循环结束后 goodSplits 中可能剩余的块
//        if (!goodSplits.isEmpty()) {
//            List<String> mergedText = mergeSplits(goodSplits, separator, goodSplitsLengths);
//            finalChunks.addAll(mergedText);
//        }
//
//        return finalChunks;
//    }
//
//    /**
//     * 使用正则表达式分割文本，并可选择保留分隔符。
//     * 对应 Python 代码中的 _split_text_with_regex 方法。
//     *
//     * @param text          要分割的文本。
//     * @param separator     分隔符字符串。
//     * @param keepSeparator 是否保留分隔符。
//     * @return 分割后的字符串列表。
//     */
//    private List<String> splitTextWithRegex(String text, String separator, boolean keepSeparator) {
//        if (separator.isEmpty()) {
//            // 如果分隔符是空字符串，按字符分割
//            List<String> chunks = new ArrayList<>();
//            for (int i = 0; i < text.length(); i++) {
//                chunks.add(String.valueOf(text.charAt(i)));
//            }
//            return chunks;
//        }
//
//        // 使用正则表达式分割
//        List<String> splits = new ArrayList<>();
//        // Pattern.quote 将分隔符转义，确保其被视为字面量而不是正则表达式
//        // 如果分隔符本身就是 intended as regex, remove Pattern.quote
//        // Adjusted regex for lookahead if keeping separator
//        Pattern pattern = Pattern.compile(keepSeparator ? "(?=" + Pattern.quote(separator) + ")" : Pattern.quote(separator));
//        Matcher matcher = pattern.matcher(text);
//        int lastIndex = 0;
//
//        while (matcher.find()) {
//            String chunk = text.substring(lastIndex, matcher.start());
//            // Only add non-empty chunks
//            if (!chunk.isEmpty()) {
//                splits.add(chunk);
//            }
//            if (keepSeparator) {
//                // If keeping separator, the chunk *is* the separator itself in this loop structure
//                // Matcher.group(0) is the entire matched substring
//                String sepChunk = matcher.group(0);
//                // Only add non-empty separator chunks
//                if (!sepChunk.isEmpty()) {
//                    splits.add(sepChunk);
//                }
//                // Original logic in Python: splits.add(text.substring(lastIndex, matcher.end())); lastIndex = matcher.end();
//                // This Java pattern with lookahead is different. Let's revert to a simpler split then add sep if needed.
//                 /*
//                 // Revert to simpler split and add separator manually
//                 int matchStart = matcher.start();
//                 int matchEnd = matcher.end();
//                 // Add the text before the separator
//                 splits.add(text.substring(lastIndex, matchStart));
//                 if (keepSeparator) {
//                     // Add the separator itself
//                     splits.add(text.substring(matchStart, matchEnd));
//                 }
//                 lastIndex = matchEnd;
//                 */
//            }
//            // Move lastIndex to the end of the match
//            lastIndex = matcher.end();
//        }
//        // Add the last chunk after the last separator
//        String lastChunk = text.substring(lastIndex);
//        // Only add non-empty last chunk
//        if (!lastChunk.isEmpty()) {
//            splits.add(lastChunk);
//        }
//
//
//        // Simplified approach: split by regex, then re-add separator if needed
//        // -1 to keep trailing empty strings
//        List<String> simpleSplits = new ArrayList<>(Arrays.asList(text.split(Pattern.quote(separator), -1)));
//
//        if (keepSeparator && !separator.isEmpty()) {
//            List<String> splitsWithSeparator = new ArrayList<>();
//            for (int i = 0; i < simpleSplits.size(); i++) {
//                splitsWithSeparator.add(simpleSplits.get(i));
//                if (i < simpleSplits.size() - 1) {
//                    // Add the separator between chunks
//                    splitsWithSeparator.add(separator);
//                }
//            }
//            splits = splitsWithSeparator;
//        } else {
//            splits = simpleSplits;
//        }
//
//
//        // Filter out empty strings, unless the separator is empty string (char split)
//        if (!separator.isEmpty()) {
//            splits.removeIf(String::isEmpty);
//        }
//
//        return splits;
//    }

//    /**
//     * 合并分割后的块，遵循 chunk_overlap。
//     * 对应 Python 代码中的 _merge_splits 方法。
//     * 这是递归分段器中处理小于 chunk_size 的块并创建重叠的核心逻辑。
//     *
//     * @param splits        需要合并的分割块列表 (它们都小于 chunk_size)。
//     * @param separator     用于分割这些块的原始分隔符。
//     * @param splitsLengths 对应 splits 中每个块的长度 (token 或字符)。
//     * @return 合并并应用重叠后的最终块列表。
//     */
//    public List<String> mergeSplits(List<String> splits, String separator, List<Integer> splitsLengths) {
//        List<String> mergedChunks = new ArrayList<>();
//        // 当前正在合并的块的组成部分
//        List<String> currentAccumulator = new ArrayList<>();
//        // 当前累积块的长度
//        int currentLength = 0;
//
//        // 根据 keepSeparator 确定添加到块之间的分隔符
//        // 如果保留分隔符，重叠时也保留，否则不加分隔符
//        String separatorWithOverlap = this.keepSeparator ? separator : "";
//        // 计算分隔符的长度
////        int separatorLength = this.lengthFunction.apply(Collections.singletonList(separatorWithOverlap))[0];
//        int separatorLength = 0;
//        for (int i = 0; i < splits.size(); i++) {
//            String split = splits.get(i);
//            int splitLength = splitsLengths.get(i);
//
//            // 计算将当前 split 加入到累积块后的长度
//            // currentLength + 分隔符长度 (如果累积块不为空) + 当前 split 长度
//            int newLength = currentLength + (currentAccumulator.isEmpty() ? 0 : separatorLength) + splitLength;
//
//            if (newLength <= this.chunkSize) {
//                // 如果加入当前 split 后不超限，则加入
//                currentAccumulator.add(split);
//                // 更新累积长度
//                currentLength = newLength;
//            } else {
//                // 如果加入当前 split 后超限
//                // 1. 将当前累积块合并成一个最终块
//                if (!currentAccumulator.isEmpty()) {
//                    mergedChunks.add(String.join(separatorWithOverlap, currentAccumulator));
//                }
//
//                // 2. 开始新的累积块
//                // 重置长度
//                currentLength = 0;
//
//                // 3. 处理重叠
//                // 从前一个块的末尾（即 splits 列表的当前位置 i 之前的部分）获取重叠内容
//                // 需要回溯来构建重叠部分
//                // 上一个块末尾的部分
//                List<String> lastChunkPart = new ArrayList<>();
//                // 重叠部分的长度
//                int overlapLength = 0;
//                // 从当前 split 往前追溯，直到达到重叠大小或列表开头
//                // j 从 i-1 开始，倒序遍历 splits 列表
//                for (int j = i - 1; j >= 0; j--) {
//                    String prevSplit = splits.get(j);
//                    int prevSplitLength = splitsLengths.get(j);
//                    // 计算将这个 prevSplit 加入到 lastChunkPart 中所需的长度 (包括它自身和它之前可能需要的分隔符)
//                    // 如果 lastChunkPart 不为空，加入 prevSplit 需要一个分隔符
//                    int lenNeededForPrevSplit = prevSplitLength + (lastChunkPart.isEmpty() ? 0 : separatorLength);
//
//                    // 检查加入 prevSplit 是否会使重叠部分超过 this.chunkOverlap
//                    if (overlapLength + lenNeededForPrevSplit <= this.chunkOverlap) {
//                        // 将前一个 split 加入重叠部分列表的开头
//                        lastChunkPart.add(0, prevSplit);
//                        overlapLength += lenNeededForPrevSplit;
//                    } else {
//                        // 如果加入这个 split 会超过重叠大小，停止追溯
//                        // 更复杂的实现可能需要截取 prevSplit 的一部分
//                        break;
//                    }
//                }
//
//                // 将重叠部分加入新的累积块开头
//                // 创建一个新的 accumulator 列表并加入重叠部分
//                currentAccumulator = new ArrayList<>(lastChunkPart);
//
//                // 重新计算新的累积块长度（重叠部分 + 当前 split）
//                // 重叠部分的长度 already calculated as overlapLength
//                // 新的累积块长度 = 重叠部分的长度 + 分隔符长度 (如果重叠部分不为空) + 当前 split 长度
//                currentLength = overlapLength + (lastChunkPart.isEmpty() ? 0 : separatorLength) + splitLength;
//
//                // 将当前 split 加入新的累积块
//                currentAccumulator.add(split);
//            }
//        }
//
//        // 处理循环结束后，currentAccumulator 中可能剩余的块
//        if (!currentAccumulator.isEmpty()) {
//            mergedChunks.add(String.join(separatorWithOverlap, currentAccumulator));
//        }
//
//        // 在返回前，过滤掉可能生成的空字符串块（例如，原始文本末尾的分隔符导致的空块）
//        // 注意：如果 keepSeparator 为 true，空字符串可能是保留的分隔符
//        // 只有在不保留分隔符时才移除空块
//        if (!this.keepSeparator) {
//            mergedChunks.removeIf(String::isEmpty);
//        } else {
//            // 如果保留分隔符，需要更谨慎地移除空块，只移除不是由分隔符产生的空块
//            // 这里的简单实现可能仍会保留由连续分隔符产生的空字符串
//            // 实际应用中需要根据 splitTextWithRegex 和 _merge_splits 的精确行为来调整
//        }
//
//
//        return mergedChunks;
//    }

}