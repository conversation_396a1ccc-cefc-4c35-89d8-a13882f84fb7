package cn.bctools.ai.stream.manager;

import cn.bctools.ai.stream.config.StreamExecutionProperties;
import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.enums.StreamEventType;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamNodeExecutionDto;
import cn.bctools.ai.stream.factory.StreamOutputStrategyFactory;
import cn.bctools.ai.stream.strategy.StreamOutputStrategy;
import cn.bctools.common.utils.SpringContextUtil;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 流式执行管理器
 * 统一管理所有流式输出的生命周期和资源
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class StreamExecutionManager {
    
    @Autowired
    private StreamOutputStrategyFactory strategyFactory;

    @Autowired
    private StreamExecutionProperties properties;
    
    /**
     * 活跃的执行上下文池
     */
    private final ConcurrentHashMap<String, StreamExecutionContext> activeContexts = new ConcurrentHashMap<>();
    
    /**
     * 执行统计计数器
     */
    private final AtomicInteger totalExecutions = new AtomicInteger(0);
    private final AtomicInteger activeExecutions = new AtomicInteger(0);
    
    /**
     * 异步任务执行器
     */
    private final ExecutorService asyncExecutor = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, StreamConstants.Defaults.DEFAULT_THREAD_NAME_PREFIX + IdUtil.fastSimpleUUID());
        thread.setDaemon(true);
        return thread;
    });

    /**
     * 定时清理任务
     */
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, StreamConstants.Defaults.DEFAULT_CLEANUP_THREAD_NAME);
        thread.setDaemon(true);
        return thread;
    });
    
    /**
     * 初始化管理器
     */
    public StreamExecutionManager() {
        // 启动定时清理任务
        int cleanupInterval = properties != null ? properties.getCleanupIntervalMinutes() : 5;
        cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredContexts, cleanupInterval, cleanupInterval, TimeUnit.MINUTES);
        log.info("🎛️ 流式执行管理器已启动");
    }
    
    /**
     * 创建流式执行上下文
     * 
     * @param appId 应用ID
     * @param ruleSecret 规则密钥
     * @param inputParams 输入参数
     * @param outputType 输出类型
     * @return 执行上下文
     */
    public StreamExecutionContext createContext(String appId, String ruleSecret, 
                                               Map<String, Object> inputParams, 
                                               StreamOutputType outputType) {
        String executionId = generateExecutionId();
        
        StreamExecutionContext context = new StreamExecutionContext()
                .setExecutionId(executionId)
                .setAppId(appId)
                .setRuleSecret(ruleSecret)
                .setInputParams(inputParams)
                .setOutputType(outputType)
                .setStartTime(LocalDateTime.now())
                .setStreamingEnabled(true);
        
        // 注册到活跃上下文池
        activeContexts.put(executionId, context);
        totalExecutions.incrementAndGet();
        activeExecutions.incrementAndGet();
        
        log.info("🆕 创建流式执行上下文 - 执行ID: {}, 应用ID: {}, 输出类型: {}", 
                executionId, appId, outputType.getCode());
        
        return context;
    }
    
    /**
     * 开始流式执行
     * 
     * @param context 执行上下文
     * @param totalNodes 总节点数
     */
    public void startExecution(StreamExecutionContext context, int totalNodes) {
        StreamOutputStrategy strategy = strategyFactory.getBestStrategy(context);
        
        try {
            // 初始化策略
            strategy.initialize(context);
            
            // 触发工作流开始事件
            strategy.onWorkflowStarted(context, totalNodes);
            
            log.info("🚀 开始流式执行 - 执行ID: {}, 策略: {}, 总节点数: {}", 
                    context.getExecutionId(), strategy.getClass().getSimpleName(), totalNodes);
            
        } catch (Exception e) {
            log.error("❌ 启动流式执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
            throw new RuntimeException("启动流式执行失败", e);
        }
    }
    
    /**
     * 节点开始执行
     * 
     * @param executionId 执行ID
     * @param nodeExecution 节点执行信息
     */
    public void onNodeStarted(String executionId, StreamNodeExecutionDto nodeExecution) {
        executeAsync(executionId, (context, strategy) -> 
                strategy.onNodeStarted(context, nodeExecution));
    }
    
    /**
     * 节点执行中
     * 
     * @param executionId 执行ID
     * @param nodeExecution 节点执行信息
     */
    public void onNodeRunning(String executionId, StreamNodeExecutionDto nodeExecution) {
        executeAsync(executionId, (context, strategy) -> 
                strategy.onNodeRunning(context, nodeExecution));
    }
    
    /**
     * 节点执行完成
     * 
     * @param executionId 执行ID
     * @param nodeExecution 节点执行信息
     */
    public void onNodeCompleted(String executionId, StreamNodeExecutionDto nodeExecution) {
        executeAsync(executionId, (context, strategy) -> {
            strategy.onNodeCompleted(context, nodeExecution);
            
            // 更新进度
            StreamExecutionContext.ExecutionStats stats = context.getStats();
            strategy.onProgressUpdate(context, stats.getProgress(), 
                    stats.getExecutedNodes(), stats.getTotalNodes());
        });
    }
    
    /**
     * 节点执行失败
     * 
     * @param executionId 执行ID
     * @param nodeExecution 节点执行信息
     */
    public void onNodeFailed(String executionId, StreamNodeExecutionDto nodeExecution) {
        executeAsync(executionId, (context, strategy) -> 
                strategy.onNodeFailed(context, nodeExecution));
    }
    
    /**
     * 工作流执行完成
     * 
     * @param executionId 执行ID
     * @param finalResult 最终结果
     */
    public void onWorkflowCompleted(String executionId, Object finalResult) {
        executeAsync(executionId, (context, strategy) -> {
            strategy.onWorkflowCompleted(context, finalResult);
            completeExecution(executionId);
        });
    }
    
    /**
     * 工作流执行失败
     * 
     * @param executionId 执行ID
     * @param errorMessage 错误信息
     */
    public void onWorkflowFailed(String executionId, String errorMessage) {
        executeAsync(executionId, (context, strategy) -> {
            strategy.onWorkflowFailed(context, errorMessage);
            completeExecution(executionId);
        });
    }
    
    /**
     * 发送自定义事件
     * 
     * @param executionId 执行ID
     * @param eventType 事件类型
     * @param data 事件数据
     */
    public void sendEvent(String executionId, StreamEventType eventType, Object data) {
        executeAsync(executionId, (context, strategy) -> 
                strategy.onEvent(context, eventType, data));
    }
    
    /**
     * 获取Flux流（仅对支持Flux的策略有效）
     * 
     * @param context 执行上下文
     * @return Flux流
     */
    public Flux<Object> getFlux(StreamExecutionContext context) {
        StreamOutputStrategy strategy = strategyFactory.getBestStrategy(context);
        try {
            return strategy.getFlux(context);
        } catch (UnsupportedOperationException e) {
            log.warn("⚠️ 策略 {} 不支持Flux输出", strategy.getClass().getSimpleName());
            throw e;
        }
    }
    
    /**
     * 获取执行上下文
     * 
     * @param executionId 执行ID
     * @return 执行上下文
     */
    public StreamExecutionContext getContext(String executionId) {
        return activeContexts.get(executionId);
    }
    
    /**
     * 获取所有活跃的执行上下文
     * 
     * @return 活跃上下文映射
     */
    public Map<String, StreamExecutionContext> getActiveContexts() {
        return new ConcurrentHashMap<>(activeContexts);
    }
    
    /**
     * 获取执行统计信息
     * 
     * @return 统计信息
     */
    public ExecutionStats getExecutionStats() {
        return new ExecutionStats(
                totalExecutions.get(),
                activeExecutions.get(),
                activeContexts.size()
        );
    }
    
    /**
     * 手动完成执行
     * 
     * @param executionId 执行ID
     */
    public void completeExecution(String executionId) {
        StreamExecutionContext context = activeContexts.remove(executionId);
        if (context != null) {
            try {
                StreamOutputStrategy strategy = strategyFactory.getBestStrategy(context);
                strategy.cleanup(context);
                activeExecutions.decrementAndGet();
                
                log.info("✅ 完成流式执行 - 执行ID: {}, 耗时: {}ms", 
                        executionId, 
                        System.currentTimeMillis() - context.getStartTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
                
            } catch (Exception e) {
                log.error("❌ 清理执行上下文失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
            }
        }
    }
    
    /**
     * 取消执行
     * 
     * @param executionId 执行ID
     */
    public void cancelExecution(String executionId) {
        StreamExecutionContext context = activeContexts.get(executionId);
        if (context != null) {
            context.setStreamingEnabled(false);
            completeExecution(executionId);
            log.info("🚫 取消流式执行 - 执行ID: {}", executionId);
        }
    }
    
    /**
     * 异步执行策略方法
     */
    private void executeAsync(String executionId, StrategyAction action) {
        asyncExecutor.submit(() -> {
            StreamExecutionContext context = activeContexts.get(executionId);
            if (context != null && context.isStreamingEnabled()) {
                try {
                    StreamOutputStrategy strategy = strategyFactory.getBestStrategy(context);
                    action.execute(context, strategy);
                } catch (Exception e) {
                    log.error("❌ 执行流式输出策略失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
                }
            }
        });
    }
    
    /**
     * 清理过期的执行上下文
     */
    private void cleanupExpiredContexts() {
        long now = System.currentTimeMillis();
        int expireMinutes = properties != null ? properties.getContextExpireMinutes() : 30;
        long expireTime = expireMinutes * 60 * 1000; // 转换为毫秒
        
        activeContexts.entrySet().removeIf(entry -> {
            StreamExecutionContext context = entry.getValue();
            long startTime = context.getStartTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
            
            if (now - startTime > expireTime) {
                log.info("🧹 清理过期执行上下文 - 执行ID: {}", entry.getKey());
                try {
                    StreamOutputStrategy strategy = strategyFactory.getBestStrategy(context);
                    strategy.cleanup(context);
                } catch (Exception e) {
                    log.error("清理过期上下文失败", e);
                }
                activeExecutions.decrementAndGet();
                return true;
            }
            return false;
        });
    }
    
    /**
     * 生成执行ID
     */
    private String generateExecutionId() {
        return StreamConstants.Patterns.EXECUTION_ID_PREFIX + System.currentTimeMillis() + "_" + IdUtil.fastSimpleUUID();
    }
    
    /**
     * 关闭管理器
     */
    @PreDestroy
    public void shutdown() {
        log.info("🛑 关闭流式执行管理器...");
        
        // 清理所有活跃上下文
        activeContexts.forEach((id, context) -> {
            try {
                StreamOutputStrategy strategy = strategyFactory.getBestStrategy(context);
                strategy.cleanup(context);
            } catch (Exception e) {
                log.error("清理上下文失败 - 执行ID: {}", id, e);
            }
        });
        activeContexts.clear();
        
        // 关闭线程池
        asyncExecutor.shutdown();
        cleanupExecutor.shutdown();
        
        try {
            if (!asyncExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                asyncExecutor.shutdownNow();
            }
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("✅ 流式执行管理器已关闭");
    }
    
    /**
     * 策略执行动作接口
     */
    @FunctionalInterface
    private interface StrategyAction {
        void execute(StreamExecutionContext context, StreamOutputStrategy strategy);
    }
    
    /**
     * 执行统计信息
     */
    public static class ExecutionStats {
        private final int totalExecutions;
        private final int activeExecutions;
        private final int activeContexts;
        
        public ExecutionStats(int totalExecutions, int activeExecutions, int activeContexts) {
            this.totalExecutions = totalExecutions;
            this.activeExecutions = activeExecutions;
            this.activeContexts = activeContexts;
        }
        
        public int getTotalExecutions() { return totalExecutions; }
        public int getActiveExecutions() { return activeExecutions; }
        public int getActiveContexts() { return activeContexts; }
    }
    
    /**
     * 获取管理器实例（静态方法）
     */
    public static StreamExecutionManager getInstance() {
        return SpringContextUtil.getBean(StreamExecutionManager.class);
    }
}