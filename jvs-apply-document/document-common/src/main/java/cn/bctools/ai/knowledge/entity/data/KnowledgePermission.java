package cn.bctools.ai.knowledge.entity.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serial;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 知识库权限资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@Accessors(chain = true)
@TableName("ai_knowledge_permission")
@ApiModel(value="KnowledgePermission对象", description="知识库权限资源表")
public class KnowledgePermission implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "标识名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "标识key")
    @TableField("permission")
    private String permission;

    @ApiModelProperty(value = "标识类型")
    @TableField("group_name")
    private String groupName;

}