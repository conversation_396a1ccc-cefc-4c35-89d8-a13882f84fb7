package cn.bctools.ai.knowledge.mapper;

import cn.bctools.ai.knowledge.entity.data.ChildChunk;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface ChildChunksMapper extends BaseMapper<ChildChunk> {

    @Select("""
                select max(position) from ai_child_chunks where segment_id = #{segmentId}
            """)
    Integer maxPosition(@Param("segmentId") String segmentId);
}