package cn.bctools.ai.common.config.async;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SystemThreadLocal;
import cn.bctools.common.utils.TenantContextHolder;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务线程池配置
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
@EnableAsync(proxyTargetClass = true)
@EnableConfigurationProperties(AsyncProperties.class)
public class AsyncConfig {

    private static final Logger logger = LoggerFactory.getLogger(AsyncConfig.class);

    public static final String COMMON_EXECUTOR_BEAN = "aiTaskExecutor";

    private final AsyncProperties properties;

    //最大同时执行任务数
    public final static int MAX_TASK = 10;

    @Bean(COMMON_EXECUTOR_BEAN)
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(properties.getPool().getThreadNamePrefix());
        // 常驻内存核心线程数
        executor.setCorePoolSize(properties.getPool().getCoreSize());
        // 任务队列长度，队列满后扩展核心线程
        executor.setQueueCapacity(properties.getPool().getQueueCapacity());
        // 最大线程数
        executor.setMaxPoolSize(properties.getPool().getMaxSize());
        // 空闲线程存活时间
        executor.setKeepAliveSeconds((int) properties.getPool().getKeepAlive().getSeconds());
        executor.setAllowCoreThreadTimeOut(this.properties.getPool().isAllowCoreThreadTimeout());
        // 关闭服务是否等待未完成任务执行完
        executor.setWaitForTasksToCompleteOnShutdown(properties.getShutdown().isAwaitTermination());
        // 关闭服务等待未完成任务最大时长
        executor.setAwaitTerminationSeconds((int) properties.getShutdown().getAwaitTerminationPeriod().toMillis() / 1000);
        /*
         * 当线程池线程数已经达到maxSize时的处理策略：
         * ThreadPoolExecutor.AbortPolicy:丢弃任务并抛出RejectedExecutionException异常。 默认策略
         * ThreadPoolExecutor.DiscardPolicy：丢弃任务，但是不抛出异常。
         * ThreadPoolExecutor.DiscardOldestPolicy：丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         * ThreadPoolExecutor.CallerRunsPolicy：不创建新线程执行任务，由调用线程直接处理该任务
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        //设置线程装饰器
        decorateExecutor(executor);

        logger.info("ai async task executor initialize: {}", executor.getThreadNamePrefix());
        executor.initialize();
        return executor;
    }

    //装饰线程
    private void decorateExecutor(ThreadPoolTaskExecutor executor) {
        executor.setTaskDecorator(runnable -> {
            RequestAttributes context = null;
            Authentication authenticationAuthentication = null;
            try {
                context = RequestContextHolder.currentRequestAttributes();
                SecurityContext authentication = SecurityContextHolder.getContext();
                authenticationAuthentication = authentication.getAuthentication();
            } catch (IllegalStateException e) {
            }
            String tenantId = TenantContextHolder.getTenantId();
            Map<String, Object> systemThreadLocalMap = SystemThreadLocal.get();
            Authentication finalAuthenticationAuthentication = authenticationAuthentication;
            RequestAttributes finalContext = context;
            return () -> {
                SystemThreadLocal.setAll(systemThreadLocalMap);
                TenantContextHolder.setTenantId(tenantId);
                if (ObjectNull.isNotNull(finalContext)) {
                    //设置上下文user对象
                    RequestContextHolder.setRequestAttributes(finalContext);
                    if (Objects.nonNull(finalAuthenticationAuthentication)) {
                        SystemThreadLocal.set("user", finalAuthenticationAuthentication.getPrincipal());
                        SecurityContextHolder.getContext().setAuthentication(finalAuthenticationAuthentication);
                    }
                }
                // 2. 缓存租户id
                runnable.run();
            };
        });
    }
}