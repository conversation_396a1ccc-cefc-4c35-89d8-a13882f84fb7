<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.app.mapper.AppPublicCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.app.entity.data.AppPublicCategory">
        <id column="id" property="id" />
        <result column="public_id" property="publicId" />
        <result column="app_id" property="appId" />
        <result column="category_id" property="categoryId" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, public_id, app_id, category_id, status, create_time, update_time, create_by, update_by, del_flag
    </sql>

</mapper>
