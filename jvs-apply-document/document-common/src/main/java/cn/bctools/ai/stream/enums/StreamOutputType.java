package cn.bctools.ai.stream.enums;

/**
 * 流式输出类型枚举
 * 
 * <AUTHOR>
 */
public enum StreamOutputType {
    
    /**
     * 控制台日志输出
     */
    CONSOLE("console", "控制台日志"),
    
    /**
     * WebSocket实时推送
     */
    WEBSOCKET("websocket", "WebSocket推送"),
    
    /**
     * Server-Sent Events推送
     */
    SSE("sse", "SSE推送"),
    
    /**
     * Reactive Flux流式输出
     */
    FLUX("flux", "Flux流式输出"),
    
    /**
     * 消息队列推送
     */
    MESSAGE_QUEUE("mq", "消息队列推送"),
    
    /**
     * 数据库存储
     */
    DATABASE("database", "数据库存储"),
    
    /**
     * 文件输出
     */
    FILE("file", "文件输出"),
    
    /**
     * 自定义输出
     */
    CUSTOM("custom", "自定义输出");
    
    private final String code;
    private final String description;
    
    StreamOutputType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static StreamOutputType fromCode(String code) {
        for (StreamOutputType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown stream output type: " + code);
    }
}
