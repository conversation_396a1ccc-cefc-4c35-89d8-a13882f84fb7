package cn.bctools.ai.tag.service;

import cn.bctools.ai.tag.entity.data.Tags;
import cn.bctools.ai.tag.entity.vo.TagEditInfoVO;
import cn.bctools.ai.tag.entity.vo.TagInfoVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface TagsService extends IService<Tags> {

    List<Tags> searchMine(String type, String keyword);

    Tags create(TagInfoVO tags);

    Tags edit(String id, TagEditInfoVO tags);

    void checkTargetExit(String targetId, String type);

}
