package cn.bctools.ai.tag.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class TagBingingsCreateVO {

    @ApiModelProperty("标签名称")
    @NotEmpty(message = "标签Id不能为空")
    private List<String> tagIds;

    @ApiModelProperty("目标Id")
    @NotBlank(message = "目标Id不能为空")
    private String targetId;

    @ApiModelProperty("标签类型")
    @NotBlank(message = "标签类型不能为空")
    private String type;

}
