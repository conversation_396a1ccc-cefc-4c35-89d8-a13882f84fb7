package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.data.AppMarket;
import cn.bctools.ai.app.entity.data.AppPublic;
import cn.bctools.ai.app.entity.data.AppPublicCategory;
import cn.bctools.ai.app.entity.data.Apps;
import cn.bctools.ai.app.entity.vo.app_public.*;
import cn.bctools.ai.app.mapper.AppPublicMapper;
import cn.bctools.ai.app.service.AppMarketService;
import cn.bctools.ai.app.service.AppPublicCategoryService;
import cn.bctools.ai.app.service.AppPublicService;
import cn.bctools.ai.app.service.AppsService;
import cn.bctools.ai.knowledge.entity.enums.AuditStatus;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 * 应用公开表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppPublicServiceImpl extends ServiceImpl<AppPublicMapper, AppPublic> implements AppPublicService {

    AppsService appsService;

    AppPublicCategoryService appPublicCategoryService;

    @Override
    public IPage<AppPublicApplyDetailVO> getApplyList(Page<AppPublicApplyDetailVO> page, AppPublicApplyListVO params) {
        return baseMapper.getMyApplyList(page, params);
    }

    @Override
    public IPage<AppPublicApplyDetailVO> getMyApplyList(Page<AppPublicApplyDetailVO> page, AppPublicApplyListVO params) {

        params.setUserId(UserCurrentUtils.getUserId());
        return baseMapper.getMyApplyList(page, params);
    }

    @Override
    public AppPublicVO appPublic(String appId) {
        Apps app = appsService.getById(appId);
        if (app == null) {
            throw new RuntimeException("应用不存在");
        }

        AppPublicVO rtn = new AppPublicVO();
        if (app.getPublicId() != null) {
            AppPublic public1 = this.getById(app.getPublicId());
            rtn.setCurrentVersionOnSale(public1);
        }
        if (app.getPublicId2() != null) {
            AppPublic auditVersion = this.getById(app.getPublicId2());
            rtn.setAuditVersion(auditVersion);
        }

        return rtn;
    }

    @Override
    public void apply(AppApplyVO params) {

        List<String> appCategoryIds = params.getAppCategoryIds();
        if (ObjectUtils.isEmpty(appCategoryIds)) {
            throw new RuntimeException("请选择应用分类");
        }

        String appId = params.getAppId();

        AppPublic recordExits = this.lambdaQuery()
                .eq(AppPublic::getAppId, appId)
                .ne(AppPublic::getAuditStatus, AuditStatus.PASSED)
                .one();
        if (recordExits != null) {
            throw new RuntimeException("请先删除已存在的申请！");
        }

        Apps app = appsService.getById(appId);
        if (app == null) {
            throw new RuntimeException("应用不存在");
        }

        // 同步发布设置到应用
        boolean update = false;
        if (StringUtils.isNotBlank(params.getAppName())) {
            app.setName(params.getAppName());
            update = true;
        }
        if (StringUtils.isNotBlank(params.getAppIcon())) {
            app.setIcon(params.getAppIcon());
            update = true;
        }
        if (StringUtils.isNotBlank(params.getAppDesc())) {
            app.setDescription(params.getAppDesc());
            update = true;
        }
        if (update) {
            appsService.updateById(app);
        }

        AppPublic appPublic = new AppPublic();
        appPublic.setType("up");
        appPublic.setAppId(appId);
        appPublic.setAppMode(app.getMode());
        appPublic.setAppName(params.getAppName());
        if (StringUtils.isBlank(params.getAppIcon())) {
            appPublic.setAppIcon(app.getIcon());
        } else {
            appPublic.setAppIcon(params.getAppIcon());
        }
        appPublic.setAppDesc(params.getAppDesc());
        appPublic.setAppVersion(params.getAppVersion());
        appPublic.setAppConfigId(params.getAppConfigId());
        appPublic.setAppWorkflowId(params.getAppWorkflowId());
        appPublic.setAuditStatus(AuditStatus.PENDING);
        this.save(appPublic);

        List<AppPublicCategory> appPublicCategoryList = appCategoryIds.stream().map(categoryId -> {
            AppPublicCategory appPublicCategory = new AppPublicCategory();
            appPublicCategory.setPublicId(appPublic.getId());
            appPublicCategory.setAppId(appId);
            appPublicCategory.setCategoryId(categoryId);
            appPublicCategory.setStatus(true);
            return appPublicCategory;
        }).toList();

        if (ObjectUtils.isNotEmpty(appPublicCategoryList)) {
            appPublicCategoryService.saveBatch(appPublicCategoryList);
        }

        // 更新审核版本id
        app.setPublicId2(appPublic.getId());
        appsService.updateById(app);

    }

    @Override
    public void applyDown(String appId) {
        Apps app = appsService.getById(appId);
        if (app == null) {
            throw new RuntimeException("应用不存在");
        }

        if (app.getPublicId() == null) {
            throw new RuntimeException("没有上架版本");
        }

        AppPublic appPublic = this.getById(app.getPublicId());
        if (appPublic == null) {
            throw new RuntimeException("没有上架版本");
        }
        if (appPublic.getAuditStatus() != AuditStatus.PASSED) {
            throw new RuntimeException("请等待审核通过");
        }

        AppPublic recordExits = this.lambdaQuery()
                .eq(AppPublic::getAppId, appId)
                .ne(AppPublic::getAuditStatus, AuditStatus.PASSED)
                .one();
        if (recordExits != null) {
            throw new RuntimeException("请先删除旧的申请！");
        }

        appPublic.setId(null);
        appPublic.setType("down");
        appPublic.setAuditStatus(AuditStatus.PENDING);
        appPublic.setCreateTime(null);
        appPublic.setUpdateTime(null);
        this.save(appPublic);
    }

    @Override
    public void withdraw(String publicId) {
        AppPublic recordExits = this.lambdaQuery()
                .eq(AppPublic::getId, publicId)
                .eq(AppPublic::getCreateById, UserCurrentUtils.getUserId())
                .eq(AppPublic::getAuditStatus, AuditStatus.PENDING)
                .one();
        if (recordExits == null) {
            throw new RuntimeException("不存在申请信息！");
        }

        recordExits.setAuditStatus(AuditStatus.NO_FLOW);
        this.updateById(recordExits);
    }

    @Override
    public void deleteApply(String publicId) {
        AppPublic recordExits = this.lambdaQuery()
                .eq(AppPublic::getId, publicId)
                .eq(AppPublic::getCreateById, UserCurrentUtils.getUserId())
                .ne(AppPublic::getAuditStatus, AuditStatus.PASSED)
                .one();
        if (recordExits == null) {
            throw new RuntimeException("不存在申请信息！");
        }
        this.removeById(publicId);
        appPublicCategoryService.lambdaUpdate()
                .eq(AppPublicCategory::getPublicId, publicId)
                .remove();
    }

    @Override
    @Transactional
    public void examine(AppPublicExamineVO params) {
        if (params.getPublicIds().isEmpty()) {
            throw new RuntimeException("请选择要审核的申请");
        }
        if (params.getType() == null) {
            throw new RuntimeException("请选择审核类型");
        }
        if (!params.getAuditStatus().equals(AuditStatus.PASSED) && !params.getAuditStatus().equals(AuditStatus.ROLLBACK)) {
            throw new RuntimeException("审核状态有误");
        }

        List<AppPublic> records = this.lambdaQuery()
                .in(AppPublic::getId, params.getPublicIds())
                .eq(AppPublic::getAuditStatus, AuditStatus.PENDING)
                .eq(AppPublic::getType, params.getType())
                .list();
        if (records.isEmpty()) {
            throw new RuntimeException("没有待审核的申请");
        }

        if (records.size() != params.getPublicIds().size()) {
            throw new RuntimeException("非法操作哦");
        }

        String type = params.getType();
        String currentUserRealName = UserCurrentUtils.getRealName();

        AppMarketService appMarketService = SpringContextUtil.getBean(AppMarketService.class);

        records.forEach(record -> {

            if (params.getAuditStatus().equals(AuditStatus.PASSED)) {
                Apps app = appsService.getById(record.getAppId());
                if (app == null) {
                    throw new RuntimeException("应用不存在");
                }
                app.setPublicId(record.getId());
                app.setPublicId2("");
                appsService.updateById(app);

                // 上架审核通过后，将记录写入应用市场表
                if (type.equals("up")) {
                    // todo 处理其他版本
                    appMarketService.lambdaUpdate()
                            .eq(AppMarket::getAppId, app.getId())
                            .set(AppMarket::getStatus, false)
                            .update();
                    // 保存到应用市场
                    AppMarket appMarket = new AppMarket();
                    appMarket.setAppId(app.getId());
                    appMarket.setPublicId(record.getId());
                    appMarket.setStatus(true);
                    appMarketService.save(appMarket);
                }

                if (type.equals("down")) {
                    // 更新状态
                    appMarketService.updateStatus(app.getId(), record.getId(), false);
                }
            }

            record.setAuditStatus(params.getAuditStatus());
            record.setAuditOpinion(params.getOpinion());
            record.setAuditBy(currentUserRealName);
            this.updateById(record);
        });

        log.info("\n【应用信息审核】" + "\n 操作人：" + currentUserRealName+ "内容：\n" + JSONObject.toJSONString(params));

    }

}
