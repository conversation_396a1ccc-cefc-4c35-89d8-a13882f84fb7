<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.app.mapper.AppCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.app.entity.data.AppCategory">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="desc" />
        <result column="status" property="status" />
        <result column="sort" property="order" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, status, sort, create_time, update_time, create_by, update_by, del_flag
    </sql>

</mapper>
