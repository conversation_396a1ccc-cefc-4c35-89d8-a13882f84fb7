package cn.bctools.ai.knowledge.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 审核状态
 */
@Getter
@AllArgsConstructor
public enum AuditStatus {
    //未发起
    NO_FLOW(-1),
    //待审核
    PENDING(0),
    //已通过
    PASSED(1),
    //已退回
    ROLLBACK(2);

    @JsonValue
    @EnumValue
    private final Integer value;

}