package cn.bctools.ai.knowledge.exception;

import cn.bctools.common.exception.BusinessException;

import java.io.Serial;
import java.io.Serializable;

public class DocumentIsDeletedPauseError extends BusinessException implements Serializable {
    @Serial
    private static final long serialVersionUID = -1;

    public DocumentIsDeletedPauseError(String message, int code, Throwable cause) {
        super(message, code, cause);
    }

    public DocumentIsDeletedPauseError(String message, int code) {
        super(message, code);
    }

    public DocumentIsDeletedPauseError(String message, Object... params) {
        super(message, params);
    }

    public DocumentIsDeletedPauseError(String message) {
        super(message);
    }
}