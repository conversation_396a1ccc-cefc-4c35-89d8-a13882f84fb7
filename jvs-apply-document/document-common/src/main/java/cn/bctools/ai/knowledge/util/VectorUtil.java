package cn.bctools.ai.knowledge.util;

import cn.bctools.common.utils.ObjectNull;

import java.util.Objects;

/**
 * <AUTHOR>
 * 向量相关工具类
 */
public class VectorUtil {

    private static final String VECTOR_START = "[";
    private static final String VECTOR_END = "]";
    private static final String VECTOR_SEPARATOR = ",";

    //单精度数组转向量数据
    public static String floatToVectorString(float[] vector) {
        if (Objects.isNull(vector) || vector.length == 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(VECTOR_START);
        for (int j = 0; j < vector.length; j++) {
            if (j > 0) {
                sb.append(VECTOR_SEPARATOR);
            }
            sb.append(vector[j]);
        }
        sb.append(VECTOR_END);
        return sb.toString();
    }
}