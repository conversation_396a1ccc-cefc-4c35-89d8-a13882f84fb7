package cn.bctools.ai.app.mapper;

import cn.bctools.ai.app.entity.data.Sites;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 网站表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface SitesMapper extends BaseMapper<Sites> {

}
