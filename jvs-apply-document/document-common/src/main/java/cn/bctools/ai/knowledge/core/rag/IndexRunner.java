package cn.bctools.ai.knowledge.core.rag;

import cn.bctools.ai.common.config.async.AsyncConfig;
import cn.bctools.ai.common.util.ExceptionUtil;
import cn.bctools.ai.common.util.TokenCalculateUtil;
import cn.bctools.ai.knowledge.core.documentparse.DocumentParseContext;
import cn.bctools.ai.knowledge.core.segmentation.SegmentationProcessor;
import cn.bctools.ai.knowledge.core.segmentation.SegmentationProcessorFactory;
import cn.bctools.ai.knowledge.entity.bean.document.*;
import cn.bctools.ai.knowledge.entity.bean.knowledge.EmbeddingModelSetting;
import cn.bctools.ai.knowledge.entity.bean.processrule.ProcessRule;
import cn.bctools.ai.knowledge.entity.constant.IndexingTechnique;
import cn.bctools.ai.knowledge.entity.constant.RKey;
import cn.bctools.ai.knowledge.entity.data.ChildChunk;
import cn.bctools.ai.knowledge.entity.data.ItemSegment;
import cn.bctools.ai.knowledge.entity.data.Knowledge;
import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.enums.DocForm;
import cn.bctools.ai.knowledge.entity.enums.ProcessStatus;
import cn.bctools.ai.knowledge.exception.DocumentIsPausedError;
import cn.bctools.ai.knowledge.service.ChildChunksService;
import cn.bctools.ai.knowledge.service.ItemSegmentsService;
import cn.bctools.ai.knowledge.service.KnowledgeItemService;
import cn.bctools.ai.knowledge.service.KnowledgeService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import dev.langchain4j.community.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 文档索引主运行类
 */
@Component
@Slf4j
public class IndexRunner {

    @Resource
    @Lazy
    private KnowledgeService knowledgeService;

    @Resource
    @Lazy
    private KnowledgeItemService itemService;

    @Resource
    private DocumentStore documentStore;

    @Resource
    private ItemSegmentsService segmentService;

    @Resource
    private ChildChunksService chunksService;

    @Resource
    @Lazy
    private KeywordProcessor keywordProcessor;

    @Resource(name = AsyncConfig.COMMON_EXECUTOR_BEAN)
    private Executor taskExecutor;

    @Resource
    private RedisUtils redisUtils;

    @Value("${oss.name}")
    private String ossName;


    public void run(List<KnowledgeItem> items) {
        for (KnowledgeItem item : items) {
            try {
                Knowledge knowledge = knowledgeService.getExistById(item.getKnowId());
                ProcessRule processRule = item.getProcessRule();
                if (Objects.isNull(processRule)) {
                    throw new BusinessException("文档分段设置为空");
                }

                SegmentationProcessor processor = SegmentationProcessorFactory.getProcessor(item.getDocForm());

                //提取文档
                List<KDocument> textDocs = this.extract(item);

                //分段处理
                List<KDocument> documents = this.transform(processor, knowledge, textDocs, item.getDocLanguage(), processRule);

                //保存文本块segments
                this.loadSegments(knowledge, item, documents);

                //保存向量数据
                this.load(processor, knowledge, item, documents);

                //自定义异常，处理其他类型异常
            } catch (DocumentIsPausedError e) {
                log.error("文档处理处于暂停状态，停止处理，文档id: ( {} )", item.getId());
                throw e;
            } catch (Exception e) {
                log.error("indexRunner处理器 run 处理文档失败", e);
                item.setIndexingStatus(ProcessStatus.ERROR);
                item.setError(ExceptionUtil.gatherInfo(e));
                item.setStoppedTime(LocalDateTime.now());
                itemService.updateById(item);
            }
        }
    }

    //在分段状态执行
    public void runInSplittingStatus(KnowledgeItem item) {
        try {
            //检验知识库是否存在
            Knowledge knowledge = knowledgeService.getExistById(item.getKnowId());

            //获取存在的文本块，进行删除
            List<String> segmentIds = segmentService.lambdaQuery().eq(ItemSegment::getKnowId, item.getKnowId())
                    .eq(ItemSegment::getKiId, item.getId()).list().stream().map(ItemSegment::getId).toList();
            if (ObjectNull.isNotNull(segmentIds)) {
                segmentService.removeByIds(segmentIds);
                chunksService.remove(new LambdaQueryWrapper<ChildChunk>().in(ChildChunk::getSegmentId, segmentIds));
            }

            //获取处理规则
            if (Objects.isNull(item.getProcessRule())) {
                throw new BusinessException("文档处理规则不存在");
            }

            SegmentationProcessor processor = SegmentationProcessorFactory.getProcessor(item.getDocForm());
            //提取文本内容
            List<KDocument> textDocs = this.extract(item);
            //清洗分段处理，转化文本块
            List<KDocument> documents = this.transform(processor, knowledge, textDocs,
                    item.getDocLanguage(), item.getProcessRule());
            //保存文本块+子块
            this.loadSegments(knowledge, item, documents);

            //保存关键词表、向量数据
            this.load(processor, knowledge, item, documents);
        } catch (DocumentIsPausedError e) {
            log.error("文档处理处于暂停状态，停止处理，文档id: ( {} )", item.getId());
            throw e;
        } catch (Exception e) {
            log.error("indexRunner处理器 runInSplittingStatus 处理文档失败", e);
            item.setIndexingStatus(ProcessStatus.ERROR);
            item.setError(ExceptionUtil.gatherInfo(e));
            item.setStoppedTime(LocalDateTime.now());
            itemService.updateById(item);
        }
    }

    //在索引状态执行
    public void runInIndexingStatus(KnowledgeItem item) {
        try {
            Knowledge knowledge = knowledgeService.getExistById(item.getKnowId());
            //获取存在的文本块，进行删除
            List<ItemSegment> segments = segmentService.lambdaQuery().eq(ItemSegment::getKnowId, item.getKnowId())
                    .eq(ItemSegment::getKiId, item.getId()).list();
            List<KDocument> documents = new ArrayList<>();
            for (ItemSegment segment : segments) {
                if (!Objects.equals(segment.getStatus(), ProcessStatus.COMPLETED)) {
                    KDocument document = new KDocument(segment.getContent(),
                            segment.getIndexNodeId(),segment.getIndexNodeHash(),
                            segment.getKiId(),segment.getKnowId());

                    if (Objects.equals(item.getDocForm(), DocForm.HIERARCHICAL_MODEL)) {
                        List<ChildChunk> childChunks = chunksService.getSegmentChildChunks(segment.getId(),
                                item.getProcessRule());
                        if (ObjectNull.isNotNull(childChunks)) {
                            List<KDocument> childDocuments = new ArrayList<>();
                            for (ChildChunk childChunk : childChunks) {
                                KDocument childDocument = new KDocument(childChunk.getContent(),
                                        childChunk.getIndexNodeId(),childChunk.getIndexNodeHash(),
                                        segment.getKiId(),segment.getKnowId());
                                childDocuments.add(childDocument);
                            }
                            document.setChilds(childDocuments);
                        }
                    }
                    documents.add(document);
                }
            }
            SegmentationProcessor processor = SegmentationProcessorFactory.getProcessor(item.getDocForm());
            //保存关键词表、向量数据
            this.load(processor, knowledge, item, documents);
        } catch (DocumentIsPausedError e) {
            log.error("文档处理处于暂停状态，停止处理，文档id: ( {} )", item.getId());
            throw e;
        } catch (Exception e) {
            log.error("indexRunner处理器 runInSplittingStatus 处理文档失败", e);
            item.setIndexingStatus(ProcessStatus.ERROR);
            item.setError(ExceptionUtil.gatherInfo(e));
            item.setStoppedTime(LocalDateTime.now());
            itemService.updateById(item);
        }
    }

    //模拟分段
    public IndexingEstimateDetail indexingEstimate(List<ExtractSetting> extractSettings, ProcessRule tmpProcessRule, DocForm docForm,
                                                   String docLanguage, String knowId, String indexingTechnique) {
        EmbeddingModelSetting embeddingModel = null;
        if (Objects.nonNull(knowId)) {
            Knowledge knowledge = knowledgeService.getExistById(knowId);
            if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique()) ||
                    IndexingTechnique.isHighQuality(indexingTechnique)) {
                if (ObjectNull.isNotNull(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel())) {
                    embeddingModel = new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(),
                            knowledge.getEmbeddingModel());
                } else {
                    //使用系统默认
                    embeddingModel = new EmbeddingModelSetting();
                }
            }
        } else {
            //使用系统默认
            embeddingModel = new EmbeddingModelSetting();
        }

        List<PreviewDetail> previewTexts = new ArrayList<>();
        List<QAPreviewDetail> qaPreviewTexts = new ArrayList<>();
        int totalSegments = 0;
        SegmentationProcessor processor = SegmentationProcessorFactory.getProcessor(docForm);
        for (ExtractSetting extractSetting : extractSettings) {
            //文档解析
            List<KDocument> textDocs = new ArrayList<>(
                    Collections.singletonList(DocumentParseContext.get(ossName).parse(extractSetting.getSourceInfo())));
            List<KDocument> documents = processor.transform(textDocs, embeddingModel, tmpProcessRule, true);
            totalSegments += documents.size();
            for (KDocument document : documents) {
                if (previewTexts.size() < 10 && qaPreviewTexts.size() < 10) {
                    if (Objects.equals(DocForm.QA_MODEL, docForm)) {
                        qaPreviewTexts.add(new QAPreviewDetail());
                        //todo 问答对处理
                    } else {
                        //文本+分层模式处理
                        PreviewDetail previewDetail = new PreviewDetail();
                        previewDetail.setContent(document.text());
                        if (ObjectNull.isNotNull(document.getChilds())) {
                            List<String> childTexts = document.getChilds().stream().map(KDocument::text).toList();
                            previewDetail.setChildChunks(childTexts);
                        }
                        previewTexts.add(previewDetail);
                    }
                }
            }
        }

        IndexingEstimateDetail estimateDetail = new IndexingEstimateDetail();
        if (Objects.equals(DocForm.QA_MODEL, docForm)) {
            estimateDetail.setTotalSegments(totalSegments);
            estimateDetail.setQaPreview(qaPreviewTexts);
        } else {
            estimateDetail.setTotalSegments(totalSegments);
            estimateDetail.setPreview(previewTexts);
        }
        return estimateDetail;
    }

    //提取文本内容
    public List<KDocument> extract(KnowledgeItem item) {
        if (ObjectNull.isNull(item.getSourceInfo())) {
            item.setSourceInfo(item.extractFileInfo());
        }

        //文档解析
        List<KDocument> textDocs = new ArrayList<>(
                Collections.singletonList(DocumentParseContext.get(ossName).parse(item.getSourceInfo())));

        //计算文本数量
        Integer wordCount = textDocs.stream().map(e -> StrUtil.length(e.getText()))
                .mapToInt(e -> e).sum();
        //更新状态
        KnowledgeItem ki = new KnowledgeItem();
        ki.setId(item.getId());
        ki.setIndexingStatus(ProcessStatus.SPLITTING);
        ki.setWordCount(wordCount);
        ki.setParsingCompletedTime(LocalDateTime.now());
        itemService.updateCheck(ki);

        textDocs.forEach(e -> {
            JSONObject metadata = e.getMetadata();
            if (Objects.nonNull(metadata)) {
                e.setItemId(item.getId());
                e.setKnowId(item.getKnowId());
            }
        });

        return textDocs;
    }

    //清洗分段处理，转化文本内容
    public List<KDocument> transform(SegmentationProcessor processor, Knowledge knowledge, List<KDocument> textDocs,
                                     String docLanguage, ProcessRule processRule) {
        EmbeddingModelSetting embeddingModel = null;
        if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique())) {
            if (ObjectNull.isNotNull(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel())) {
                embeddingModel = new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(),
                        knowledge.getEmbeddingModel());
            } else {
                //使用系统默认
                embeddingModel = new EmbeddingModelSetting();
            }
        }

        return processor.transform(textDocs, embeddingModel, processRule, false);
    }

    //保存文本+子块到数据库
    public void loadSegments(Knowledge knowledge, KnowledgeItem item, List<KDocument> documents) {
        //保存到mysql
        documentStore.addDocuments(knowledge, item.getId(), documents, true,
                Objects.equals(item.getDocForm(), DocForm.HIERARCHICAL_MODEL));

        //更新处理信息
        LocalDateTime now = LocalDateTime.now();
        KnowledgeItem updateItem = new KnowledgeItem();
        updateItem.setId(item.getId());
        updateItem.setIndexingStatus(ProcessStatus.INDEXING);
        //todo 有待考究是否在此处设置文本内容清洗时间
        updateItem.setCleaningCompletedTime(now);
        updateItem.setSplittingCompletedTime(now);
        itemService.updateCheck(updateItem);

        segmentService.lambdaUpdate().eq(ItemSegment::getKiId, updateItem.getId())
                .set(ItemSegment::getStatus, ProcessStatus.INDEXING)
                .set(ItemSegment::getIndexingTime, LocalDateTime.now()).update();
    }

    //保存关键词表、向量数据
    public void load(SegmentationProcessor processor, Knowledge knowledge, KnowledgeItem item, List<KDocument> documents) {
        StopWatch sw = new StopWatch();
        sw.start();

        //如果不是分层结构，需要处理关键词（用于全文检索）
        CompletableFuture<Void> keywordTask = null;
        if (!Objects.equals(item.getDocForm(), DocForm.HIERARCHICAL_MODEL)) {
            // 创建关键词索引
            keywordTask = CompletableFuture.runAsync(() -> this.processKeywordIndex(knowledge, item.getId(), documents), taskExecutor);
        }


        int tokens = 0;
        //更新文档信息，统计tokens总数
        if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique())) {
            //限制分组数量，根据哈希值分组，防止多线程处理相同hash文档插入死锁
            List<List<KDocument>> documentGroups =
                    Stream.generate(ArrayList<KDocument>::new)
                            .limit(AsyncConfig.MAX_TASK)
                            .collect(Collectors.toList());

            for (KDocument document : documents) {
                String hash = document.getHash();
                // 将十六进制哈希字符串转换为 BigInteger，然后取模
                BigInteger hashValue = new BigInteger(hash, 16);
                int groupIndex = hashValue.mod(BigInteger.valueOf(AsyncConfig.MAX_TASK)).intValue();
                documentGroups.get(groupIndex).add(document);
            }

            List<CompletableFuture<Integer>> futures = documentGroups.stream()
                    .filter(chunks -> !chunks.isEmpty())
                    .map(chunks ->
                            CompletableFuture.supplyAsync(() ->
                                    this.processChunk(processor, chunks, knowledge, item), taskExecutor))
                    .toList();

            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

            //阻塞获取所有结果
            tokens = allFutures.thenApply(e -> futures.stream().map(CompletableFuture::join)
                    .mapToInt(Integer::intValue)
                    .sum()).join();
        }

        //阻塞执行，统计耗时
        //如果是父子结构则不处理关键词表
        if (!Objects.equals(item.getDocForm(), DocForm.HIERARCHICAL_MODEL)) {
            keywordTask.join();
        }

        sw.stop();
        KnowledgeItem updateItem = new KnowledgeItem();
        updateItem.setId(item.getId());
        updateItem.setIndexingStatus(ProcessStatus.COMPLETED);
        updateItem.setTokens(tokens);
        updateItem.setCompletedTime(LocalDateTime.now());
        updateItem.setIndexingLatency((int) sw.getTotalTimeMillis());
        updateItem.setError("");
        itemService.updateCheck(updateItem);

    }

    //更新文档信息，统计tokens总数
    private Integer processChunk(SegmentationProcessor processor, List<KDocument> chunks,
                                 Knowledge knowledge, KnowledgeItem item) {
        //检验是否终止
        checkDocumentPaused(item.getId());

        if (ObjectNull.isNull(chunks)) {
            return 0;
        }

        int tokens = chunks.stream().map(e -> TokenCalculateUtil.getTokenNumOfContent(e.text()))
                .mapToInt(Integer::intValue).sum();

        //保存向量数据
        processor.load(knowledge, chunks, false, null);

        List<String> docIds = chunks.stream().map(KDocument::getDocId).toList();

        //更新完成状态
        segmentService.lambdaUpdate()
                .eq(ItemSegment::getKnowId, knowledge.getId())
                .eq(ItemSegment::getKiId, item.getId())
                .eq(ItemSegment::getStatus, ProcessStatus.INDEXING)
                .in(ItemSegment::getIndexNodeId, docIds)
                .set(ItemSegment::getStatus, ProcessStatus.COMPLETED)
                .set(ItemSegment::getEnabled, true)
                .set(ItemSegment::getCompletedTime, LocalDateTime.now())
                .update();

        return tokens;
    }


    /**
     * 处理文档关键词索引
     *
     * @param knowledge 知识库信息
     * @param itemId    文档id
     * @param documents 文档信息数组
     */
    public void processKeywordIndex(Knowledge knowledge, String itemId, List<KDocument> documents) {
        //处理创建关键词信息
        keywordProcessor.create(knowledge.getId(), documents);

        if (IndexingTechnique.isEconomy(knowledge.getIndexingTechnique())) {
            List<String> indexNodeIds = documents.stream().map(KDocument::getDocId).toList();
            //更新文本块状态、设置完成时间
            segmentService.lambdaUpdate().eq(ItemSegment::getKnowId, knowledge.getId())
                    .eq(ItemSegment::getKiId, itemId)
                    .eq(ItemSegment::getStatus, ProcessStatus.INDEXING)
                    .in(ItemSegment::getIndexNodeId, indexNodeIds)
                    .set(ItemSegment::getStatus, ProcessStatus.COMPLETED)
                    .set(ItemSegment::getEnabled, true)
                    .set(ItemSegment::getCompletedTime, LocalDateTime.now()).update();
        }
    }

    /**
     * 检查是否终止
     *
     * @param itemId 文档id
     */
    private void checkDocumentPaused(String itemId) {
        String key = RKey.ITEM_PAUSED + itemId;
        Boolean flag = redisUtils.hasKey(key);
        if (flag) {
            throw new DocumentIsPausedError(String.format("文档( %s )处理被暂停，无法继续处理", itemId));
        }
    }

}