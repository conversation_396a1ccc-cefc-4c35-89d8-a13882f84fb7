package cn.bctools.ai.knowledge.core.rag;

import cn.bctools.ai.knowledge.core.pgvector.PgVector;
import cn.bctools.ai.knowledge.core.pgvector.PgVectorFactory;
import cn.bctools.ai.knowledge.core.segmentation.SegmentationProcessor;
import cn.bctools.ai.knowledge.core.segmentation.SegmentationProcessorFactory;
import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.ai.knowledge.entity.bean.knowledge.EmbeddingModelSetting;
import cn.bctools.ai.knowledge.entity.bean.processrule.ProcessRule;
import cn.bctools.ai.knowledge.entity.constant.IndexingTechnique;
import cn.bctools.ai.knowledge.entity.data.ChildChunk;
import cn.bctools.ai.knowledge.entity.data.ItemSegment;
import cn.bctools.ai.knowledge.entity.data.Knowledge;
import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.enums.DocForm;
import cn.bctools.ai.knowledge.entity.enums.ParentMode;
import cn.bctools.ai.knowledge.service.ChildChunksService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.model.model_runtime.enums.ModelType;
import cn.hutool.core.util.StrUtil;
import dev.langchain4j.community.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * 文本块、子块向量处理服务类
 */
@Service
public class VectorService {

    @Resource
    @Lazy
    ChildChunksService chunkService;

    @Resource
    @Lazy
    PgVectorFactory pgVectorFactory;

    @Resource
    @Lazy
    KeywordProcessor keywordProcessor;

    //创建文本块向量数据
    public void createSegmentsVector(List<ItemSegment> segments, List<Set<String>> keywordsList, Knowledge knowledge,
                                     KnowledgeItem item) {
        boolean hierarchical = DocForm.HIERARCHICAL_MODEL.equals(item.getDocForm());
        boolean highQuality = IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique());
        List<KDocument> documents = new ArrayList<>();
        for (ItemSegment segment : segments) {
            if (hierarchical) {
                if (Objects.isNull(item.getProcessRule())) {
                    throw new BusinessException("文档处理规则不存在");
                }

                if (highQuality) {
                    EmbeddingModelSetting embeddingModel = null;
                    if (ObjectNull.isNotNull(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel())) {
                        embeddingModel = new EmbeddingModelSetting(
                                knowledge.getEmbeddingModelProvider(),
                                knowledge.getEmbeddingModel());
                    } else {
                        //使用系统默认
                        embeddingModel = new EmbeddingModelSetting();
                    }

                    this.generateChildChunks(knowledge, item, segment, embeddingModel,
                            item.getProcessRule(), false);
                }
            } else {
                KDocument document = new KDocument(segment.getContent(), segment.getIndexNodeId(),
                        segment.getIndexNodeHash(), segment.getKiId(), segment.getKnowId());
                documents.add(document);
            }
        }
        if (!documents.isEmpty()) {
            //存储向量数据，保存关键词信息
            SegmentationProcessor processor = SegmentationProcessorFactory.getProcessor(item.getDocForm());
            processor.load(knowledge, documents, true, keywordsList);
        }
    }

    //更新文本块向量数据
    public void updateSegmentVector(Knowledge knowledge, ItemSegment segment, Set<String> keyWords) {
        KDocument document = new KDocument(segment.getContent(), segment.getIndexNodeId(), segment.getIndexNodeHash(),
                segment.getKiId(), segment.getKnowId());
        List<KDocument> documents = List.of(document);
        if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique())) {
            //对于向量数据，先删除后增加
            PgVector pgVector = pgVectorFactory.initVector(knowledge);
            pgVector.deleteByIds(List.of(segment.getIndexNodeId()));
            //检查是否存在，存在则不重复创建
            List<String> existIds = pgVector.testExists(List.of(document.getDocId()));
            if (existIds.isEmpty()) {
                EmbeddingProcessor embeddingProcessor = pgVectorFactory.initEmbedding(
                        new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel()));
                List<float[]> floats = embeddingProcessor.embedDocuments(documents);
                pgVector.create(documents, floats);
            }
        }

        //关键词表，先删除后增加
        //删除关键词表数据
        keywordProcessor.deleteByIds(knowledge.getId(), List.of(segment.getIndexNodeId()));

        //新增关键词表数据
        if (ObjectNull.isNotNull(keyWords)) {
            keywordProcessor.addTexts(knowledge.getId(), documents, List.of(keyWords));
        } else {
            keywordProcessor.addTexts(knowledge.getId(), documents, null);
        }

    }

    //创建子块数据及索引
    //regenerate->再次生成，默认为false，为true是需要先删除后添加
    public void generateChildChunks(Knowledge knowledge, KnowledgeItem item, ItemSegment segment
            , EmbeddingModelSetting embeddingModel, ProcessRule processRule, Boolean regenerate) {
        SegmentationProcessor processor = SegmentationProcessorFactory.getProcessor(item.getDocForm());
        if (Boolean.TRUE.equals(regenerate)) {
            //先删除原子块数据
            processor.clean(knowledge, List.of(segment.getIndexNodeId()), true, true);
        }

        //生成新子块
        KDocument document = new KDocument(segment.getContent(), segment.getIndexNodeId(), segment.getIndexNodeHash(),
                segment.getKiId(), segment.getKnowId());
        //使用全文模式生成文本块的子块
        processRule.getRules().setParentMode(ParentMode.FULL_DOC);
        //获取分割后的文档信息
        List<KDocument> documents = processor.transform(List.of(document), embeddingModel, processRule, false);

        //保存子块
        if (ObjectNull.isNotNull(documents) && ObjectNull.isNotNull(documents.get(0).getChilds())) {
            processor.load(knowledge, documents, true, null);

            int position = 1;
            List<ChildChunk> childChunks = new ArrayList<>();
            for (KDocument childDocument : documents.get(0).getChilds()) {
                ChildChunk childChunk = new ChildChunk();
                childChunk.setKnowId(knowledge.getId());
                childChunk.setKiId(item.getId());
                childChunk.setSegmentId(segment.getId());
                childChunk.setPosition(position++);
                childChunk.setIndexNodeId(childDocument.getDocId());
                childChunk.setIndexNodeHash(childDocument.getHash());
                childChunk.setContent(childDocument.text());
                childChunk.setWordCount(StrUtil.length(childDocument.text()));
                childChunk.setType("automatic");
                childChunks.add(childChunk);
            }
            chunkService.saveBatch(childChunks);
        }
    }

    //创建子块向量数据
    public void createChildChunk(Knowledge knowledge, ChildChunk childChunk) {
        //只有高质量才有子块向量数据
        if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique())) {
            KDocument document = new KDocument(childChunk.getContent(), childChunk.getIndexNodeId(),
                    childChunk.getIndexNodeHash(), childChunk.getKiId(), childChunk.getKnowId());

            //创建向量数据
            PgVector pgVector = pgVectorFactory.initVector(knowledge);
            //检查是否存在，存在则不重复创建
            List<String> existIds = pgVector.testExists(List.of(document.getDocId()));
            if (existIds.isEmpty()) {
                List<KDocument> documents = List.of(document);
                EmbeddingProcessor embeddingProcessor = pgVectorFactory.initEmbedding(
                        new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel()));
                List<float[]> floats = embeddingProcessor.embedDocuments(documents);
                pgVector.create(documents, floats);
            }

        }
    }

    //更新子块向量数据
    public void updateChildChunkVector(Knowledge knowledge, List<ChildChunk> newChildChunks,
                                       List<ChildChunk> updateChildChunks, List<String> deleteChildChunkIds) {
        List<KDocument> documents = new ArrayList<>();
        List<String> deleteNodeIds = new ArrayList<>();
        for (ChildChunk newChildChunk : newChildChunks) {
            KDocument newChildDocument = new KDocument(newChildChunk.getContent(), newChildChunk.getIndexNodeId(),
                    newChildChunk.getIndexNodeHash(), newChildChunk.getKiId(), newChildChunk.getKnowId());
            documents.add(newChildDocument);
        }

        //更新的子块先删除后添加
        for (ChildChunk updateChildChunk : updateChildChunks) {
            KDocument childDocument = new KDocument(updateChildChunk.getContent(), updateChildChunk.getIndexNodeId(),
                    updateChildChunk.getIndexNodeHash(), updateChildChunk.getKiId(), updateChildChunk.getKnowId());
            documents.add(childDocument);
            deleteNodeIds.add(updateChildChunk.getIndexNodeId());
        }

        deleteNodeIds.addAll(deleteChildChunkIds);

        if (IndexingTechnique.isHighQuality(knowledge.getIndexingTechnique())) {
            PgVector pgVector = pgVectorFactory.initVector(knowledge);
            if (ObjectNull.isNotNull(deleteNodeIds)) {
                pgVector.deleteByIds(deleteNodeIds);
            }

            if (ObjectNull.isNotNull(documents)) {
                //去除已存在文档
                List<String> nodeIds = documents.stream().map(KDocument::getDocId).toList();
                List<String> existIds = pgVector.testExists(nodeIds);
                documents = documents.stream().filter(e -> !existIds.contains(e.getDocId())).toList();
                EmbeddingProcessor embeddingProcessor = pgVectorFactory.initEmbedding(
                        new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(), knowledge.getEmbeddingModel()));
                List<float[]> floats = embeddingProcessor.embedDocuments(documents);
                pgVector.create(documents, floats);
            }
        }
    }

    //删除子块向量数据
    public void deleteChildChunkVector(Knowledge knowledge, ChildChunk childChunk) {
        PgVector pgVector = pgVectorFactory.initVector(knowledge);
        pgVector.deleteByIds(List.of(childChunk.getIndexNodeId()));
    }

}