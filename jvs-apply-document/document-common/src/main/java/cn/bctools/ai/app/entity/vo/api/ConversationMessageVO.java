package cn.bctools.ai.app.entity.vo.api;

import lombok.Data;

import java.util.Map;

@Data
public class ConversationMessageVO {

    private String id;

    private String agent_thoughts;

    private String answer;

    private String conversation_id;

    private feedbackVO feedback;

    private Map<String, String> inputs;

    private String parent_message_id;

    private String query;

    @Data
    public static class feedbackVO {
        private String rating;
    }



}
