package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.apps.advanced_chat.AdvancedChatAppGenerator;
import cn.bctools.ai.app.apps.chat.ChatAppGenerator;
import cn.bctools.ai.app.apps.completion.CompletionAppGenerator;
import cn.bctools.ai.app.apps.workflow.WorkflowAppGenerator;
import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.DefaultConfig;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.error.AppInvokeQuotaExceededError;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.app.service.RuleRunService;
import cn.bctools.ai.app.service.WorkflowService;
import cn.bctools.ai.app.utils.RateLimit;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.design.rule.entity.RuleDesignPo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Optional;

/**
 * 对话服务
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Slf4j
@Service
@AllArgsConstructor
public class AppGenerateServiceImpl implements AppGenerateService {
    WorkflowService workflowService;

    @Override
    public Object generate(HttpServletRequest request, HttpServletResponse response, AppDetail appDetail,
                           UserDto userDto, Map<String, Object> args, InvokeFrom invokeFrom, boolean streaming) {
        // todo 系统级限流
        // todo 应用级限流
        // app level rate limiter
        int maxActiveRequest = getMaxActiveRequests(appDetail);
        RateLimit rateLimit = new RateLimit(appDetail.getId(), maxActiveRequest);
        String requestId = RateLimit.genRequestKey();
        RuleDesignPo ruleDesignPo;
        try {
            requestId = rateLimit.enter(requestId);
            switch (AppMode.fromValue(appDetail.getMode())) {
                case COMPLETION:
                    return rateLimit.generate(
                            CompletionAppGenerator.convertToEventStream(
                                    new CompletionAppGenerator().generate(
                                            appDetail, userDto, args, invokeFrom, streaming
                                    )
                            ),
                            requestId
                    );
                case AGENT_CHAT:
                    throw new BusinessException("暂不支持该模式");
//                    return rateLimit.generate(
//                            AgentChatAppGenerator.convertToEventStream(
//                                    new AgentChatAppGenerator().generate(
//                                            appModel, user, args, invokeFrom, streaming
//                                    )
//                            ),
//                            requestId
//                    );
                case CHAT:
                    return rateLimit.generate(
                            ChatAppGenerator.convertToEventStream(
                                    new ChatAppGenerator().generate(
                                            appDetail, userDto, args, invokeFrom, streaming
                                    )
                            ),
                            requestId
                    );
                case ADVANCED_CHAT:
                    ruleDesignPo = getWorkflow(appDetail, invokeFrom);
                    return rateLimit.generate(
                            AdvancedChatAppGenerator.convertToEventStream(
                                    new AdvancedChatAppGenerator().generate(
                                            request, response, appDetail, ruleDesignPo, userDto, args, invokeFrom, streaming
                                    )
                            ),
                            requestId
                    );
                case WORKFLOW:
                    ruleDesignPo = getWorkflow(appDetail, invokeFrom);
                    return rateLimit.generate(
                            WorkflowAppGenerator.convertToEventStream(
                                    new WorkflowAppGenerator().generate(
                                            request, response, appDetail, ruleDesignPo, userDto, args, invokeFrom, streaming
                                    )
                            ),
                            requestId
                    );
                default:
                    throw new BusinessException("Invalid app mode " + appDetail.getMode());
            }
        } catch (AppInvokeQuotaExceededError e) {
            throw new BusinessException("应用调用次数已达到上限");
        } catch (Exception e) {
            rateLimit.exit(requestId);
            throw e;
        } finally {
            if (!streaming) {
                rateLimit.exit(requestId);
            }
        }
    }

    @Override
    public Object generateSingleLoop(HttpServletRequest request, HttpServletResponse response, AppDetail appDetail, UserDto userDto, Map<String, Object> args, InvokeFrom invokeFrom, boolean streaming) {
        return null;
    }

    private static int getMaxActiveRequests(AppDetail appDetail) {
        return Optional.ofNullable(appDetail.getMaxActiveRequests())
                .orElse(DefaultConfig.APP_MAX_ACTIVE_REQUESTS);
    }

    private RuleDesignPo getWorkflow(AppDetail appDetail, InvokeFrom invokeFrom) {
        RuleDesignPo ruleDesignPo;
        if (invokeFrom.equals(InvokeFrom.DEBUGGER)) {
            ruleDesignPo = workflowService.getDraftWorkflow(appDetail);
            if (ruleDesignPo == null) {
                throw new BusinessException("工作流不存在");
            }
        } else {
            ruleDesignPo = workflowService.getPublishedWorkflow(appDetail);
            if (ruleDesignPo == null) {
                throw new BusinessException("工作流未发布");
            }
        }
        return ruleDesignPo;
    }
}