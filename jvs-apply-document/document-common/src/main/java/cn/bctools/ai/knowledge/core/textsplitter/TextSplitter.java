package cn.bctools.ai.knowledge.core.textsplitter;

import cn.bctools.ai.common.util.TokenCalculateUtil;
import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * 文本分段器接口
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class TextSplitter {
    // 块的最大大小 (token)
    protected int chunkSize;
    // 块之间的重叠大小
    protected int chunkOverlap;
    //暂不明确作用
    protected Boolean keepSeparator;
    // 是否在元数据中添加 'start_index' 计算起始索引
    protected Boolean addStartIndex;

    public TextSplitter(int chunkSize, int chunkOverlap, Boolean keepSeparator, Boolean addStartIndex) {
        if (chunkOverlap > chunkSize) {
            throw new BusinessException("分段重叠长度不可大于分段最大长度");
        }
        this.chunkSize = chunkSize;
        this.chunkOverlap = chunkOverlap;
        this.keepSeparator = keepSeparator;
        this.addStartIndex = addStartIndex;
    }

    /**
     * 将文档列表分割成更小的块（Document 对象列表）。
     *
     * @param documents 输入文档列表。
     * @return 分割后的文档块列表。
     */
    public List<KDocument> splitDocuments(List<KDocument> documents) {
        List<String> texts = new ArrayList<>();
        List<JSONObject> metadatas = new ArrayList<>();
        for (KDocument document : documents) {
            texts.add(document.text());
            metadatas.add(document.metadata());
        }
        return this.createDocuments(texts, metadatas);
    }

    /**
     * 将单个文本字符串分割成更小的块（字符串列表）。
     *
     * @param text 输入文本。
     * @return 分割后的文本块列表。
     */
    public abstract List<String> splitText(String text);


    //生成切割文档
    public List<KDocument> createDocuments(List<String> texts, List<JSONObject> metadatas) {
        if (ObjectNull.isNull(metadatas)) {
            metadatas = Stream.generate(JSONObject::new).limit(texts.size()).toList();
        }

        List<KDocument> documents = new ArrayList<>();

        for (int i = 0; i < texts.size(); i++) {
            int index = -1;
            List<String> chunks = splitText(texts.get(i));
            for (String chunk : chunks) {
                String text = texts.get(i);
                JSONObject metadata = new JSONObject(metadatas.get(i));
                //记录子块下标索引位置
                if (Boolean.TRUE.equals(this.addStartIndex)) {
                    index = text.indexOf(chunk, index + 1);
                    metadata.put("start_index", index);
                }
                KDocument newDoc = new KDocument(chunk, metadata);
                documents.add(newDoc);
            }
        }

        return documents;
    }

    //连接文档内容
    public String joinDocs(List<String> docs, String separator) {
        String text = Strings.join(docs, separator);
        text = text.strip();
        if (StrUtil.isEmpty(text)) {
            return null;
        }
        return text;
    }

    //合并切割文档内容+分隔符 （使得不大于maxtokens   chunksize），恰好符合边界
    public List<String> mergeSplits(List<String> splits, String separator, List<Integer> lengths) {
        int separatorLength = TokenCalculateUtil.getTokenNumOfContent(separator);
        List<String> docs = new ArrayList<>();
        List<String> currentDoc = new ArrayList<>();
        //记录总token数
        int total = 0;
        //长度下标
        int index = 0;
        for (String split : splits) {
            //分割文本块长度
            Integer len = lengths.get(index);

            //如果是第一条则不计算分隔符长度
            if (total + len + (!currentDoc.isEmpty() ? separatorLength : 0) > this.chunkSize) {
                if (total > this.chunkSize) {
                    log.warn("分割创建的文本块长度为( {} ),大于限制的文本块长度({ {} )", total, this.chunkSize);
                }
                if (!currentDoc.isEmpty()) {
                    String doc = this.joinDocs(currentDoc, separator);
                    if (Objects.nonNull(doc)) {
                        docs.add(doc);
                    }

                    //持续弹出元素
                    //1、有一个比块重叠更大的块
                    //2、长度即将到达限度，但是还有文本块未处理
                    while (total > this.chunkOverlap
                            || (total + len + (!currentDoc.isEmpty() ? separatorLength : 0) > this.chunkSize
                            && total > 0)) {
                        total -= TokenCalculateUtil.getTokenNumOfContent(currentDoc.get(0)) + (currentDoc.size() > 1 ? separatorLength : 0);
                        currentDoc.remove(0);
                    }
                }
            }

            currentDoc.add(split);
            //已有数据，非起始文本，默认计算分隔符长度
            total += len + (currentDoc.size() > 1 ? separatorLength : 0);
            index += 1;
        }
        String doc = this.joinDocs(currentDoc, separator);
        if (Objects.nonNull(doc)) {
            docs.add(doc);
        }

        return docs;
    }

    /**
     * 使用正则表达式分割文本，并可选择保留分隔符。
     * 对应 Python 代码中的 _split_text_with_regex 方法。
     *
     * @param text          要分割的文本。
     * @param separator     分隔符
     * @param keepSeparator 是否保留分隔符。
     * @return 分割后的字符串列表。
     */
    public static List<String> splitTextWithRegex(String text, String separator, boolean keepSeparator) {
        List<String> splits = new ArrayList<>();

        if (StrUtil.isNotEmpty(separator)) {
            if (Boolean.TRUE.equals(keepSeparator)) {
                //保留分隔符

                //转义分隔符
                // 将字符串 separator 转义为一个字面量（literal），防止它被当作正则表达式中的特殊字符来解析 如 \n \t
                String escapedSeparator = Pattern.quote(separator);
                //把分隔符放在括号里可以确保结果包含分隔符
                Pattern pattern = Pattern.compile("(" + escapedSeparator + ")");
                Matcher matcher = pattern.matcher(text);

                //最后一个加入字符的索引下标
                int lastEnd = 0;
                while (matcher.find()) {
                    int start = matcher.start();
                    int end = matcher.end();

                    //合并包含分隔符，将分割的两个元素并为一个元素
                    String segment = text.substring(lastEnd, start) + text.substring(start, end);
                    splits.add(segment);
                    lastEnd = end;
                }

                //处理未加入的字符串
                if (lastEnd < text.length()) {
                    splits.add(text.substring(lastEnd));
                }
            } else {
                //不保留分隔符，直接切割
                splits.addAll(Arrays.asList(text.split(Pattern.quote(separator))));
            }
        } else {
            //没有分割符，将文本拆分成单个字符
            for (char c : text.toCharArray()) {
                splits.add(String.valueOf(c));
            }
        }

        //取出空白和换行
        splits.removeIf(s -> StrUtil.isEmpty(s) || "\n".equals(s));
        return splits;
    }


}