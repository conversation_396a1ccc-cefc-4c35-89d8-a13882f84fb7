package cn.bctools.ai.knowledge.core.rerank;

import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.ai.knowledge.entity.bean.document.RerankDocument;
import cn.bctools.ai.knowledge.entity.bean.document.RerankResult;
import cn.bctools.ai.knowledge.entity.bean.knowledge.RerankingModel;
import cn.bctools.model.ModelInstance;
import cn.bctools.model.model_runtime.enums.ModelType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * 重排序模型排序执行器
 */
@RequiredArgsConstructor
@Slf4j
public class RerankModelRunner implements RerankRunner {

    private final RerankingModel rerankingModel;

    @Override
    public List<KDocument> run(String query, List<KDocument> documents, Float scoreThreshold, Integer topN) {
        List<String> docs = new ArrayList<>();
        Set<String> docIds = new HashSet<>();
        List<KDocument> uniqueDocs = new ArrayList<>();
        for (KDocument document : documents) {
            if (Objects.equals(document.getProvider(), "edf")
                    && Objects.nonNull(document.metadata())
                    && !docIds.contains(document.getDocId())) {
                docIds.add(document.getDocId());
                docs.add(document.text());
                uniqueDocs.add(document);
            } else if (Objects.equals(document.getProvider(), "external")
                    && !uniqueDocs.contains(document)) {
                docs.add(document.text());
                uniqueDocs.add(document);
            }
        }

        documents = uniqueDocs;

        ModelInstance modelInstance = new ModelInstance(rerankingModel.getRerankingProviderName(),
                rerankingModel.getRerankingModelName(), ModelType.RERANK);
        RerankResult rerankResult = modelInstance.invokeRerank(query,docs,scoreThreshold,topN);

        List<KDocument> rerankDocuments = new ArrayList<>();
        int index = 0;
        for (RerankDocument result : rerankResult.getDocs()) {
            KDocument document = documents.get(index);
            KDocument rerankDocument = new KDocument(result.getText(), document.metadata());
            rerankDocument.setProvider(document.getProvider());
            if (Objects.nonNull(rerankDocument.metadata())) {
                rerankDocument.setScore(result.getScore());
                rerankDocuments.add(rerankDocument);
            }
            index++;
        }

        return rerankDocuments;
    }
}