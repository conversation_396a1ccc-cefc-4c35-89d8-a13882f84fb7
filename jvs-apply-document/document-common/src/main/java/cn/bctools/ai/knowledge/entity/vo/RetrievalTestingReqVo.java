package cn.bctools.ai.knowledge.entity.vo;

import cn.bctools.ai.knowledge.entity.bean.knowledge.RetrievalSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 召回测试入参vo
 * <AUTHOR>
 */
@ApiModel("召回测试入参vo")
@Data
public class RetrievalTestingReqVo {

    @NotEmpty(message = "查询值不能为空")
    @ApiModelProperty("查询值")
    private String query;

    @ApiModelProperty("检索设置")
    private RetrievalSetting retrievalModel;

    @ApiModelProperty("外部知识库检索设置")
    private RetrievalSetting externalRetrievalModel;

}