<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.knowledge.mapper.KnowledgeKeywordTablesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.knowledge.entity.data.KnowledgeKeywordTable">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="know_id" property="knowId" />
        <result column="keyword_table" property="keywordTable" />
        <result column="data_source_type" property="dataSourceType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, know_id, keyword_table, data_source_type
    </sql>

</mapper>