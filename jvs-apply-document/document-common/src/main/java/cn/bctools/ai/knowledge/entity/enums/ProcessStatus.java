package cn.bctools.ai.knowledge.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 处理状态
 */
@Getter
@AllArgsConstructor
public enum ProcessStatus {

    /**
     * 待处理
     */
    WAITING("waiting", "待处理"),
    /**
     * 正在向量化
     */
    DOING("doing", "处理中"),
    /**
     * 已向量化
     */
    COMPLETED("completed", "已完成"),
    /**
     * 失败
     */
    ERROR("error", "失败"),
    /**
     * 解析中
     */
    PARSING("parsing", "解析中"),
    /**
     * 清洗中
     */
    CLEANING("cleaning", "清洗中"),
    /**
     * 分段中
     */
    SPLITTING("splitting", "分段中"),
    /**
     * 向量化中
     */
    INDEXING("indexing", "向量化中"),
    /**
     * 暂停
     */
    PAUSED("paused", "暂停");

    @EnumValue
    @JsonValue
    public final String value;
    public final String desc;

    @Override
    public String toString() {
        return desc;
    }

    public static Boolean isIndexing(ProcessStatus status) {
        return Arrays.asList(PARSING, CLEANING, SPLITTING, INDEXING).contains(status);
    }

//    public static Boolean isAvailable(ProcessStatus status) {
//        return Arrays.asList(ProcessStatus.UNDO, ProcessStatus.DOING).contains(status);
//    }

    //索引未完成的状态
    public static final List<ProcessStatus> INDEXING_STATUS_LIST = List.of(PARSING, CLEANING, SPLITTING, INDEXING);
}