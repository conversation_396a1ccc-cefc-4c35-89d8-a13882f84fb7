package cn.bctools.ai.app.entity.vo.app_market;

import cn.bctools.ai.app.entity.data.AppMarket;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppMarkerListVO extends AppMarket {

    private String appMode;

    private String appName;

    private String appIcon;

    private String appDesc;

    private String appVersion;

    private String appConfigId;

    private String appWorkflowId;

    private String categoryInfos;

}
