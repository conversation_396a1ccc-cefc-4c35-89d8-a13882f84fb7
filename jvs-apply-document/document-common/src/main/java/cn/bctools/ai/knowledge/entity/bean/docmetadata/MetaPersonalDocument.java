package cn.bctools.ai.knowledge.entity.bean.docmetadata;

import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * 元数据类型-个人文档 Personal document
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(MetaPersonalDocument.TYPE)
@Accessors(chain = true)
@ApiModel("元数据-个人文档 Personal document")
public class MetaPersonalDocument extends MetaBase{
    public static final String TYPE = "personal_document";

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("作者")
    private String author;

    @ApiModelProperty("创建日期")
    private String creationDate;

    @ApiModelProperty("最后修改日期")
    private String lastModifiedDate;

    @ApiModelProperty("文档类型")
    private String documentType;

    @ApiModelProperty("标签/类别")
    private String tagsOrCategory;
}