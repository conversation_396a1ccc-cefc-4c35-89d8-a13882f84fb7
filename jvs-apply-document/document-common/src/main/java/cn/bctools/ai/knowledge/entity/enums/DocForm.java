package cn.bctools.ai.knowledge.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 索引内容形式
 */
@Getter
@AllArgsConstructor
public enum DocForm {
    //文本模式
    TEXT_MODEL("text_model"),
    //分层模式
    HIERARCHICAL_MODEL("hierarchical_model"),
    //问答对模式
    QA_MODEL("qa_model");

    @JsonValue
    @EnumValue
    private final String value;
}