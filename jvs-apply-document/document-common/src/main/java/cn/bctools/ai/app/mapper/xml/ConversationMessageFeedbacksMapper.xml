<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.app.mapper.ConversationMessageFeedbacksMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.app.entity.data.ConversationMessageFeedbacks">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="conversation_id" property="conversationId" />
        <result column="message_id" property="messageId" />
        <result column="rating" property="rating" />
        <result column="content" property="content" />
        <result column="from_source" property="fromSource" />
        <result column="from_end_user_id" property="fromEndUserId" />
        <result column="from_account_id" property="fromAccountId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, conversation_id, message_id, rating, content, from_source, from_end_user_id, from_account_id, create_time, update_time
    </sql>

</mapper>
