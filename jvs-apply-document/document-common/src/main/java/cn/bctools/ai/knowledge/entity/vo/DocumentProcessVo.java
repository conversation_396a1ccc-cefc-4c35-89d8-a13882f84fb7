package cn.bctools.ai.knowledge.entity.vo;

import cn.bctools.ai.knowledge.entity.bean.processrule.ProcessRule;
import cn.bctools.ai.knowledge.entity.bean.knowledge.RetrievalSetting;
import cn.bctools.ai.knowledge.entity.enums.DocForm;
import cn.bctools.oss.dto.BaseFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * 数据处理类
 */
@Data
@Accessors(chain = true)
public class DocumentProcessVo {
    @ApiModelProperty("文件信息")
    private List<BaseFile> files;

    @ApiModelProperty("id数组，更新索引")
    private List<String> itemIds;

    @ApiModelProperty("引用文库文档id数组")
    private List<String> dcIds;

    @NotNull(message = "处理规则不能为空")
    @ApiModelProperty("处理规则")
    private ProcessRule processRule;

    @ApiModelProperty("索引模式")
    private String indexingTechnique;

    @ApiModelProperty("索引内容的形式，text_model- text文档(默认) embedding经济模式默认为该模式；" +
            "hierarchical_model- parent-child 模式；qa_model- Q&A 模式：为分片文档生成 Q&A 对，然后对问题进行 embedding")
    private DocForm docForm;

    @ApiModelProperty("语言")
    private String docLanguage;

    @ApiModelProperty("embedding模型")
    private String embeddingModel;

    @ApiModelProperty("embedding模型供应商")
    private String embeddingModelProvider;

    @ApiModelProperty("检索配置")
    RetrievalSetting retrievalSetting;

    @ApiModelProperty("知识库id")
    private String knowId;

    @ApiModelProperty("检验重复")
    private Boolean duplicate = true;

}