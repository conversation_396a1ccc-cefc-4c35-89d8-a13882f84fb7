package cn.bctools.ai.knowledge.core.rag;

import cn.bctools.ai.common.config.async.AsyncConfig;
import cn.bctools.ai.common.util.ExceptionUtil;
import cn.bctools.ai.common.util.StrategyTypeUtil;
import cn.bctools.ai.common.util.TokenCalculateUtil;
import cn.bctools.ai.knowledge.core.documentparse.DocumentParseContext;
import cn.bctools.ai.knowledge.core.documentsplitter.DocumentItemSplitter;
import cn.bctools.ai.knowledge.core.eventlistener.event.ItemIndexingEvent;
import cn.bctools.ai.knowledge.core.eventlistener.event.ItemIndexingUpdateEvent;
import cn.bctools.ai.knowledge.core.pgvector.PgVectorFactory;
import cn.bctools.ai.knowledge.entity.bean.knowledge.EmbeddingModelSetting;
import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.ai.knowledge.entity.bean.processrule.ProcessRule;
import cn.bctools.ai.knowledge.entity.bean.processrule.Rules;
import cn.bctools.ai.knowledge.entity.constant.RKey;
import cn.bctools.ai.knowledge.entity.data.ItemSegment;
import cn.bctools.ai.knowledge.entity.data.Knowledge;
import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.enums.ParentMode;
import cn.bctools.ai.knowledge.entity.enums.ProcessStatus;
import cn.bctools.ai.knowledge.service.ItemSegmentsService;
import cn.bctools.ai.knowledge.service.KnowledgeEmbeddingService;
import cn.bctools.ai.knowledge.service.KnowledgeItemService;
import cn.bctools.ai.knowledge.util.KeywordExtractor;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.database.util.IdGenerator;
import cn.bctools.redis.utils.RedisUtils;
import dev.langchain4j.community.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class EmbeddingRAG {

    @Value("${oss.name}")
    private String ossName;

    @Resource(name = AsyncConfig.COMMON_EXECUTOR_BEAN)
    private Executor taskExecutor;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    @Lazy
    private KnowledgeItemService itemService;

    @Resource
    @Lazy
    private ItemSegmentsService segmentsService;

    @Resource
    @Lazy
    private PgVectorFactory pgVectorFactory;

    @Resource
    private KnowledgeEmbeddingService embeddingService;

    @Resource
    private ApplicationContext applicationContext;


    /**
     * 新增索引处理
     *
     * @param knowledge 知识库信息
     * @param kiList    知识库条目列表
     */
    //拆解方法，每个处理单独抽取
    @SuppressWarnings("uncheck")
    public void asyncDocumentIndexing1(Knowledge knowledge, List<KnowledgeItem> kiList) {
        CompletableFuture.runAsync(() -> {
            for (KnowledgeItem ki : kiList) {
                boolean lock = redisUtils.tryLock(RKey.ITEM_INDEXING + ki.getId(), 10 * 60);
                if (lock) {
                    continue;
                }

//                //高质量需要检查是否有embedding模型
//                if(IndexingTechnique.HIGH_QUALITY.equals(knowledge.getIndexingTechnique())){
//
//                }

                createSegment(knowledge, ki);

            }
        }, taskExecutor);
    }

    private void createSegment(Knowledge knowledge, KnowledgeItem ki) {
        try {
            //检验是否有处理规则
            ParentMode parentMode = Optional.ofNullable(ki.getProcessRule()).map(ProcessRule::getRules)
                    .map(Rules::getParentMode)
                    .orElse(null);
            if (Objects.isNull(parentMode)) {
                throw new BusinessException("条目未设置分段处理规则,处理失败");
            }

            //1.开始处理文档
            ki.setProcessingStartedTime(LocalDateTime.now());
            //2.解析文档
            KDocument document = DocumentParseContext.get(ossName).parse(ki.extractFileInfo());
            document.setKnowId(knowledge.getId());
            document.setItemId(ki.getId());
            if (Objects.isNull(document.text())) {
                return;
            }
            //postgresql 不支持 \u0000
            String content = Optional.of(document).map(KDocument::text).orElse("")
                    .replaceAll("\u0000", "");
            ki.setParsingCompletedTime(LocalDateTime.now());

            //3.开始清洗文档
            content = ki.getProcessRule().process(content);
            document.setText(content);
            ki.setCleaningCompletedTime(LocalDateTime.now());

            //4.开始文档分段
            DocumentItemSplitter splitter = StrategyTypeUtil
                    .getDocumentSplitter(ki.getProcessRule().parentMode().getValue());
//                    // 非langchain4j实现文本分段
//                    SegmentationStrategy splitter = SegmentationStrategyFactory.getStrategy(parentMode);

            //父子模式切割还需处理子块 >> 保存到 child_chunks表 同时向量化
            //段落模式
            //父段和子段都要向量化 embeddings
            //父段->item_segments
            //子段->child_chunks
            //
            //全文模式
            //父段不向量化    子段向量化 embeddings
            //父段->item_segments
            //子段->child_chunks

            List<KDocument> segments = splitter.split(document, ki.getProcessRule().getRules());
            ki.setSplittingCompletedTime(LocalDateTime.now());

            //5.分块向量化处理 分段保存内容到 ai_item_segments表
            //构造embeddingModel
            EmbeddingModelSetting embeddingModel = new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(),
                    knowledge.getEmbeddingModel());

            //初始化数据库元数据处理
//                    DefaultMetadataStorageConfig metaDataConfig = DefaultMetadataStorageConfig.builder()
//                            .storageMode(MetadataStorageMode.COLUMN_PER_KEY)
//                            .columnDefinitions(new ArrayList<>(Arrays.asList("hash", "create_time", "provider_name", "model_name", "other_info")))
//                            .indexes(new ArrayList<>(Arrays.asList("hash", "provider_name", "model_name")))
//                            .indexType("BTREE")
//                            .build();

            //初始化存储管道
            CustomEmbeddingStoreIngestor ingestor = CustomEmbeddingStoreIngestor.builder()
                    .embeddingModel(new EmbeddingModelSetting(knowledge.getEmbeddingModelProvider(),
                            knowledge.getEmbeddingModel()))
//                            .embeddingStore(EmbeddingStoreUtil.kbEmbeddingStore(knowledge.getEmbeddingModelProvider(),
//                                    knowledge.getEmbeddingModel(), embeddingModel.dimension(), EmbeddingStoreType.TEXT,
//                                    metaDataConfig))
                    .embeddingService(embeddingService)
                    .pgVector(pgVectorFactory.initVector(knowledge))
                    .build();

            //新增分片数据 segments
            AtomicInteger segmentIndex = new AtomicInteger(1);
            List<ItemSegment> itemSegmentList = new ArrayList<>();
            List<String> segmentIds = new ArrayList<>();
            segments.forEach(e -> {
                ItemSegment itemSegments = new ItemSegment();
                String idStr = IdGenerator.getIdStr();
                segmentIds.add(idStr);
                segmentIds.add(idStr);
                itemSegments.setId(idStr);
                itemSegments.setIndexNodeId(IdGenerator.getIdStr());
                itemSegments.setKnowId(knowledge.getId());
                itemSegments.setKiId(ki.getId());
                itemSegments.setContent(e.text());
                itemSegments.setPosition(segmentIndex.getAndIncrement());
                itemSegments.setWordCount(e.text().length());
                itemSegments.setTokens(TokenCalculateUtil.getTokenNumOfContent(e.text()));
                itemSegments.setStatus(ProcessStatus.DOING);
                //父子模式不需要提取关键词
                itemSegments.setKeywords(KeywordExtractor.extractKeywords(e.text()));
                itemSegments.setIndexNodeId(e.getDocId());
                itemSegments.setIndexNodeHash(e.getHash());
                itemSegmentList.add(itemSegments);
            });
            segmentsService.saveBatch(itemSegmentList);

            //向量化
            List<float[]> embedResponse = ingestor.embedAll(segments);
            //更新处理时间
            segmentsService.lambdaUpdate().in(ItemSegment::getId, segmentIds)
                    .set(ItemSegment::getIndexingTime, LocalDateTime.now()).update();

            //存储到postgresql
            ingestor.storeAll(embedResponse, segments);
            //更新完成时间时间
            segmentsService.lambdaUpdate().in(ItemSegment::getId, itemSegmentList)
                    .set(ItemSegment::getCompletedTime, LocalDateTime.now())
                    .set(ItemSegment::getStatus, ProcessStatus.COMPLETED).update();

            //6.完成
            //设置位置
            ki.setWordCount(content.length());
            ki.setTokens(TokenCalculateUtil.getTokenNumOfContent(content));
            ki.setDataSourceType("upload_file");
            ki.setIndexingStatus(ProcessStatus.COMPLETED);
            ki.setCompletedTime(LocalDateTime.now());
            itemService.updateById(ki);
        } catch (Exception e) {
            log.error("异步处理文档失败,文档id:{} . ", ki.getId(), e);
            ki.setIndexingStatus(ProcessStatus.ERROR);
            ki.setError(ExceptionUtil.gatherInfo(e));
            itemService.updateById(ki);
        } finally {
            redisUtils.del(RKey.ITEM_INDEXING + ki.getId());
        }
    }


    /**
     * 新增索引处理
     *
     * @param knowledge 知识库信息
     * @param kiList    知识库条目列表
     */
    @SuppressWarnings("uncheck")
    public void asyncDocumentIndexing(Knowledge knowledge, List<KnowledgeItem> kiList) {
        applicationContext.publishEvent(new ItemIndexingEvent(this, knowledge, kiList));
    }


    /**
     * 更新索引处理
     *
     * @param knowledge 知识库信息
     * @param kiList    知识库条目列表
     */
    @SuppressWarnings("uncheck")
    public void asyncUpdateDocumentIndexing(Knowledge knowledge, List<KnowledgeItem> kiList) {
        applicationContext.publishEvent(new ItemIndexingUpdateEvent(this, knowledge, kiList));
    }

}