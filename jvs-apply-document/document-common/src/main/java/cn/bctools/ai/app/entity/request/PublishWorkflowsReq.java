package cn.bctools.ai.app.entity.request;

import cn.bctools.ai.app.entity.dto.WorkflowFeaturesDTO;
import cn.bctools.rule.utils.html.HtmlGraph;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Data
public class PublishWorkflowsReq {
    @ApiModelProperty("名称")
    String name;
    @ApiModelProperty("描述")
    String description;
}
