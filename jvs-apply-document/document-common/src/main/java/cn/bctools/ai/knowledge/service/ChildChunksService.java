package cn.bctools.ai.knowledge.service;

import cn.bctools.ai.knowledge.entity.bean.processrule.ProcessRule;
import cn.bctools.ai.knowledge.entity.data.ChildChunk;
import cn.bctools.ai.knowledge.entity.data.ItemSegment;
import cn.bctools.ai.knowledge.entity.data.Knowledge;
import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.vo.UpdateChildChunkReqVo;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
public interface ChildChunksService extends IService<ChildChunk> {

    //获取当前最大位置
    int maxPosition(String segmentId);

    //获取文本块子块数组
    List<ChildChunk> getSegmentChildChunks(String segmentId, ProcessRule processRule);

    //批量获取文本块子块数组
    Map<String, List<ChildChunk>> getSegmentsChildChunks(List<String> itemIds, ProcessRule processRule);

    //创建文本子块
    ChildChunk createChildChunk(String segmentId, String content);

    //获取分页列表
    void getPage(Page<ChildChunk> page, String segmentId, String keyword);

    //删除子块
    void delete(String chunkId);

    //根据id获取子块，没有则抛出异常
    @SuppressWarnings("unchecked")
    ChildChunk getExistById(String id, SFunction<ChildChunk, ?>... columns);

    //更新子块
    ChildChunk updateChildChunk(Knowledge knowledge, ChildChunk chunk, String content);

    //批量更新子块
    List<ChildChunk> updateChildChunks(Knowledge knowledge, KnowledgeItem item, ItemSegment segment,
                                       List<UpdateChildChunkReqVo> vos);
}