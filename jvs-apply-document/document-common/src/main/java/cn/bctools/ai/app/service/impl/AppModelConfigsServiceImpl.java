package cn.bctools.ai.app.service.impl;

import cn.bctools.ai.app.entity.dto.*;
import cn.bctools.ai.app.mapper.AppModelConfigsMapper;
import cn.bctools.ai.app.service.AppModelConfigsService;
import cn.bctools.ai.app.entity.data.AppModelConfigs;
import cn.bctools.common.utils.BeanCopyUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * <p>
 * 应用配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppModelConfigsServiceImpl extends ServiceImpl<AppModelConfigsMapper, AppModelConfigs> implements AppModelConfigsService {

    @Override
    public AppModelConfigs addDefaultConfig(String appId) {

        ModelConfigDTO configDTO = new ModelConfigDTO();

        AgentModeBean agentModeBean = new AgentModeBean();
        agentModeBean.setEnabled(false);
        agentModeBean.setTools(new ArrayList<>());
        configDTO.setAgent_mode(agentModeBean);

        AnnotationReplyBean annotationReplyBean = new AnnotationReplyBean();
        annotationReplyBean.setEnabled(false);
        configDTO.setAnnotation_reply(annotationReplyBean);

        DatasetConfigsBean datasetConfigsBean = new DatasetConfigsBean();
        datasetConfigsBean.setRetrieval_model("retrieval_model");
        configDTO.setDataset_configs(datasetConfigsBean);

        FileUploadBean fileUploadBean = new FileUploadBean();
        ImageBean imageBean = new ImageBean();
        imageBean.setEnabled(false);
        imageBean.setDetail("high");
        imageBean.setNumber_limits(3);
        imageBean.setTransfer_methods(Arrays.asList("remote_url", "local_file"));
        fileUploadBean.setImage(imageBean);
        configDTO.setFile_upload(fileUploadBean);

        ModelBean modelBean = new ModelBean();
        modelBean.setMode("chat");
        modelBean.setName("qwen-turbo");
        modelBean.setProvider("tongyi");
        configDTO.setModel(modelBean);


        MoreLikeThisBean moreLikeThisBean = new MoreLikeThisBean();
        moreLikeThisBean.setEnabled(false);
        configDTO.setMore_like_this(moreLikeThisBean);

        RetrieverResourceBean retrieverResourceBean = new RetrieverResourceBean();
        retrieverResourceBean.setEnabled(false);
        configDTO.setRetriever_resource(retrieverResourceBean);

        SensitiveWordAvoidanceBean sensitiveWordAvoidanceBean = new SensitiveWordAvoidanceBean();
        sensitiveWordAvoidanceBean.setEnabled(false);
        configDTO.setSensitive_word_avoidance(sensitiveWordAvoidanceBean);

        SpeechToTextBean speechToTextBean = new SpeechToTextBean();
        speechToTextBean.setEnabled(false);
        configDTO.setSpeech_to_text(speechToTextBean);

        TextToSpeechBean textToSpeechBean = new TextToSpeechBean();
        textToSpeechBean.setEnabled(false);
        configDTO.setText_to_speech(textToSpeechBean);

        SuggestedQuestionsAfterAnswerBean suggestedQuestionsAfterAnswerBean = new SuggestedQuestionsAfterAnswerBean();
        suggestedQuestionsAfterAnswerBean.setEnabled(false);
        configDTO.setSuggested_questions_after_answer(suggestedQuestionsAfterAnswerBean);

        AppModelConfigs appModelConfigs = BeanCopyUtil.copy(configDTO, AppModelConfigs.class);
        appModelConfigs.setAppId(appId);
        appModelConfigs.setVersion("1.0.0");
        save(appModelConfigs);

        return appModelConfigs;
    }


    @Override
    public AppModelConfigs copyConfig(String configId) {

        AppModelConfigs appModelConfigs = getById(configId);
        if(appModelConfigs == null) {
            return null;
        }
        appModelConfigs.setId(null);
        appModelConfigs.setCreateTime(null);
        appModelConfigs.setUpdateTime(null);
        save(appModelConfigs);

        return appModelConfigs;
    }

}
