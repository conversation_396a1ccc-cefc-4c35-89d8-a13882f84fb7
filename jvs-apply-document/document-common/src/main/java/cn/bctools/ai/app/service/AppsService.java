package cn.bctools.ai.app.service;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.data.AppModelConfigs;
import cn.bctools.ai.app.entity.data.Apps;
import cn.bctools.ai.app.entity.data.Sites;
import cn.bctools.ai.app.entity.dto.ModelConfigDTO;
import cn.bctools.ai.app.entity.dto.SiteDTO;
import cn.bctools.ai.app.entity.vo.*;
import cn.bctools.model.entity.vo.VoiceVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 应用 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface AppsService extends IService<Apps> {

    IPage<AppListDetailVO> getAppList(Page<AppListDetailVO> page, AppListVO params);

    AppDetail getAppInfo(String appId);

    AppDetail getAppInfoByConfigId(String appId, String configId);

    Apps create(AppInfoVO app);

    Apps edit(String appId, AppInfoVO app);

    Apps copy(String appId, CopyAppVO newApp);

    boolean delete(String appId);

    AppModelConfigs addNewConfigVersion(String appId, ModelConfigDTO params);

    Sites updateSite(String appId, SiteDTO params);

    Sites resetAccessToken(String appId);

    IPage<VersionInfo> getVersions(Page<AppModelConfigs> page, String appId);

    ModelConfigDTO getVersion(String appId, String version);

    void recoverConfig(String appId, String version);

    boolean updateSiteEnable(String appId, Boolean enable);

    boolean updateApiEnable(String appId, Boolean enable);

    List<VoiceVO> getVoices(String language);

}
