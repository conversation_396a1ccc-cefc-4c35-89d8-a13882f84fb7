package cn.bctools.ai.knowledge.core.knowupload;

import cn.bctools.ai.knowledge.entity.constant.DocumentCreateFrom;
import cn.bctools.ai.knowledge.entity.data.KnowledgeItem;
import cn.bctools.ai.knowledge.entity.vo.DocumentProcessVo;
import cn.bctools.ai.knowledge.entity.vo.KnowFileUploadVo;
import cn.bctools.ai.knowledge.entity.vo.KnowUploadVo;
import cn.bctools.ai.knowledge.service.KnowledgeItemService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component(IKnowUpload.BEAN_NAME_PREFIX + FileKnowUpload.ID)
@RequiredArgsConstructor
public class FileKnowUpload implements IKnowUpload {

    public final static String ID = "file";

    private final KnowledgeItemService knowledgeItemService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<KnowledgeItem> upload(KnowUploadVo vo) {
        KnowFileUploadVo fileUploadVo = (KnowFileUploadVo) vo;
        //转换为DocumentProcessVo
        DocumentProcessVo documentProcessVo = JSON.parseObject(JSON.toJSONString(fileUploadVo), DocumentProcessVo.class);
        //设置files
        documentProcessVo.setFiles(fileUploadVo.getFiles());
        return knowledgeItemService.saveItem(documentProcessVo, DocumentCreateFrom.WEB);
    }
}