package cn.bctools.ai.app.entity.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.codehaus.jackson.map.annotate.JsonSerialize;

/**
 * <p>
 * 应用
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_apps")
@ApiModel(value = "Apps对象", description = "应用")
public class Apps implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "应用名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "应用模式")
    @TableField("mode")
    private String mode;

    @ApiModelProperty(value = "图标")
    @TableField("icon")
    private String icon;

    @ApiModelProperty(value = "应用描述")
    @TableField("description")
    @JsonInclude // 强制输出该字段，不管是否为 null
    private String description;

    @ApiModelProperty(value = "工作流id")
    @TableField("workflow_id")
    private String workflowId;

    @ApiModelProperty(value = "应用配置id")
    @TableField("app_model_config_id")
    private String appModelConfigId;

    @ApiModelProperty(value = "发布配置id")
    @TableField("publish_config_id")
    private String publishConfigId;

    @ApiModelProperty(value = "应用状态")
    @TableField("status")
    private String status;

    @ApiModelProperty(value = "是否允许网站运行")
    @TableField("enable_site")
    @SerializedName("enable_site")
    private Boolean enableSite;


    @ApiModelProperty(value = "是否允许api调用")
    @TableField("enable_api")
    @SerializedName("enable_api")
    private Boolean enableApi;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @ApiModelProperty(value = "创建人名称")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人名称")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "逻辑删除")
    @TableField("del_flag")
    private Boolean delFlag;

    @ApiModelProperty(value = "最大请求数")
    @TableField("max_active_requests")
    private Integer maxActiveRequests;

    @ApiModelProperty(value = "当前上架版本")
    @TableField("public_id")
    private String publicId;

    @ApiModelProperty(value = "审核版本")
    @TableField("public_id2")
    private String publicId2;

}