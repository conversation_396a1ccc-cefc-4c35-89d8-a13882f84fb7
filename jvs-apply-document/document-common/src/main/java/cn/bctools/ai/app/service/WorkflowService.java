package cn.bctools.ai.app.service;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.dto.WorkflowFeaturesDTO;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.rule.utils.html.HtmlGraph;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
public interface WorkflowService {
    RuleDesignPo getDraftWorkflow(AppDetail appDetail);

    RuleDesignPo saveDraftWorkflow(AppDetail appDetail, String name, String description, HtmlGraph designDrawingJson, WorkflowFeaturesDTO features);

    RuleDesignPo getPublishedWorkflow(AppDetail appDetail);

    RuleDesignPo publishWorkflow(AppDetail appDetail, String name, String description);

    RuleDesignPo getWorkflowById(AppDetail appDetail, String id);
}
