package cn.bctools.ai.knowledge.entity.vo;


import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 引用文库文档导入vo
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(KnowQuoteUploadVo.TYPE)
@Accessors(chain = true)
public class KnowQuoteUploadVo extends KnowUploadVo{
    public static final String TYPE = "quote";

    @ApiModelProperty("文档id-处理规则关联数组")
    @NotEmpty(message = "文档id不能为空")
    private List<String> dcIds;
}