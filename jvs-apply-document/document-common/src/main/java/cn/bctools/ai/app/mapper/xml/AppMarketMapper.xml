<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.ai.app.mapper.AppMarketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.ai.app.entity.data.AppMarket">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="public_id" property="publicId" />
        <result column="status" property="status" />
        <result column="allow_guest" property="allowGuest" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, public_id, status, allow_guest, create_time, update_time, create_by, update_by, del_flag
    </sql>

    <select id="getAppInfo" resultType="cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO">
        select
        aam.*,
        aap.app_mode, aap.app_name, aap.app_icon, aap.app_desc, aap.app_version, aap.app_config_id, aap.app_workflow_id,
        GROUP_CONCAT(aac.name) AS category_infos
        from ai_app_market aam
        join ai_app_public aap on aap.id = aam.public_id
        left join ai_app_public_category aapc on aapc.public_id = aap.id and aapc.del_flag = 0
        left join ai_app_category aac on aac.id = aapc.category_id
        <where>
            aam.del_flag = 0
            and aam.id = #{id}
        </where>
        group by aam.id
        order by aam.create_time desc
    </select>

    <select id="getList" resultType="cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO">
        select
            aam.*,
            aap.app_mode, aap.app_name, aap.app_icon, aap.app_desc, aap.app_version, aap.app_config_id, aap.app_workflow_id,
            GROUP_CONCAT(aac.name) AS category_infos
        from ai_app_market aam
        join ai_app_public aap on aap.id = aam.public_id
        left join ai_app_public_category aapc on aapc.public_id = aap.id and aapc.del_flag = 0
        left join ai_app_category aac on aac.id = aapc.category_id
        <where>
            aam.del_flag = 0
            <if test="params.mode != null and params.mode != ''">
                and aap.app_mode = #{params.mode}
            </if>
            <if test="params.status != null">
                and aam.status = #{params.status}
            </if>
            <if test="params.allowGuest != null">
                and aam.allow_guest = #{params.allowGuest}
            </if>
            <if test="params.keyword != null and params.keyword != ''">
                and aap.app_name like concat('%',#{params.keyword},'%')
            </if>
            <if test="params.categoryIds != null and params.categoryIds.size() > 0">
                and aam.public_id in (
                select public_id
                from ai_app_public_category
                where category_id in
                <foreach collection="params.categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
                )
            </if>
        </where>
        group by aam.id
        order by aam.create_time desc
    </select>



</mapper>
