package cn.bctools.ai.knowledge.core.retrieval;

import brave.internal.Nullable;
import cn.bctools.ai.app.entity.dto.ModelBean;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.common.config.async.AsyncConfig;
import cn.bctools.ai.common.util.DefaultVUtil;
import cn.bctools.ai.knowledge.core.rerank.DataPostProcessor;
import cn.bctools.ai.knowledge.core.rerank.WeightRerankRunner;
import cn.bctools.ai.knowledge.entity.bean.docmetadata.MetadataCondition;
import cn.bctools.ai.knowledge.entity.bean.docmetadata.MetadataFilteringCondition;
import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.ai.knowledge.entity.bean.document.RetrievalSegments;
import cn.bctools.ai.knowledge.entity.bean.document.ScoreDocument;
import cn.bctools.ai.knowledge.entity.bean.knowledge.*;
import cn.bctools.ai.knowledge.entity.bean.retrieval.PromptMessageEntity;
import cn.bctools.ai.knowledge.entity.constant.IndexingTechnique;
import cn.bctools.ai.knowledge.entity.data.*;
import cn.bctools.ai.knowledge.entity.enums.*;
import cn.bctools.ai.knowledge.service.*;
import cn.bctools.ai.tag.enums.TagType;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.model.ModelInstance;
import cn.bctools.model.model_runtime.entities.ModelConfig;
import cn.bctools.model.model_runtime.entities.constant.ModelFeature;
import cn.bctools.model.model_runtime.model_providers.__base.AIModel;
import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.Null;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 知识库检索处理服务类
 */
@Service
public class KnowRetrievalService {

    @Resource
    KnowledgeService knowledgeService;

    @Resource
    KnowledgeItemService itemService;

    @Resource
    RetrievalHandler retrievalHandler;

    @Resource(name = AsyncConfig.COMMON_EXECUTOR_BEAN)
    Executor taskExecutor;

    @Resource
    DataPostProcessor dataPostProcessor;

    @Resource
    KnowledgeQueriesService queriesService;

    @Resource
    ChildChunksService chunksService;

    @Resource
    ItemSegmentsService segmentsService;

    //todo handler属性后续改为资源检索回调类入参，用于处理存储检索资源记录
    public String retrieve(String appId, String query, InvokeFrom invokeFrom, Boolean showRetrieveSource,
                           LLMModelSetting modelSetting, KnowRetrieveSetting knowRetrieveSetting,
                           String messageId, @Null Boolean handler, @Nullable Map<String, String> inputs
    ) {
        if (ObjectNull.isNull(knowRetrieveSetting.getKnowIds())) {
            return "";
        }

        ModelInstance llmModel = modelSetting.getLlmModel();
        if (Objects.isNull(llmModel)) {
            return "";
        }
        AIModel aiModel = llmModel.getModel();
        ModelConfig modelConfig = aiModel.getModelConfig(llmModel.getModelName(), llmModel.getEncryptedConfig());
        if (Objects.isNull(modelConfig)) {
            return "";
        }

        PlanningStrategy planningStrategy = PlanningStrategy.REACT_ROUTER;
        List<String> features = modelConfig.getFeatures();
        if (ObjectNull.isNotNull(features)) {
            if (features.contains(ModelFeature.TOOL_CALL)
                    || features.contains(ModelFeature.MULTI_TOOL_CALL)) {
                planningStrategy = PlanningStrategy.ROUTER;
            }
        }
        List<Knowledge> availableKnows = new ArrayList<>();
        List<String> availableKnowIds = new ArrayList<>();
        Map<String, Integer> availableDocumentCounts =
                itemService.getAvailableDocumentCounts(knowRetrieveSetting.getKnowIds());
        Map<String, Knowledge> knowMap = knowledgeService.listByIds(knowRetrieveSetting.getKnowIds())
                .stream().collect(Collectors.toMap(Knowledge::getId, Function.identity()));
        for (String knowId : knowRetrieveSetting.getKnowIds()) {
            Knowledge knowledge = knowMap.get(knowId);
            if (Objects.isNull(knowledge)) {
                continue;
            }

            Integer availableDocCount = availableDocumentCounts.getOrDefault(knowId, 0);
            if (availableDocCount == 0 && !Objects.equals(knowledge.getProvider(), "external")) {
                continue;
            }
            availableKnowIds.add(knowId);
            availableKnows.add(knowledge);
        }

        KnowRetrieveConfig retrieveConfig = knowRetrieveSetting.getKnowRetrieveConfig();

        Pair<Map<String, List<String>>, MetadataCondition> metadataFilterResult =
                this.getMetadataFilterCondition(availableKnowIds, query, retrieveConfig.getMetadataFilteringMode(),
                        retrieveConfig.getMetadataModelConfig(), retrieveConfig.getMetadataFilteringCondition());


        List<KDocument> allDocs = new ArrayList<>();
        String userFrom;
        if (List.of(InvokeFrom.EXPLORE, InvokeFrom.DEBUGGER).contains(invokeFrom)) {
            userFrom = "account";
        } else {
            userFrom = "end_user";
        }

        if (Objects.equals(RetrieveStrategy.SINGLE,
                retrieveConfig.getRetrieveStrategy())) {
            allDocs = this.singleRetrieve(appId, userFrom, query, llmModel, modelSetting, planningStrategy,
                    messageId, availableKnows, metadataFilterResult.getLeft(), metadataFilterResult.getRight());
        } else if (Objects.equals(RetrieveStrategy.MULTIPLE,
                retrieveConfig.getRetrieveStrategy())) {
            allDocs = this.multipleRetrieve(appId, userFrom, query,
                    DefaultVUtil.get(retrieveConfig.getTopK(), 0),
                    DefaultVUtil.get(retrieveConfig.getScoreThreshold(), 0.0f),
                    DefaultVUtil.get(retrieveConfig.getRerankingMode(), RerankingMode.RERANKING_MODEL),
                    retrieveConfig.getRerankingModel(), retrieveConfig.getWeights(),
                    DefaultVUtil.get(retrieveConfig.getRerankingEnable(), true),
                    availableKnows, messageId, metadataFilterResult.getLeft(), metadataFilterResult.getRight());
        }

        List<KDocument> edfDocuments = allDocs.stream()
                .filter(e -> Objects.equals("edf", e.getProvider())).toList();

        List<KDocument> externalDocuments = allDocs.stream()
                .filter(e -> Objects.equals("external", e.getProvider())).toList();
        List<ScoreDocument> documentScoreList = new ArrayList<>();
        List<KnowledgeRetrieverResource> retrievalResourceList = new ArrayList<>();
        String source = invokeFrom.toSource();
        //处理外部文档
        for (KDocument externalDocument : externalDocuments) {
            documentScoreList.add(new ScoreDocument(externalDocument.getText(), externalDocument.getScore()));
            if (Objects.nonNull(handler)) {
                KnowledgeRetrieverResource resource = new KnowledgeRetrieverResource();
                resource.setKnowId(externalDocument.getKnowId());
                resource.setKnowName(externalDocument.getKnowName());
                resource.setKiId(externalDocument.getItemId());
                resource.setKiName(externalDocument.getItemName());
                resource.setDataSourceType("external");
                resource.setScore((double) DefaultVUtil.get(externalDocument.getScore(), 0.0f));
                resource.setContent(externalDocument.text());
                resource.setRetrieverFrom(source);
                retrievalResourceList.add(resource);
            }
        }

        //处理内部文档
        if (ObjectNull.isNotNull(edfDocuments)) {
            List<RetrievalSegments> records = retrievalHandler.formatRetrievalDocuments(edfDocuments);
            if (ObjectNull.isNotNull(records)) {
                for (RetrievalSegments record : records) {
                    ItemSegment segment = record.getSegment();
                    String content = segment.getContent();
                    //处理问答对
                    if (StrUtil.isNotEmpty(segment.getAnswer())) {
                        content = "question:" + segment.getContent() +
                                " answer:" + segment.getAnswer();
                    }
                    documentScoreList.add(new ScoreDocument(content, record.getScore()));
                }
                //设置了获取源头
                if (Boolean.TRUE.equals(showRetrieveSource)) {
                    List<String> knowIds = new ArrayList<>();
                    List<String> itemIds = new ArrayList<>();
                    records.stream().map(RetrievalSegments::getSegment)
                            .forEach(e -> {
                                knowIds.add(e.getKnowId());
                                itemIds.add(e.getKiId());
                            });
                    Map<String, Knowledge> knowMapCopy = new HashMap<>();
                    if (ObjectNull.isNotNull(knowIds)) {
                        knowMapCopy = knowledgeService.listByIds(knowIds)
                                .stream().collect(Collectors.toMap(Knowledge::getId, Function.identity()));
                    }
                    Map<String, KnowledgeItem> itemMap = new HashMap<>();
                    if (ObjectNull.isNotNull(itemIds)) {
                        itemMap = itemService.lambdaQuery().in(KnowledgeItem::getId, itemIds)
                                .eq(KnowledgeItem::getEnabled, true)
                                .eq(KnowledgeItem::getIsArchived, false).list()
                                .stream().collect(Collectors.toMap(KnowledgeItem::getId, Function.identity()));
                    }
                    if (Objects.nonNull(handler)) {
                        for (RetrievalSegments record : records) {
                            ItemSegment segment = record.getSegment();
                            Knowledge know = knowMapCopy.get(segment.getKnowId());
                            KnowledgeItem item = itemMap.get(segment.getKiId());
                            if (ObjectNull.isNotNull(know, item)) {
                                KnowledgeRetrieverResource resource = new KnowledgeRetrieverResource();
                                resource.setKnowId(know.getId());
                                resource.setKnowName(know.getName());
                                resource.setKiId(item.getId());
                                resource.setKiName(item.getName());
                                resource.setSegmentId(segment.getId());
                                resource.setDataSourceType(item.getDataSourceType());
                                resource.setScore((double) DefaultVUtil.get(record.getScore(), 0.0f));
                                resource.setRetrieverFrom(source);

                                //调试模式处理
                                if (Objects.equals(source, "dev")) {
                                    resource.setHitCount(segment.getHitCount());
                                    resource.setWordCount(segment.getWordCount());
                                    resource.setSegmentPosition(segment.getPosition());
                                    resource.setIndexNodeHash(segment.getIndexNodeHash());
                                }

                                //问答对处理
                                if (StrUtil.isNotEmpty(segment.getContent())) {
                                    resource.setContent("question:" + segment.getContent() +
                                            " answer:" + segment.getAnswer());
                                } else {
                                    resource.setContent(segment.getContent());
                                }

                                retrievalResourceList.add(resource);
                            }
                        }
                    }
                }
            }
        }

        //todo 添加资源检索回调类入参，用于处理存储检索资源记录
        if (Objects.nonNull(handler)) {

        }

        if (ObjectNull.isNotNull(documentScoreList)) {
            documentScoreList.sort(Comparator.comparing(ScoreDocument::getScore).reversed());
            return documentScoreList.stream().map(ScoreDocument::getContent)
                    .filter(StrUtil::isNotBlank).collect(Collectors.joining("\n"));
        }

        return "";
    }

    //todo 获取元数据过滤结果
    private Pair<Map<String, List<String>>, MetadataCondition>
    getMetadataFilterCondition(List<String> availableKnowIds, String query, MetadataFilteringMode metadataFilteringMode,
                               ModelBean metadataModelConfig, MetadataFilteringCondition metadataFilteringCondition) {


        return Pair.of(null, null);
    }


    //todo 多个知识库选择策略
    private List<KDocument> singleRetrieve(String appId, String userFrom, String query, ModelInstance llmModel,
                                           LLMModelSetting llmModelSetting, PlanningStrategy planningStrategy,
                                           String messageId, List<Knowledge> availableKnows,
                                           Map<String, List<String>> metadataFilterDocumentIdsMap,
                                           MetadataCondition metadataCondition) {
        List<PromptMessageEntity> entities = new ArrayList<>();
        for (Knowledge know : availableKnows) {
            if (StrUtil.isEmpty(know.getDescription())) {
                know.setDescription("当您想要回答有关" + know.getName() + "的查询时很有用");
            }
            know.setDescription(know.getDescription().replace("\n", "")
                    .replace("\r", ""));

            PromptMessageEntity promptMessageEntity = new PromptMessageEntity(know.getId(), know.getDescription()
                    , Map.of("type", "object",
                    "properties", new HashMap<>(),
                    "required", new ArrayList<>()));
            entities.add(promptMessageEntity);
        }

        String knowId = null;
        //todo 若为单个检索，传入多个知识库需要根据某些策略选中其中一个
        if (Objects.equals(PlanningStrategy.REACT_ROUTER, planningStrategy)) {

        }
        return List.of();
    }

    @SneakyThrows
    private List<KDocument> multipleRetrieve(String appId, String userFrom, String query, Integer topK,
                                             Float scoreThreshold, RerankingMode rerankingMode,
                                             RerankingModel rerankingModel, Weights weights, Boolean rerankingEnable,
                                             List<Knowledge> availableKnows, String messageId,
                                             Map<String, List<String>> metadataFilterDocumentIdsMap,
                                             MetadataCondition metadataCondition) {
        if (ObjectNull.isNull(availableKnows)) {
            return List.of();
        }

        List<KDocument> allDocuments;
        String indexingTechnique = availableKnows.get(0).getIndexingTechnique();
        boolean indexTypeCheck = availableKnows.stream()
                .allMatch(e -> Objects.equals(e.getIndexingTechnique(), indexingTechnique));
        if (!indexTypeCheck && (!rerankingEnable || !Objects.equals(rerankingMode, RerankingMode.RERANKING_MODEL))) {
            throw new BusinessException("配置的知识库检索包含不同的索引方式，需要设置重排序模型");
        }
        if (IndexingTechnique.isHighQuality(indexingTechnique)) {
            String embeddingModelName = availableKnows.get(0).getEmbeddingModel();
            boolean modelCheck = availableKnows.stream()
                    .allMatch(e -> Objects.equals(e.getEmbeddingModel(), embeddingModelName));

            String embeddingModelProvider = availableKnows.get(0).getEmbeddingModelProvider();
            boolean providerCheck = availableKnows.stream()
                    .allMatch(e -> Objects.equals(e.getEmbeddingModelProvider(), embeddingModelProvider));

            if (Boolean.TRUE.equals(rerankingEnable) && Objects.equals(RerankingMode.WEIGHTED_SCORE, rerankingMode) &&
                    (!modelCheck || !providerCheck)) {
                throw new BusinessException("配置的知识库检索具有不同的索引技术，需要设置重排序模型");
            }
            if (Boolean.TRUE.equals(rerankingEnable) && Objects.equals(RerankingMode.WEIGHTED_SCORE, rerankingMode)) {
                if (Objects.nonNull(weights)) {
                    weights.getVectorSetting().setEmbeddingProviderName(embeddingModelProvider);
                    weights.getVectorSetting().setEmbeddingModelName(embeddingModelName);
                }
            }
        }

        //异步处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        List<KDocument> finalAllDocuments = new ArrayList<>();
        for (Knowledge know : availableKnows) {
            List<String> documentIdsFilter = new ArrayList<>();
            if (!Objects.equals(know.getProvider(), "external")) {
                if (Objects.nonNull(metadataCondition) && ObjectNull.isNull(metadataFilterDocumentIdsMap)) {
                    continue;
                }
                if (ObjectNull.isNotNull(metadataFilterDocumentIdsMap)) {
                    documentIdsFilter = metadataFilterDocumentIdsMap.getOrDefault(know.getId(), List.of());
                    if (ObjectNull.isNull(documentIdsFilter)) {
                        continue;
                    }
                }
            }

            List<String> finalDocumentIdsFilter = documentIdsFilter;
            futures.add(CompletableFuture.runAsync(() ->
                            this.retrieveDoc(know, query, topK, scoreThreshold, finalAllDocuments,
                                    finalDocumentIdsFilter, metadataCondition)
                    , taskExecutor));
        }

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.get();
        allDocuments = finalAllDocuments;

        if (Boolean.TRUE.equals(rerankingEnable)) {
            allDocuments =
                    dataPostProcessor.invoke(rerankingMode, rerankingModel, weights, query,
                            allDocuments, scoreThreshold, topK);
        } else {
            if (IndexingTechnique.isHighQuality(indexingTechnique)) {
                allDocuments = this.calculateVectorScore(allDocuments, topK, scoreThreshold);
            } else if (IndexingTechnique.isEconomy(indexingTechnique)) {
                allDocuments = this.calculateKeywordScore(allDocuments, query, topK);
            }
        }

        List<String> knowIds = availableKnows.stream().map(Knowledge::getId).toList();
        //检索记录
        this.onQuery(query, knowIds, appId, userFrom);

        //检索完成记录
        this.onRetrievalEnd(allDocuments, messageId);

        return allDocuments;
    }


    //检索完成记录
    private void onRetrievalEnd(List<KDocument> allDocuments, String messageId) {
        //只处理edf供应的文档
        List<KDocument> edfDocuments = allDocuments.stream().filter(e -> Objects.equals(e.getProvider(), "edf")
                        && !e.getMetadata().isEmpty())
                .toList();
        //更新命中次数
        if (ObjectNull.isNotNull(edfDocuments)) {
            //确保顺序一致
            List<String> itemIds = new ArrayList<>();
            List<String> docIds = new ArrayList<>();
            edfDocuments.forEach(e -> {
                itemIds.add(e.getItemId());
                docIds.add(e.getDocId());
            });
            Map<String, KnowledgeItem> itemMap = itemService.listByIds(itemIds).stream()
                    .collect(Collectors.toMap(KnowledgeItem::getId, k -> k));
            //分别处理不同类id
            int index = 0;
            List<String> chunkNodeIds = new ArrayList<>();
            List<String> segmentNodeIds = new ArrayList<>();
            for (String itemId : itemIds) {
                KnowledgeItem item = itemMap.get(itemId);
                if (DocForm.HIERARCHICAL_MODEL.equals(item.getDocForm())) {
                    //docId代表子块index_node_id
                    chunkNodeIds.add(docIds.get(index++));
                } else {
                    //docId代表文本块index_node_id
                    segmentNodeIds.add(docIds.get(index++));
                }
            }

            //更新文本块命中次数
            if (ObjectNull.isNotNull(chunkNodeIds)) {
                //查出关联的文本块
                List<String> segmentIds = chunksService.lambdaQuery().in(ChildChunk::getIndexNodeId, chunkNodeIds)
                        .list().stream().map(ChildChunk::getSegmentId).distinct().toList();
                if (ObjectNull.isNotNull(segmentIds)) {
                    segmentsService.lambdaUpdate().in(ItemSegment::getId)
                            .setSql("hit_count = hit_count + 1").update();
                }
            }

            if (ObjectNull.isNotNull(segmentNodeIds)) {
                segmentsService.lambdaUpdate().in(ItemSegment::getIndexNodeId,segmentNodeIds)
                        .setSql("hit_count = hit_count + 1").update();
            }

            //todo 运行后置处理保存检索资源记录

        }
    }

    //检索记录
    private void onQuery(String query, List<String> knowIds, String appId, String userFrom) {
        if (StrUtil.isEmpty(query)) {
            return;
        }
        List<KnowledgeQuery> queries = new ArrayList<>();
        knowIds.forEach(knowId -> {
            KnowledgeQuery knowQuery = new KnowledgeQuery();
            knowQuery.setKnowId(knowId);
            knowQuery.setContent(query);
            knowQuery.setSource(TagType.APP.getValue());
            knowQuery.setSourceAppId(appId);
            knowQuery.setCreateByRole(userFrom);
            queries.add(knowQuery);
        });
        if (ObjectNull.isNotNull(queries)) {
            queriesService.saveBatch(queries);
        }

    }


    //计算关键词分数
    private List<KDocument> calculateKeywordScore(List<KDocument> allDocuments, String query, Integer topK) {
//        List<Set<String>> documentsKeywords = new ArrayList<>();
//        Set<String> allKeywords = new HashSet<>();
//        Set<String> queryKeywords = KeywordExtractor.extractKeywords(query, 20);
//        //计算查询关键词词频
//        Map<String, Long> queryKeywordFrequency = queryKeywords.stream()
//                .collect(Collectors.groupingBy(
//                        keyword -> keyword,
//                        Collectors.counting()
//                ));
//        for (KDocument document : allDocuments) {
//            if (!document.metadata().isEmpty()) {
//                Set<String> keywords = KeywordExtractor.extractKeywords(document.text(), 20);
//                document.setKeywords(keywords);
//                documentsKeywords.add(keywords);
//                allKeywords.addAll(keywords);
//            }
//        }
//
//        int totalDocuments = allDocuments.size();
//
//        Map<String, Double> keywordIdf = new HashMap<>();
//        //计算关键词IDF值
//        for (String keyword : allKeywords) {
//            //计算包含查询关键词的文档
//            long docCountContainingKeyword = documentsKeywords.stream()
//                    .filter(e -> e.contains(keyword)).count();
//            //设置IDF值
//            keywordIdf.put(keyword, Math.log(totalDocuments + 1) / (docCountContainingKeyword + 1));
//        }
//
//        Map<String, Double> queryTfidf = new HashMap<>();
//        for (Map.Entry<String, Long> keywordEntity : queryKeywordFrequency.entrySet()) {
//            Long tf = keywordEntity.getValue();
//            Double idf = keywordIdf.getOrDefault(keywordEntity.getKey(), 0.0D);
//            queryTfidf.put(keywordEntity.getKey(), tf * idf);
//        }
//
//        //计算所有文档的tf-idf值
//        List<Map<String, Double>> documentsTfIdf = new ArrayList<>();
//        for (Set<String> documentsKeyword : documentsKeywords) {
//            //计算文档关键词频
//            Map<String, Long> documentKeywordFrequency = documentsKeyword.stream()
//                    .collect(Collectors.groupingBy(
//                            keyword -> keyword,
//                            Collectors.counting()
//                    ));
//            Map<String, Double> documentTfIdf = new HashMap<>();
//            documentKeywordFrequency.forEach((k, v) -> {
//                Long tf = v;
//                Double idf = keywordIdf.getOrDefault(k, 0.0D);
//                documentTfIdf.put(k, tf * idf);
//            });
//            documentsTfIdf.add(documentTfIdf);
//        }

        //使用WeightRerankRunner方法
        WeightRerankRunner weightRerankRunner = new WeightRerankRunner(null, null);
        List<Float> similarities = weightRerankRunner.calculateKeywordScore(query, allDocuments);
        for (int i = 0; i < allDocuments.size(); i++) {
            KDocument document = allDocuments.get(i);
            if (!document.metadata().isEmpty()) {
                document.setScore(similarities.get(i));
            }
        }
        allDocuments.sort(Comparator.comparing(KDocument::getScore).reversed());
        if (Objects.nonNull(topK)) {
            return allDocuments.subList(0, Math.min(topK, allDocuments.size()));
        } else {
            return allDocuments;
        }

    }

    //计算向量分数
    private List<KDocument> calculateVectorScore(List<KDocument> allDocuments, Integer topK, Float scoreThreshold) {
        List<KDocument> filterDocuments = new ArrayList<>();
        for (KDocument document : allDocuments) {
            if (Objects.isNull(scoreThreshold) ||
                    DefaultVUtil.get(document.getScore(), 0.0f) >= scoreThreshold) {
                filterDocuments.add(document);
            }
        }

        if (ObjectNull.isNull(filterDocuments)) {
            return new ArrayList<>();
        }

        filterDocuments.sort(Comparator.comparing(KDocument::getScore).reversed());

        if (Objects.nonNull(topK)) {
            return filterDocuments.subList(0, Math.min(topK, filterDocuments.size()));
        } else {
            return filterDocuments;
        }
    }

    //文档检索处理
    private void retrieveDoc(Knowledge know, String query, Integer topK, Float scoreThreshold,
                             List<KDocument> allDocuments, List<String> documentIdsFilter,
                             MetadataCondition metadataCondition) {

        if (Objects.equals(know.getProvider(), "external")) {
            //todo 外部知识库检索处理
        } else {
            //获取知识库检索设置，没有则使用默认设置
            if (Objects.isNull(know.getRetrievalModel())) {
                know.setRetrievalModel(RetrievalSetting.DEFAULT_SETTING);
            }
            if (IndexingTechnique.isEconomy(know.getIndexingTechnique())) {
                retrievalHandler.retrieve(RetrievalMethod.KEYWORD_SEARCH.getValue(), know.getId(), query, topK,
                        0.0f, null, null, null, documentIdsFilter);
            } else {
                if (topK > 0) {
                    RetrievalSetting retrievalSetting = know.getRetrievalModel();
                    //reranking设置使用知识库的检索设置，不使用传入的
                    List<KDocument> documents = retrievalHandler.retrieve(
                            retrievalSetting.getSearchMethod().getValue(), know.getId(), query, topK,
                            DefaultVUtil.get(scoreThreshold, retrievalSetting.getScoreThreshold()),
                            Boolean.TRUE.equals(retrievalSetting.getRerankingEnable()) ?
                                    retrievalSetting.getRerankingModel() : null,
                            DefaultVUtil.get(retrievalSetting.getRerankingMode(), RerankingMode.RERANKING_MODEL),
                            retrievalSetting.getWeights(), documentIdsFilter);
                    allDocuments.addAll(documents);
                }
            }
        }
    }

}