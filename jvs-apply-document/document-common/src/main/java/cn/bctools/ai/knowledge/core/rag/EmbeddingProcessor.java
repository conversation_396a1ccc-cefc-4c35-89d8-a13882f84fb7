package cn.bctools.ai.knowledge.core.rag;

import cn.bctools.ai.knowledge.entity.bean.knowledge.EmbeddingModelSetting;
import cn.bctools.ai.knowledge.entity.bean.document.KDocument;
import cn.bctools.ai.knowledge.entity.constant.EmbeddingInputType;
import cn.bctools.ai.knowledge.entity.constant.RKey;
import cn.bctools.ai.knowledge.entity.data.KnowledgeEmbedding;
import cn.bctools.ai.knowledge.service.KnowledgeEmbeddingService;
import cn.bctools.ai.knowledge.util.TextProcessUtils;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.redis.utils.RedisUtils;
import com.google.common.primitives.Floats;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * 向量化处理器，请求embedding模型接口统一处理
 * 对应cached_embedding
 */
@RequiredArgsConstructor
@Slf4j
public class EmbeddingProcessor {

    private final KnowledgeEmbeddingService embeddingService;

    private final EmbeddingModelSetting embeddingModel;

    private final RedisUtils redisUtils;

    /**
     * 向量化文档
     */
    public List<float[]> embedDocuments(List<KDocument> textSegments) {
        // 初始化结果列表
        List<float[]> textEmbeddings = new ArrayList<>(Collections.nCopies(textSegments.size(), null));
        // 嵌入索引
        List<Integer> embeddingQueueIndices = new ArrayList<>();

        for (int i = 0; i < textSegments.size(); i++) {
            KDocument e = textSegments.get(i);
            String hash = e.getHash();
            KnowledgeEmbedding embedding = embeddingService.lambdaQuery().eq(KnowledgeEmbedding::getHash, hash)
                    .eq(KnowledgeEmbedding::getProviderName, embeddingModel.getEmbeddingProviderName())
                    .eq(KnowledgeEmbedding::getModelName, embeddingModel.getEmbeddingModelName())
                    .last("limit 1").one();

            if (Objects.nonNull(embedding)) {
                textEmbeddings.set(i, embedding.getEmbedding());
            } else {
                embeddingQueueIndices.add(i);
            }
        }

        if (!embeddingQueueIndices.isEmpty()) {
            List<KDocument> embeddingQueueTexts = embeddingQueueIndices.stream().map(textSegments::get).toList();
            List<float[]> embeddingQueueEmbeddings = new ArrayList<>();

            //根据模型信息获取最大可处理chunks
            int maxChunks = embeddingModel.getMaxChunks();

            //分批次批量处理文本
            for (int i = 0; i < embeddingQueueTexts.size(); i += maxChunks) {
                int end = Math.min(i + maxChunks, embeddingQueueTexts.size());
                List<KDocument> batchDocuments = embeddingQueueTexts.subList(i, end);
                List<float[]> embeddingResult = embeddingModel.getEmbeddingModel()
                        .invokeEmbedding(batchDocuments.stream().map(KDocument::getText).toList(),
                                EmbeddingInputType.DOCUMENT);

                //检查向量中是否存在 NaN（Not a Number）值
                /*
                影响：
                    数值计算问题：在向量归一化过程中可能出现除以零的情况
                    数据质量问题：某些文本可能无法正确生成向量
                    模型输出问题：模型可能输出无效的向量值
                 */
                for (float[] vector : embeddingResult) {
                    float[] embeddings = normalizeVector(vector);
                    //stackoverflow best way: https://stackoverflow.com/questions/20319813/how-to-check-list-containing-nan
                    if (!containsNaN(embeddings)) {
                        //for issue #11827  float values are not json compliant
                        embeddingQueueEmbeddings.add(embeddings);
                    } else {
                        embeddingQueueEmbeddings.add(null);
                        log.error("向量结果存在NaN情况: {}", embeddings);
                    }
                }
            }

            //保存新向量
            Set<String> cacheEmbeddings = new HashSet<>();
            List<KnowledgeEmbedding> knowledgeEmbeddingList = new ArrayList<>();
            for (int i = 0; i < Math.min(embeddingQueueIndices.size(), embeddingQueueEmbeddings.size()); i++) {
                int index = embeddingQueueIndices.get(i);
                float[] embedding = embeddingQueueEmbeddings.get(i);
                if (Objects.nonNull(embedding)) {
                    textEmbeddings.set(index, embedding);
                }

                String hash = textSegments.get(index).getHash();
                String text = textSegments.get(index).text();
                //新增embeddings缓存记录
                if (!cacheEmbeddings.contains(hash)) {
                    KnowledgeEmbedding knowledgeEmbedding = new KnowledgeEmbedding();
                    knowledgeEmbedding.setHash(hash);
                    knowledgeEmbedding.setProviderName(embeddingModel.getEmbeddingProviderName());
                    knowledgeEmbedding.setModelName(embeddingModel.getEmbeddingModelName());
                    knowledgeEmbedding.setText(text);
                    knowledgeEmbedding.setEmbedding(embedding);
                    knowledgeEmbeddingList.add(knowledgeEmbedding);
                    cacheEmbeddings.add(hash);
                }
            }
            embeddingService.saveBatch(knowledgeEmbeddingList);

        }

        //包装成Embedding
        return textEmbeddings;
    }

    //向量化查询值
    public float[] embedQuery(String query) {
        String hash = TextProcessUtils.generateTextHash(query);
        String embeddingCacheKey = String.format(RKey.EMBED_QUERY + "%s_%s_%s", embeddingModel.getEmbeddingProviderName(),
                embeddingModel.getEmbeddingModelName(), hash);
        //判断是否有缓存
        String embedding = (String) redisUtils.get(embeddingCacheKey);
        if (Objects.nonNull(embedding)) {
            redisUtils.expire(embeddingCacheKey, 600L);
            // 解码Base64，转换为浮点数列表
            byte[] decodedBytes = Base64.getDecoder().decode(embedding);
            return bytesToFloatArray(decodedBytes);
        }

        //获取查询值向量化数据
        List<float[]> vectors = embeddingModel.getEmbeddingModel()
                .invokeEmbedding(List.of(query), EmbeddingInputType.QUERY);
        float[] vector = vectors.get(0);
        //归一化处理
        float[] embeddingResults = normalizeVector(vector);
        if (containsNaN(embeddingResults)) {
            log.error("查询值向量化处理，查询值:( {} )，向量结果存在NaN情况: {}", query, embeddingResults);
            throw new BusinessException("询值向量化处理，向量结果存在NaN情况");
        }

        //转成字节数组，base64编码，然后写入redis
        byte[] bytes = floatListToBytes(embeddingResults);
        byte[] encode = Base64.getEncoder().encode(bytes);
        redisUtils.set(embeddingCacheKey, new String(encode), 600L);

        return embeddingResults;
    }

    //检验向量float数组是否Not-a-Number
    public static boolean containsNaN(float[] vector) {
        return Floats.asList(vector).stream().anyMatch(e -> Float.isNaN(e));
    }

    /**
     * 将字节数组转换为浮点数数组
     * 模拟numpy frombuffer dtype="float"
     */
    public static float[] bytesToFloatArray(byte[] bytes) {
        float[] result = new float[bytes.length / 4];
        for (int i = 0; i < result.length; i++) {
            int intBits = ((bytes[i * 4] & 0xFF) << 24) |
                    ((bytes[i * 4 + 1] & 0xFF) << 16) |
                    ((bytes[i * 4 + 2] & 0xFF) << 8) |
                    (bytes[i * 4 + 3] & 0xFF);
            result[i] = Float.intBitsToFloat(intBits);
        }
        return result;
    }

    /**
     * 将浮点数列表转换为字节数组
     */
    public static byte[] floatListToBytes(float[] floats) {
        byte[] result = new byte[floats.length * 4];
        for (int i = 0; i < floats.length; i++) {
            int intBits = Float.floatToIntBits(floats[i]);
            result[i * 4] = (byte) ((intBits >> 24) & 0xFF);
            result[i * 4 + 1] = (byte) ((intBits >> 16) & 0xFF);
            result[i * 4 + 2] = (byte) ((intBits >> 8) & 0xFF);
            result[i * 4 + 3] = (byte) (intBits & 0xFF);
        }
        return result;
    }

    /**
     * 归一化向量
     * 模拟 vector /  nympy.linalg.norm(vector)
     * 算式：向量各元素平方和的开平方（L2 范数）  sum(x²) ^ 0.5
     */
    public static float[] normalizeVector(float[] vector) {
        double norm = 0;
        for (float v : vector) {
            norm += v * v;
        }
        norm = Math.sqrt(norm);

        float[] result = new float[vector.length];
        for (int i = 0; i < vector.length; i++) {
            result[i] = (float) (vector[i] / norm);
        }

        return result;
    }

}