package cn.bctools.ai.app.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvokeFrom {

    SERVICE_API("SERVICE_API"),
    WEB_APP("WEB_APP"),
    EXPLORE("EXPLORE"),
    DEBUGGER("DEBUGGER");


    @EnumValue
    @JsonEnumDefaultValue
    private final String value;

    public String toSource() {
        return switch (this) {
            case WEB_APP -> "web_app";
            case SERVICE_API -> "api";
            case EXPLORE -> "explore_app";
            case DEBUGGER -> "dev";
        };
    }
}