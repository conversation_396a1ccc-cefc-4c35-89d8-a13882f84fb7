# 🏗️ 流式输出架构重构说明

## **重构背景**

原先的设计存在架构问题：
- `jvs-function-run` (核心工作流执行模块) 依赖 `document-common` (业务模块)
- 违反了依赖倒置原则，核心模块不应该依赖业务模块
- 导致模块耦合度过高，不利于维护和扩展

## **新架构设计**

### **模块层次结构**
```
jvs-stream-core (流式输出核心模块)
    ↑
    ├── jvs-function-run (工作流执行核心)
    └── document-common (文档业务模块)
```

### **依赖关系**
- `jvs-stream-core`: 独立的核心模块，不依赖任何业务模块
- `jvs-function-run`: 依赖 `jvs-stream-core`，提供工作流执行能力
- `document-common`: 依赖 `jvs-stream-core`，提供业务层流式输出功能

## **📁 模块职责划分**

### **jvs-stream-core (流式输出核心模块)**
**职责**: 提供通用的流式输出基础设施

**包含内容**:
- `StreamConstants` - 常量定义
- `StreamOutputType` - 输出类型枚举
- `StreamEventType` - 事件类型枚举
- `StreamNodeExecutionDto` - 节点执行DTO
- `StreamExecutionListener` - 监听器接口
- `DefaultStreamExecutionListener` - 默认监听器实现
- `StreamExecutionUtils` - 工具类

**特点**:
- ✅ 无业务逻辑，纯技术组件
- ✅ 可被任何模块复用
- ✅ 遵循单一职责原则

### **jvs-function-run (工作流执行核心)**
**职责**: 提供工作流执行引擎

**依赖**: `jvs-stream-core`

**使用方式**:
```java
// 获取流式监听器
StreamExecutionListener listener = StreamExecutionUtils.getPrimaryStreamListener();

// 通知工作流开始
listener.onWorkflowStarted(executionId, totalNodes);

// 通知节点执行
listener.onNodeStarted(nodeExecution);
listener.onNodeCompleted(nodeExecution);
```

### **document-common (文档业务模块)**
**职责**: 提供文档相关的业务功能和高级流式输出策略

**依赖**: `jvs-stream-core`

**包含内容**:
- `StreamExecutionManager` - 高级流式执行管理器
- `StreamOutputStrategy` - 输出策略接口
- `FluxStreamOutputStrategy` - Flux输出策略
- 其他业务相关的流式输出组件

## **🔄 重构过程**

### **1. 创建独立核心模块**
```bash
# 新建模块
jvs-stream-core/
├── pom.xml
└── src/main/java/cn/bctools/stream/
    ├── constants/StreamConstants.java
    ├── enums/StreamOutputType.java
    ├── enums/StreamEventType.java
    ├── dto/StreamNodeExecutionDto.java
    ├── listener/StreamExecutionListener.java
    ├── listener/DefaultStreamExecutionListener.java
    └── utils/StreamExecutionUtils.java
```

### **2. 修改依赖关系**
```xml
<!-- jvs-function-run/pom.xml -->
<dependency>
    <groupId>cn.bctools</groupId>
    <artifactId>jvs-stream-core</artifactId>
    <version>${project.version}</version>
</dependency>

<!-- document-common/pom.xml -->
<dependency>
    <groupId>cn.bctools</groupId>
    <artifactId>jvs-stream-core</artifactId>
    <version>${project.version}</version>
</dependency>
```

### **3. 重构代码调用**
```java
// 修改前 (jvs-function-run 中)
StreamExecutionManager streamManager = getStreamManager();
streamManager.onNodeStarted(executionId, nodeExecution);

// 修改后 (jvs-function-run 中)
StreamExecutionListener listener = StreamExecutionUtils.getPrimaryStreamListener();
listener.onNodeStarted(nodeExecution);
```

## **🎯 重构优势**

### **1. 架构清晰**
- 核心模块独立，职责单一
- 依赖关系正确，符合设计原则
- 模块边界清晰，便于维护

### **2. 复用性强**
- `jvs-stream-core` 可被任何需要流式输出的模块使用
- 不绑定特定业务场景
- 支持多种实现方式

### **3. 扩展性好**
- 新增流式输出类型只需实现接口
- 支持自定义监听器
- 配置灵活，支持多种部署方式

### **4. 测试友好**
- 核心逻辑独立，易于单元测试
- 接口设计清晰，便于Mock
- 依赖注入，支持测试替换

## **📊 对比分析**

| 方面 | 重构前 | 重构后 |
|------|--------|--------|
| 依赖方向 | 核心→业务 ❌ | 业务→核心 ✅ |
| 模块耦合 | 高耦合 ❌ | 低耦合 ✅ |
| 复用性 | 难复用 ❌ | 易复用 ✅ |
| 测试性 | 难测试 ❌ | 易测试 ✅ |
| 维护性 | 难维护 ❌ | 易维护 ✅ |

## **🚀 使用示例**

### **在工作流引擎中使用**
```java
@Component
public class WorkflowEngine {
    
    public void executeWorkflow(WorkflowDefinition workflow) {
        // 获取监听器
        StreamExecutionListener listener = StreamExecutionUtils.getPrimaryStreamListener();
        
        // 开始执行
        String executionId = StreamExecutionUtils.generateExecutionId();
        listener.onWorkflowStarted(executionId, workflow.getNodes().size());
        
        // 执行节点
        for (Node node : workflow.getNodes()) {
            StreamNodeExecutionDto nodeExecution = new StreamNodeExecutionDto()
                .setExecutionId(executionId)
                .setNodeId(node.getId())
                .setNodeName(node.getName())
                .setRunning();
                
            listener.onNodeStarted(nodeExecution);
            
            try {
                Object result = executeNode(node);
                nodeExecution.setCompleted().setOutputs(result);
                listener.onNodeCompleted(nodeExecution);
            } catch (Exception e) {
                nodeExecution.setFailed(e.getMessage());
                listener.onNodeFailed(nodeExecution);
            }
        }
        
        listener.onWorkflowCompleted(executionId, "执行完成");
    }
}
```

### **自定义监听器实现**
```java
@Component
public class CustomStreamListener implements StreamExecutionListener {
    
    @Override
    public void onNodeCompleted(StreamNodeExecutionDto nodeExecution) {
        // 发送到消息队列
        messageQueue.send("node.completed", nodeExecution);
        
        // 更新数据库
        nodeExecutionRepository.save(nodeExecution);
        
        // 发送WebSocket消息
        webSocketService.broadcast("node_completed", nodeExecution);
    }
}
```

## **🔧 迁移指南**

### **对于使用方**
1. 更新依赖：将 `document-common` 依赖改为 `jvs-stream-core`
2. 更新导入：`cn.bctools.ai.stream.*` → `cn.bctools.stream.*`
3. 更新调用：使用 `StreamExecutionListener` 替代 `StreamExecutionManager`

### **对于扩展方**
1. 实现 `StreamExecutionListener` 接口
2. 注册为Spring Bean
3. 系统会自动发现并使用

## **✅ 验证清单**

- [x] `jvs-stream-core` 模块创建完成
- [x] 核心类迁移到新模块
- [x] 依赖关系修正
- [x] 导入路径更新
- [x] 功能验证通过
- [x] 架构文档完善

这次重构彻底解决了架构问题，建立了清晰的模块边界和正确的依赖关系，为后续的功能扩展和维护奠定了坚实的基础。
