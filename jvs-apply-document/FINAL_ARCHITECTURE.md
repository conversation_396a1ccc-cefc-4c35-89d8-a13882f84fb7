# 🏆 最终优雅架构设计

## **问题解决**

您完全正确！确实存在两套冲突的策略模式：

### **❌ 冲突的双重策略**
1. **document-mgr**: `WorkflowExecutionProcessorFactory` + `WorkflowExecutionProcessor`
2. **edf-stream-core**: `StreamOutputStrategyFactory` + `StreamOutputStrategy`

这是明显的设计冲突和功能重复！

### **✅ 最优解决方案**

**保留 document-mgr 的 WorkflowExecutionProcessor 策略**，原因：

1. **更完整** - 包含HTTP处理 + 流式输出的完整链路
2. **更实用** - 直接面向业务需求（SSE、Flux、WebSocket、同步）
3. **更清晰** - 一个处理器负责一种完整的交互方式
4. **已经成熟** - 代码更完善，功能更强大

## **最终架构**

### **edf-stream-core (基础设施层)**
```
📦 edf-stream-core
├── constants/StreamConstants.java          ⭐ 常量管理
├── dto/StreamNodeExecutionDto.java         ⭐ 节点执行DTO
├── enums/StreamOutputType.java             ⭐ 输出类型枚举
├── listener/StreamExecutionListener.java  ⭐ 监听器接口
├── listener/DefaultStreamExecutionListener.java ⭐ 默认实现
├── manager/StreamOutputManager.java       ⭐⭐⭐ 核心输出管理器
└── utils/StreamExecutionUtils.java        ⭐ 工具类
```

**职责**: 提供流式输出的基础设施，不包含业务逻辑

### **document-mgr (业务处理层)**
```
📦 document-mgr
├── processor/WorkflowExecutionProcessor.java              ⭐ 处理器接口
├── processor/impl/FluxWorkflowExecutionProcessor.java     ⭐ Flux处理器
├── processor/impl/SseWorkflowExecutionProcessor.java      ⭐ SSE处理器
├── processor/impl/WebSocketWorkflowExecutionProcessor.java ⭐ WebSocket处理器
├── processor/impl/SyncWorkflowExecutionProcessor.java     ⭐ 同步处理器
├── factory/WorkflowExecutionProcessorFactory.java         ⭐ 处理器工厂
├── controller/WorkflowApiController.java                  ⭐ 统一API控制器
└── websocket/WorkflowWebSocketHandler.java                ⭐ WebSocket处理器
```

**职责**: 处理不同类型的HTTP请求，提供完整的业务功能

## **核心设计理念**

### **单一策略模式**
```java
// 统一的处理器接口
public interface WorkflowExecutionProcessor {
    StreamOutputType getSupportedOutputType();
    boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest);
    Object execute(WorkflowExecutionRequest request, ...);
    int getPriority();
}

// 工厂选择最佳处理器
@Component
public class WorkflowExecutionProcessorFactory {
    public WorkflowExecutionProcessor getBestProcessor(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        return processors.stream()
                .filter(p -> p.supports(request, httpRequest))
                .min(Comparator.comparingInt(WorkflowExecutionProcessor::getPriority))
                .orElse(defaultProcessor);
    }
}
```

### **处理器实现示例**
```java
@Component
public class FluxWorkflowExecutionProcessor implements WorkflowExecutionProcessor {
    
    @Autowired
    private StreamOutputManager streamOutputManager;
    
    @Override
    public Object execute(WorkflowExecutionRequest request, ...) {
        // 1. 生成执行ID
        String executionId = StreamExecutionUtils.generateExecutionId();
        
        // 2. 创建Flux连接
        Flux<Object> flux = streamOutputManager.createFluxConnection(executionId);
        
        // 3. 异步执行工作流
        executeWorkflowAsync(executionId, appDetail, user, params);
        
        return flux;
    }
    
    private void executeWorkflowAsync(String executionId, ...) {
        CompletableFuture.runAsync(() -> {
            // 通知开始
            StreamExecutionUtils.notifyWorkflowStarted(executionId, totalNodes);
            
            // 执行工作流
            Object result = appGenerateService.generate(...);
            
            // 通知完成
            StreamExecutionUtils.notifyWorkflowCompleted(executionId, result);
        });
    }
}
```

## **完整数据流**

```
1. 客户端请求
   ↓
2. WorkflowApiController.runWorkflow()
   ↓
3. WorkflowExecutionProcessorFactory.getBestProcessor()
   ↓
4. FluxWorkflowExecutionProcessor.execute()
   ↓
5. StreamOutputManager.createFluxConnection()
   ↓
6. AppGenerateService.generate() (异步执行)
   ↓
7. RuleDesignUtils (节点执行时)
   ↓
8. StreamExecutionUtils.notifyNodeCompleted()
   ↓
9. DefaultStreamExecutionListener.onNodeCompleted()
   ↓
10. StreamOutputManager.sendNodeCompleted()
    ↓
11. 数据推送到 Flux/SSE/WebSocket
    ↓
12. 客户端实时接收数据
```

## **关键优势**

### **1. 单一策略模式**
- ✅ 只有一套策略模式，没有冲突
- ✅ 每个处理器负责完整的请求-响应链路
- ✅ 工厂模式智能选择最佳处理器

### **2. 职责清晰**
- **edf-stream-core**: 基础设施，可复用
- **document-mgr**: 业务逻辑，面向具体需求

### **3. 真正的流式输出**
- `StreamOutputManager` 真正实现数据推送
- 支持SSE、Flux、WebSocket三种协议
- 统一的事件模型和数据格式

### **4. 优雅的扩展性**
```java
// 新增处理器只需实现接口
@Component
public class GraphQLWorkflowExecutionProcessor implements WorkflowExecutionProcessor {
    @Override
    public boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        return httpRequest.getRequestURI().contains("/graphql");
    }
    
    @Override
    public Object execute(...) {
        // GraphQL特定的处理逻辑
    }
}
```

## **使用示例**

### **客户端调用**
```javascript
// Flux方式
fetch('/api/workflows/run', {
    method: 'POST',
    headers: { 'Accept': 'application/stream+json' },
    body: JSON.stringify({ input: 'test' })
}).then(response => {
    const reader = response.body.getReader();
    // 读取流式数据
});

// SSE方式
const eventSource = new EventSource('/api/workflows/run', {
    method: 'POST',
    headers: { 'Accept': 'text/event-stream' },
    body: JSON.stringify({ input: 'test' })
});

// WebSocket方式
const ws = new WebSocket('ws://localhost:8080/ws/workflow-execution');
```

### **服务端处理**
```java
@PostMapping("/api/workflows/run")
public Object runWorkflow(@RequestBody WorkflowExecutionRequest request,
                         HttpServletRequest httpRequest,
                         HttpServletResponse httpResponse) {
    
    // 工厂自动选择最佳处理器
    WorkflowExecutionProcessor processor = processorFactory.getBestProcessor(request, httpRequest);
    
    // 执行处理
    return processor.execute(request, httpRequest, httpResponse, appDetail, user);
}
```

## **总结**

最终架构特点：
- **🎯 单一策略** - 只保留最强大的 WorkflowExecutionProcessor 策略
- **🏗️ 分层清晰** - 基础设施层 + 业务处理层
- **🚀 真实可用** - 真正的流式数据输出
- **🔧 易于扩展** - 新增处理器只需实现接口
- **💡 优雅简洁** - 没有冗余代码，每个类都有明确作用

这就是最优雅、最强大的流式输出架构！
