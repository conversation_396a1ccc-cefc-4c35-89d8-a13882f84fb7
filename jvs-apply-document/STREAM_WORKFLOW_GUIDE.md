# 🚀 流式工作流执行系统使用指南

## 概述

本系统将原本同步执行的逻辑引擎改造为流式输出模式，仿照Dify工作流的方式，支持多种流式输出方式：**Flux**、**WebSocket**、**SSE**、**控制台日志**等。

## 🎯 核心特性

### ✨ 多种流式输出方式
- **Flux**: 响应式流，支持背压和异步处理
- **WebSocket**: 双向实时通信
- **SSE**: 单向服务器推送事件
- **Console**: 控制台日志输出

### 📊 实时监控
- 节点级别的执行状态跟踪
- 实时进度更新
- 详细的输入输出信息
- 执行统计和性能监控

### 🏭 工厂模式设计
- 策略模式支持多种输出方式
- 统一的流式管理器
- 可扩展的架构设计

## 🔧 使用方式

### 1. Flux流式输出

#### 前端JavaScript示例
```javascript
// 使用fetch API获取Flux流
fetch('/api/workflows/run/flux', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/stream+json'
    },
    body: JSON.stringify({
        input: 'your input data',
        params: { key: 'value' }
    })
})
.then(response => {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    function readStream() {
        return reader.read().then(({ done, value }) => {
            if (done) {
                console.log('Stream completed');
                return;
            }
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            lines.forEach(line => {
                if (line.trim()) {
                    try {
                        const event = JSON.parse(line);
                        handleStreamEvent(event);
                    } catch (e) {
                        console.error('Parse error:', e);
                    }
                }
            });
            
            return readStream();
        });
    }
    
    return readStream();
})
.catch(error => console.error('Stream error:', error));

function handleStreamEvent(event) {
    console.log('Event:', event.event, 'Data:', event.data);
    
    switch (event.event) {
        case 'workflow_started':
            console.log('🚀 工作流开始执行');
            break;
        case 'node_completed':
            console.log('✅ 节点执行完成:', event.data.nodeName);
            break;
        case 'workflow_completed':
            console.log('🎉 工作流执行完成');
            break;
    }
}
```

#### Spring WebFlux客户端示例
```java
@RestController
public class WorkflowClientController {
    
    @Autowired
    private WebClient webClient;
    
    @GetMapping("/test-flux")
    public Flux<String> testFluxWorkflow() {
        return webClient.post()
                .uri("/api/workflows/run/flux")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_STREAM_JSON)
                .bodyValue(Map.of("input", "test data"))
                .retrieve()
                .bodyToFlux(String.class)
                .doOnNext(event -> log.info("收到事件: {}", event))
                .doOnComplete(() -> log.info("流式执行完成"))
                .doOnError(error -> log.error("流式执行失败", error));
    }
}
```

### 2. SSE (Server-Sent Events) 流式输出

#### 前端JavaScript示例
```javascript
// 创建SSE连接
const eventSource = new EventSource('/api/workflows/run', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'X-Stream-Type': 'sse'
    },
    body: JSON.stringify({
        input: 'your input data'
    })
});

// 监听不同类型的事件
eventSource.addEventListener('workflow_started', function(event) {
    const data = JSON.parse(event.data);
    console.log('🚀 工作流开始执行:', data);
    updateProgress(0, data.totalNodes);
});

eventSource.addEventListener('node_started', function(event) {
    const data = JSON.parse(event.data);
    console.log('▶️ 节点开始执行:', data.nodeName);
    addNodeCard(data);
});

eventSource.addEventListener('node_completed', function(event) {
    const data = JSON.parse(event.data);
    console.log('✅ 节点执行完成:', data.nodeName);
    updateNodeCard(data);
    updateProgress(data.progress, data.totalNodes);
});

eventSource.addEventListener('workflow_completed', function(event) {
    const data = JSON.parse(event.data);
    console.log('🎉 工作流执行完成:', data);
    eventSource.close();
});

eventSource.addEventListener('error', function(event) {
    console.error('❌ SSE连接错误:', event);
});

// 辅助函数
function updateProgress(progress, total) {
    document.getElementById('progress').style.width = progress + '%';
    document.getElementById('progress-text').textContent = `${progress}% (${total} nodes)`;
}

function addNodeCard(nodeData) {
    const container = document.getElementById('nodes-container');
    const card = document.createElement('div');
    card.id = 'node-' + nodeData.nodeId;
    card.className = 'node-card running';
    card.innerHTML = `
        <h3>${nodeData.nodeName}</h3>
        <p>状态: <span class="status">执行中</span></p>
        <p>功能: ${nodeData.functionName}</p>
    `;
    container.appendChild(card);
}

function updateNodeCard(nodeData) {
    const card = document.getElementById('node-' + nodeData.nodeId);
    if (card) {
        card.className = 'node-card completed';
        card.querySelector('.status').textContent = '已完成';
        card.innerHTML += `
            <p>耗时: ${nodeData.duration}ms</p>
            <details>
                <summary>输入参数</summary>
                <pre>${JSON.stringify(nodeData.inputs, null, 2)}</pre>
            </details>
            <details>
                <summary>输出结果</summary>
                <pre>${JSON.stringify(nodeData.outputs, null, 2)}</pre>
            </details>
        `;
    }
}
```

### 3. WebSocket流式输出

#### 前端JavaScript示例
```javascript
// 首先获取WebSocket连接信息
fetch('/api/workflows/execution/info', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ input: 'your data' })
})
.then(response => response.json())
.then(data => {
    const executionId = data.executionId;
    
    // 创建WebSocket连接
    const ws = new WebSocket(`ws://localhost:8080/ws/workflow-execution/${executionId}`);
    
    ws.onopen = function() {
        console.log('🔗 WebSocket连接已建立');
        
        // 启动工作流执行
        fetch('/api/workflows/run', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Stream-Type': 'websocket',
                'X-Execution-Id': executionId
            },
            body: JSON.stringify({ input: 'your data' })
        });
    };
    
    ws.onmessage = function(event) {
        const message = JSON.parse(event.data);
        console.log('📨 收到消息:', message);
        
        handleWebSocketEvent(message.event, message.data);
    };
    
    ws.onclose = function() {
        console.log('🚫 WebSocket连接已关闭');
    };
    
    ws.onerror = function(error) {
        console.error('❌ WebSocket错误:', error);
    };
});

function handleWebSocketEvent(eventType, data) {
    switch (eventType) {
        case 'workflow_started':
            console.log('🚀 工作流开始执行');
            break;
        case 'node_completed':
            console.log('✅ 节点执行完成:', data.nodeName);
            break;
        case 'workflow_completed':
            console.log('🎉 工作流执行完成');
            break;
    }
}
```

### 4. 控制台日志输出

#### 后端Java示例
```java
@RestController
public class WorkflowController {
    
    @Autowired
    private StreamExecutionManager streamManager;
    
    @PostMapping("/workflows/run/console")
    public R<String> runWorkflowWithConsoleOutput(@RequestBody Map<String, Object> params) {
        // 创建控制台输出上下文
        StreamExecutionContext context = streamManager.createContext(
            "app-id",
            "rule-secret", 
            params,
            StreamOutputType.CONSOLE
        );
        
        // 执行工作流（会输出到控制台和日志文件）
        // ... 执行逻辑
        
        return R.ok("工作流执行完成，请查看控制台输出");
    }
}
```

## 🎨 高级用法

### 1. 组合输出策略

```java
// 同时输出到控制台和SSE
List<StreamOutputType> outputTypes = Arrays.asList(
    StreamOutputType.CONSOLE,
    StreamOutputType.SSE
);

StreamOutputStrategy compositeStrategy = strategyFactory.createCompositeStrategy(outputTypes);
```

### 2. 自定义流式输出策略

```java
@Component
public class CustomStreamOutputStrategy implements StreamOutputStrategy {
    
    @Override
    public StreamOutputType getOutputType() {
        return StreamOutputType.CUSTOM;
    }
    
    @Override
    public void onNodeCompleted(StreamExecutionContext context, StreamNodeExecutionDto nodeExecution) {
        // 自定义处理逻辑
        // 例如：发送到消息队列、存储到数据库等
        sendToMessageQueue(nodeExecution);
        saveToDatabase(nodeExecution);
    }
    
    // 实现其他方法...
}
```

### 3. 动态选择输出策略

```java
@PostMapping("/workflows/run/dynamic")
public Object runWorkflowDynamic(HttpServletRequest request, @RequestBody Map<String, Object> params) {
    // 根据请求头动态选择输出策略
    String accept = request.getHeader("Accept");
    String streamType = request.getHeader("X-Stream-Type");
    
    StreamOutputType outputType;
    if ("application/stream+json".equals(accept)) {
        outputType = StreamOutputType.FLUX;
    } else if ("text/event-stream".equals(accept)) {
        outputType = StreamOutputType.SSE;
    } else if ("websocket".equals(streamType)) {
        outputType = StreamOutputType.WEBSOCKET;
    } else {
        outputType = StreamOutputType.CONSOLE;
    }
    
    // 创建上下文并执行
    StreamExecutionContext context = streamManager.createContext(
        "app-id", "rule-secret", params, outputType
    );
    
    return executeWorkflow(context);
}
```

## 📊 监控和管理

### 1. 获取执行状态

```javascript
// 获取执行状态
fetch(`/api/workflows/execution/${executionId}/status`)
.then(response => response.json())
.then(data => {
    console.log('执行状态:', data);
    console.log('总节点数:', data.totalNodes);
    console.log('已执行:', data.executedNodes);
    console.log('进度:', data.progress + '%');
});
```

### 2. 取消执行

```javascript
// 取消执行
fetch(`/api/workflows/execution/${executionId}/cancel`, {
    method: 'POST'
})
.then(response => response.json())
.then(data => {
    console.log('执行已取消:', data);
});
```

### 3. 获取管理器统计

```java
@GetMapping("/workflows/stats")
public R<StreamExecutionManager.ExecutionStats> getExecutionStats() {
    return R.ok(streamExecutionManager.getExecutionStats());
}
```

## 🔧 配置选项

### application.yml配置

```yaml
# 流式输出配置
stream:
  execution:
    # 默认输出类型
    default-output-type: console
    # 上下文过期时间（分钟）
    context-expire-minutes: 30
    # 异步执行线程池大小
    async-pool-size: 10
    # 清理任务间隔（分钟）
    cleanup-interval-minutes: 5

# WebSocket配置
websocket:
  enabled: true
  path: /ws/workflow-execution

# SSE配置  
sse:
  timeout-seconds: 300
  
# Flux配置
flux:
  buffer-size: 1000
  timeout-seconds: 600
```

## 🚀 性能优化建议

1. **合理设置超时时间**: 根据工作流复杂度设置合适的超时时间
2. **使用背压控制**: Flux流式输出支持背压，避免内存溢出
3. **及时清理资源**: 执行完成后及时清理上下文和连接
4. **监控内存使用**: 大量并发执行时注意内存监控
5. **使用连接池**: WebSocket和SSE连接使用连接池管理

## 🐛 故障排除

### 常见问题

1. **连接超时**: 检查网络连接和超时配置
2. **内存泄漏**: 确保执行完成后清理资源
3. **事件丢失**: 检查网络稳定性和缓冲区大小
4. **性能问题**: 监控线程池使用情况和GC状态

### 调试技巧

1. 启用详细日志：
```yaml
logging:
  level:
    cn.bctools.ai.stream: DEBUG
```

2. 使用JVM参数监控：
```bash
-XX:+PrintGCDetails -XX:+PrintGCTimeStamps
```

3. 使用Spring Boot Actuator监控：
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,info
```

## 📝 更新日志

### v1.0.0
- ✅ 支持Flux、WebSocket、SSE、控制台四种输出方式
- ✅ 工厂模式和策略模式设计
- ✅ 统一的流式执行管理器
- ✅ 完整的前端示例和文档

### 后续计划
- [ ] 支持更多输出方式（消息队列、数据库等）
- [ ] 添加执行历史查询功能
- [ ] 支持工作流暂停和恢复
- [ ] 集成监控和告警系统
