# 🚀 完整流式输出流程指南

## **整体架构**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  jvs-function-  │    │  edf-stream-    │    │  document-mgr   │
│  run (核心)     │───▶│  core (基础)    │◀───│  (业务端点)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
   工作流执行引擎          流式输出基础设施        HTTP端点实现
```

## **完整执行流程**

### **1. 客户端发起请求**
```javascript
// SSE方式
fetch('/api/stream/workflows/sse', {
    method: 'POST',
    headers: { 'Accept': 'text/event-stream' },
    body: JSON.stringify({ input: 'test data' })
});

// Flux方式  
fetch('/api/stream/workflows/flux', {
    method: 'POST',
    headers: { 'Accept': 'application/stream+json' },
    body: JSON.stringify({ input: 'test data' })
});

// WebSocket方式
const ws = new WebSocket('ws://localhost:8080/ws/workflow-execution');
ws.onopen = () => {
    ws.send(JSON.stringify({
        action: 'subscribe',
        executionId: 'exec_123456'
    }));
};
```

### **2. document-mgr接收请求**
- `StreamWorkflowController` 接收HTTP请求
- 创建执行ID: `exec_1234567890_abc123`
- 调用 `StreamOutputManager` 创建对应的连接（SSE/Flux/WebSocket）
- 异步调用 `AppGenerateService.generate()` 执行工作流

### **3. 工作流执行过程**
- `AppGenerateService` 调用 `jvs-function-run` 模块
- `RuleDesignUtils` 在节点执行时调用监听器：
  ```java
  StreamExecutionListener listener = StreamExecutionUtils.getPrimaryStreamListener();
  listener.onNodeStarted(nodeExecution);
  ```

### **4. 流式输出传递**
- `DefaultStreamExecutionListener` 接收事件
- 调用 `StreamOutputManager` 的对应方法
- `StreamOutputManager` 同时发送到SSE、Flux、WebSocket三种连接

### **5. 客户端接收数据**
```javascript
// SSE接收
eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('收到事件:', data.event, data.data);
};

// WebSocket接收
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('收到事件:', data.event, data.data);
};
```

## **核心类职责**

### **edf-stream-core 模块**

#### **StreamConstants**
- 作用：统一管理所有常量
- 避免硬编码，提供类型安全

#### **StreamNodeExecutionDto**
- 作用：节点执行信息的数据传输对象
- 包含节点状态、执行时间、输入输出等信息

#### **StreamExecutionListener**
- 作用：监听器接口，定义工作流执行事件回调
- 被 `jvs-function-run` 调用，通知执行事件

#### **DefaultStreamExecutionListener**
- 作用：默认监听器实现
- 同时输出控制台日志和调用 `StreamOutputManager`

#### **StreamOutputManager**
- 作用：**核心类** - 真正实现流式数据输出
- 管理SSE、Flux、WebSocket连接
- 将事件数据发送到所有活跃连接

#### **StreamExecutionUtils**
- 作用：工具类，提供便捷的监听器调用方法
- 简化 `jvs-function-run` 中的调用代码

### **document-mgr 模块**

#### **StreamWorkflowController**
- 作用：**关键类** - 提供真正的HTTP端点
- `/api/stream/workflows/sse` - SSE端点
- `/api/stream/workflows/flux` - Flux端点
- 创建连接并异步执行工作流

#### **WorkflowWebSocketHandler**
- 作用：WebSocket处理器
- 处理WebSocket连接、消息、订阅等

#### **WebSocketConfig**
- 作用：WebSocket配置
- 注册WebSocket端点路径

## **数据流向图**

```
客户端请求
    ↓
StreamWorkflowController (创建连接)
    ↓
StreamOutputManager (管理连接)
    ↓
AppGenerateService (异步执行)
    ↓
RuleDesignUtils (节点执行)
    ↓
StreamExecutionListener (事件通知)
    ↓
StreamOutputManager (发送数据)
    ↓
SSE/Flux/WebSocket (推送到客户端)
```

## **关键改进点**

### **1. 真正的流式输出**
- `StreamOutputManager` 真正实现了数据推送
- 支持SSE、Flux、WebSocket三种方式
- 数据实时推送，不是轮询

### **2. 统一的事件模型**
```json
{
  "event": "node_started",
  "data": {
    "executionId": "exec_123",
    "nodeId": "node_1",
    "nodeName": "数据处理",
    "status": "running"
  },
  "timestamp": 1703123456789
}
```

### **3. 完整的WebSocket实现**
- 支持订阅/取消订阅
- 心跳检测
- 错误处理
- 连接管理

### **4. 配置化管理**
- `StreamConstants` 统一常量管理
- 支持超时配置
- 支持连接数限制

## **使用示例**

### **前端SSE连接**
```javascript
const eventSource = new EventSource('/api/stream/workflows/sse', {
    method: 'POST',
    body: JSON.stringify({ input: 'test' })
});

eventSource.addEventListener('workflow_started', (event) => {
    console.log('工作流开始:', JSON.parse(event.data));
});

eventSource.addEventListener('node_completed', (event) => {
    console.log('节点完成:', JSON.parse(event.data));
});
```

### **前端WebSocket连接**
```javascript
const ws = new WebSocket('ws://localhost:8080/ws/workflow-execution');

ws.onopen = () => {
    // 订阅特定执行ID
    ws.send(JSON.stringify({
        action: 'subscribe',
        executionId: 'exec_123456'
    }));
};

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    switch(data.event) {
        case 'node_started':
            console.log('节点开始:', data.data);
            break;
        case 'node_completed':
            console.log('节点完成:', data.data);
            break;
    }
};
```

## **监控和调试**

### **连接状态检查**
```bash
# 检查活跃连接数
GET /api/stream/workflows/connections/count

# 检查特定执行状态
GET /api/stream/workflows/{executionId}/status
```

### **日志监控**
```
🚀 开始SSE流式执行工作流 - 执行ID: exec_123
🔗 SSE连接已建立 - 执行ID: exec_123
▶️ 节点开始执行 - 节点: 数据处理
✅ 节点执行完成 - 节点: 数据处理, 耗时: 1500ms
🎉 工作流执行完成 - 执行ID: exec_123
```

## **性能优化**

1. **连接池管理** - 自动清理过期连接
2. **内存控制** - 限制最大连接数
3. **异步处理** - 所有IO操作异步化
4. **错误恢复** - 连接断开自动重连机制

这个设计解决了您提出的所有问题：
- ✅ 真正的流式数据输出到SSE/Flux/WebSocket
- ✅ 每个类都有明确的作用，没有冗余方法
- ✅ 模块职责清晰，没有重叠
- ✅ 配置类被实际使用
- ✅ 完整的WebSocket端点实现
