package cn.bctools;

import cn.bctools.oauth2.annotation.EnableJvsMgrResourceServer;
import cn.bctools.rule.entity.enums.InputTypeTransformInterface;
import cn.bctools.rule.utils.RuleDesignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@RefreshScope
@EnableAsync
@EnableJvsMgrResourceServer
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
@Slf4j
public class DocumentMgrApplication {

    public static void main(String[] args) {
        long startTime = System.currentTimeMillis();
        log.info("🚀 AI中台启动中...");

        try {
            // 启动Spring应用
            ConfigurableApplicationContext context = SpringApplication.run(DocumentMgrApplication.class, args);

            // 异步初始化逻辑引擎
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始异步初始化逻辑引擎...");
                    context.getBeansOfType(InputTypeTransformInterface.class)
                            .values()
                            .forEach(e ->
                                    RuleDesignUtils.inputTypeTransformInterfaceMap.put(e.name(), e));
                    log.info("逻辑引擎初始化完成");
                } catch (Exception e) {
                    log.error("逻辑引擎初始化失败", e);
                }
            });

            long endTime = System.currentTimeMillis();
            log.info("✅ AI中台启动完成，耗时: {}ms", endTime - startTime);

        } catch (Exception e) {
            log.error("❌ AI中台启动失败", e);
            System.exit(1);
        }
    }
}