package cn.bctools.ai.web.processor.impl;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.stream.constants.StreamConstants;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.dto.WorkflowExecutionResponse;
import cn.bctools.ai.web.processor.WorkflowExecutionProcessor;
import cn.bctools.ai.web.utils.WorkflowExecutionUtils;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;

/**
 * 同步工作流执行处理器
 * 用于传统的同步执行方式
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncWorkflowExecutionProcessor implements WorkflowExecutionProcessor {
    
    @Autowired
    private AppGenerateService appGenerateService;
    
    @Override
    public StreamOutputType getSupportedOutputType() {
        return StreamOutputType.CONSOLE; // 使用控制台输出类型表示同步执行
    }
    
    @Override
    public boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        // 如果明确指定不使用流式输出
        if (Boolean.FALSE.equals(request.getStreaming())) {
            return true;
        }
        
        // 如果没有指定流式类型，且Accept头不包含流式类型
        if (request.getStreamOutputType() == null) {
            String accept = httpRequest.getHeader(StreamConstants.Headers.ACCEPT);
            if (accept == null ||
                (!accept.contains(StreamConstants.MediaTypes.TEXT_EVENT_STREAM) &&
                 !accept.contains(StreamConstants.MediaTypes.APPLICATION_STREAM_JSON))) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public Object processExecution(AppDetail appDetail, 
                                 UserDto user, 
                                 WorkflowExecutionRequest request,
                                 HttpServletRequest httpRequest, 
                                 HttpServletResponse httpResponse) {
        
        log.info("⚡ 开始同步执行工作流 - 应用ID: {}", appDetail.getId());
        
        LocalDateTime startTime = LocalDateTime.now();
        String executionId = generateExecutionId();
        
        try {
            // 调用原有的工作流执行逻辑
            Object result = appGenerateService.generate(
                    httpRequest, 
                    httpResponse, 
                    appDetail, 
                    user, 
                    request.getParams(), 
                    InvokeFrom.WEB_APP, 
                    true
            );
            
            LocalDateTime endTime = LocalDateTime.now();
            
            // 构建响应
            WorkflowExecutionResponse response = new WorkflowExecutionResponse()
                    .setExecutionId(executionId)
                    .setAppId(appDetail.getId())
                    .setStatus(StreamConstants.ExecutionStatus.COMPLETED)
                    .setOutputType(StreamOutputType.CONSOLE)
                    .setStartTime(startTime)
                    .setEndTime(endTime)
                    .setResult(result);
            
            log.info("✅ 同步工作流执行完成 - 应用ID: {}, 执行ID: {}", appDetail.getId(), executionId);
            
            // 如果请求需要包装为R对象
            if (WorkflowExecutionUtils.needWrapResult(httpRequest)) {
                return R.ok(response);
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("❌ 同步工作流执行失败 - 应用ID: {}, 执行ID: {}, 错误: {}", 
                    appDetail.getId(), executionId, e.getMessage(), e);
            
            LocalDateTime endTime = LocalDateTime.now();
            
            WorkflowExecutionResponse errorResponse = new WorkflowExecutionResponse()
                    .setExecutionId(executionId)
                    .setAppId(appDetail.getId())
                    .setStatus(StreamConstants.ExecutionStatus.FAILED)
                    .setOutputType(StreamOutputType.CONSOLE)
                    .setStartTime(startTime)
                    .setEndTime(endTime)
                    .setErrorMessage(e.getMessage());

            if (WorkflowExecutionUtils.needWrapResult(httpRequest)) {
                return R.failed(errorResponse, StreamConstants.Messages.EXECUTION_FAILED + ": " + e.getMessage());
            }
            
            return errorResponse;
        }
    }
    
    @Override
    public int getPriority() {
        return 1000; // 最低优先级，作为兜底处理器
    }
    
    /**
     * 生成执行ID
     */
    private String generateExecutionId() {
        return StreamConstants.Patterns.SYNC_ID_PREFIX + System.currentTimeMillis() + "_" +
               Integer.toHexString((int)(Math.random() * 0x10000));
    }
}