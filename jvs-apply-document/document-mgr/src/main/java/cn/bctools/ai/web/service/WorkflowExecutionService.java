package cn.bctools.ai.web.service;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.dto.WorkflowExecutionResponse;
import cn.bctools.ai.web.factory.WorkflowExecutionProcessorFactory;
import cn.bctools.ai.web.processor.WorkflowExecutionProcessor;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.stream.manager.StreamExecutionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;

/**
 * 工作流执行服务
 * 统一处理工作流执行的业务逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowExecutionService {
    
    private final WorkflowExecutionProcessorFactory processorFactory;
    
    private final StreamExecutionManager streamExecutionManager;
    
    /**
     * 执行工作流
     * 
     * @param request 执行请求
     * @param httpRequest HTTP请求
     * @param httpResponse HTTP响应
     * @return 执行结果
     */
    public Object executeWorkflow(@Valid WorkflowExecutionRequest request,
                                 HttpServletRequest httpRequest,
                                 HttpServletResponse httpResponse) {
        
        // 1. 获取应用详情
        AppDetail appDetail = getAppDetail();
        
        // 2. 验证应用类型
        validateAppType(appDetail);
        
        // 3. 获取当前用户
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        
        // 4. 记录执行开始
        log.info("🚀 开始执行工作流 - 应用ID: {}, 用户: {}, 流式类型: {}", 
                appDetail.getId(), currentUser.getRealName(), request.getStreamType());
        
        try {
            // 5. 选择合适的处理器
            WorkflowExecutionProcessor processor = processorFactory.getBestProcessor(request, httpRequest);
            
            // 6. 执行工作流
            Object result = processor.processExecution(appDetail, currentUser, request, httpRequest, httpResponse);
            
            log.info("✅ 工作流执行完成 - 应用ID: {}, 处理器: {}", 
                    appDetail.getId(), processor.getClass().getSimpleName());
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ 工作流执行失败 - 应用ID: {}, 错误: {}", appDetail.getId(), e.getMessage(), e);
            throw new RuntimeException("工作流执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取执行状态
     * 
     * @param executionId 执行ID
     * @return 执行状态
     */
    public R<WorkflowExecutionResponse> getExecutionStatus(String executionId) {
        StreamExecutionContext context = streamExecutionManager.getContext(executionId);
        if (context == null) {
            return R.failed("执行上下文不存在");
        }
        
        WorkflowExecutionResponse response = buildExecutionResponse(context);
        return R.ok(response);
    }
    
    /**
     * 取消执行
     * 
     * @param executionId 执行ID
     * @return 取消结果
     */
    public R<String> cancelExecution(String executionId) {
        try {
            streamExecutionManager.cancelExecution(executionId);
            log.info("🚫 工作流执行已取消 - 执行ID: {}", executionId);
            return R.ok("执行已取消");
        } catch (Exception e) {
            log.error("❌ 取消执行失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
            return R.failed("取消执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有活跃执行
     * 
     * @return 活跃执行列表
     */
    public R<Map<String, StreamExecutionContext>> getActiveExecutions() {
        Map<String, StreamExecutionContext> activeContexts = streamExecutionManager.getActiveContexts();
        return R.ok(activeContexts);
    }
    
    /**
     * 获取执行统计
     * 
     * @return 执行统计
     */
    public R<StreamExecutionManager.ExecutionStats> getExecutionStats() {
        StreamExecutionManager.ExecutionStats stats = streamExecutionManager.getExecutionStats();
        return R.ok(stats);
    }
    
    /**
     * 获取处理器信息
     * 
     * @return 处理器信息
     */
    public R<Map<String, Object>> getProcessorInfo() {
        Map<String, Object> info = processorFactory.getProcessorInfo();
        return R.ok(info);
    }
    
    /**
     * 推荐处理器
     * 
     * @param request 执行请求
     * @param httpRequest HTTP请求
     * @return 推荐结果
     */
    public R<WorkflowExecutionProcessorFactory.ProcessorRecommendation> recommendProcessor(
            WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        
        WorkflowExecutionProcessorFactory.ProcessorRecommendation recommendation = 
                processorFactory.recommendProcessor(request, httpRequest);
        
        return R.ok(recommendation);
    }
    
    /**
     * 获取应用详情
     */
    private AppDetail getAppDetail() {
        AppDetail appDetail = AppDetailContext.getObject();
        if (appDetail == null) {
            throw new IllegalStateException("应用详情不存在");
        }
        return appDetail;
    }
    
    /**
     * 验证应用类型
     */
    private void validateAppType(AppDetail appDetail) {
        if (!AppMode.WORKFLOW.value.equals(appDetail.getMode())) {
            throw new BusinessException("该应用不是工作流应用，当前模式: " + appDetail.getMode());
        }
    }
    
    /**
     * 构建执行响应
     */
    private WorkflowExecutionResponse buildExecutionResponse(StreamExecutionContext context) {
        WorkflowExecutionResponse response = new WorkflowExecutionResponse()
                .setExecutionId(context.getExecutionId())
                .setAppId(context.getAppId())
                .setOutputType(context.getOutputType())
                .setStartTime(context.getStartTime())
                .setMetadata(context.getAttributes());
        
        // 设置状态
        if (context.isStreamingEnabled()) {
            response.setStatus("running");
        } else {
            response.setStatus("completed");
        }
        
        // 设置统计信息
        if (context.getStats() != null) {
            WorkflowExecutionResponse.ExecutionStats stats = new WorkflowExecutionResponse.ExecutionStats()
                    .setTotalNodes(context.getStats().getTotalNodes())
                    .setExecutedNodes(context.getStats().getExecutedNodes())
                    .setFailedNodes(context.getStats().getFailedNodes())
                    .setProgress(context.getStats().getProgress())
                    .setTotalDuration(context.getStats().getTotalDuration())
                    .setAverageNodeDuration(context.getStats().getAverageNodeDuration());
            
            response.setStats(stats);
        }
        
        // 设置连接URL
        String baseUrl = getBaseUrl();
        response.setWebsocketUrl(baseUrl + "/ws/workflow-execution/" + context.getExecutionId())
                .setSseUrl(baseUrl + "/api/workflows/stream/" + context.getExecutionId());
        
        return response;
    }
    
    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        // 这里可以从配置中获取，或者从请求中构建
        return ""; // 返回空字符串，使用相对路径
    }
}