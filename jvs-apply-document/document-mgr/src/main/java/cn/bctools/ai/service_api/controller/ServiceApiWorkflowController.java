package cn.bctools.ai.service_api.controller;


import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.entity.vo.ChatMessageBodyVO;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.interfaces.GetAppInfo;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.app.service.CompletionService;
import cn.bctools.ai.app.service.WorkflowService;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.stream.constants.StreamConstants;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/v1")
@Api(tags = "访问api")
public class ServiceApiWorkflowController {

    AppGenerateService appGenerateService;

    @PostMapping(value = "/workflows/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Object workflowsRun(HttpServletRequest request, HttpServletResponse response, @RequestBody Map<String, Object> paramMap) {
        AppDetail appDetail = AppDetailContext.getObject();
        if (!appDetail.getMode().equals(AppMode.WORKFLOW.value) && !appDetail.getMode().equals(AppMode.ADVANCED_CHAT.value)) {
            return R.failed(StreamConstants.Messages.INVALID_APP_TYPE);
        }
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        //todo 运行
        return appGenerateService.generate(request, response, appDetail, currentUser, paramMap, InvokeFrom.SERVICE_API, true);

    }


}