package cn.bctools.ai.web.factory;

import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.processor.WorkflowExecutionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 工作流执行处理器工厂
 * 使用工厂模式管理不同类型的执行处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class WorkflowExecutionProcessorFactory {
    
    @Autowired
    private List<WorkflowExecutionProcessor> processors;
    
    /**
     * 按输出类型分组的处理器映射
     */
    private final Map<StreamOutputType, WorkflowExecutionProcessor> processorMap = new ConcurrentHashMap<>();
    
    /**
     * 按优先级排序的处理器列表
     */
    private List<WorkflowExecutionProcessor> sortedProcessors;
    
    @PostConstruct
    public void initProcessors() {
        log.info("🏭 初始化工作流执行处理器工厂...");
        
        // 按优先级排序处理器
        sortedProcessors = processors.stream()
                .sorted(Comparator.comparingInt(WorkflowExecutionProcessor::getPriority))
                .collect(Collectors.toList());
        
        // 构建处理器映射
        for (WorkflowExecutionProcessor processor : processors) {
            StreamOutputType outputType = processor.getSupportedOutputType();
            processorMap.put(outputType, processor);
            log.info("📝 注册工作流执行处理器: {} -> {}", 
                    outputType.getCode(), processor.getClass().getSimpleName());
        }
        
        log.info("✅ 工作流执行处理器工厂初始化完成，共注册 {} 个处理器", processors.size());
    }
    
    /**
     * 根据请求获取最佳处理器
     * 
     * @param request 执行请求
     * @param httpRequest HTTP请求
     * @return 最佳处理器
     */
    public WorkflowExecutionProcessor getBestProcessor(WorkflowExecutionRequest request, 
                                                      HttpServletRequest httpRequest) {
        
        // 1. 首先尝试根据明确指定的输出类型获取处理器
        StreamOutputType outputType = request.getStreamOutputType();
        if (outputType != null) {
            WorkflowExecutionProcessor processor = processorMap.get(outputType);
            if (processor != null && processor.supports(request, httpRequest)) {
                log.debug("🎯 根据指定类型选择处理器: {} -> {}", 
                        outputType.getCode(), processor.getClass().getSimpleName());
                return processor;
            }
        }
        
        // 2. 按优先级遍历所有处理器，找到第一个支持的
        for (WorkflowExecutionProcessor processor : sortedProcessors) {
            if (processor.supports(request, httpRequest)) {
                log.debug("🎯 根据优先级选择处理器: {} (优先级: {})", 
                        processor.getClass().getSimpleName(), processor.getPriority());
                return processor;
            }
        }
        
        // 3. 如果没有找到合适的处理器，返回默认的同步处理器
        WorkflowExecutionProcessor defaultProcessor = processorMap.get(StreamOutputType.CONSOLE);
        if (defaultProcessor != null) {
            log.warn("⚠️ 未找到合适的处理器，使用默认同步处理器");
            return defaultProcessor;
        }
        
        // 4. 最后的兜底方案
        if (!sortedProcessors.isEmpty()) {
            WorkflowExecutionProcessor fallbackProcessor = sortedProcessors.get(sortedProcessors.size() - 1);
            log.warn("⚠️ 使用兜底处理器: {}", fallbackProcessor.getClass().getSimpleName());
            return fallbackProcessor;
        }
        
        throw new IllegalStateException("没有可用的工作流执行处理器");
    }
    
    /**
     * 根据输出类型获取处理器
     * 
     * @param outputType 输出类型
     * @return 对应的处理器
     */
    public WorkflowExecutionProcessor getProcessor(StreamOutputType outputType) {
        WorkflowExecutionProcessor processor = processorMap.get(outputType);
        if (processor == null) {
            log.warn("⚠️ 未找到输出类型 {} 对应的处理器", outputType.getCode());
            return processorMap.get(StreamOutputType.CONSOLE); // 返回默认处理器
        }
        return processor;
    }
    
    /**
     * 获取所有支持的输出类型
     * 
     * @return 支持的输出类型列表
     */
    public List<StreamOutputType> getSupportedOutputTypes() {
        return processors.stream()
                .map(WorkflowExecutionProcessor::getSupportedOutputType)
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有处理器信息
     * 
     * @return 处理器信息映射
     */
    public Map<String, Object> getProcessorInfo() {
        Map<String, Object> info = new ConcurrentHashMap<>();
        
        info.put("totalProcessors", processors.size());
        info.put("supportedOutputTypes", getSupportedOutputTypes());
        
        Map<String, Object> processorDetails = new ConcurrentHashMap<>();
        for (WorkflowExecutionProcessor processor : processors) {
            Map<String, Object> detail = new ConcurrentHashMap<>();
            detail.put("className", processor.getClass().getSimpleName());
            detail.put("priority", processor.getPriority());
            detail.put("supportedOutputType", processor.getSupportedOutputType().getCode());
            
            processorDetails.put(processor.getClass().getSimpleName(), detail);
        }
        info.put("processors", processorDetails);
        
        return info;
    }
    
    /**
     * 检查是否支持指定的输出类型
     * 
     * @param outputType 输出类型
     * @return 是否支持
     */
    public boolean isSupported(StreamOutputType outputType) {
        return processorMap.containsKey(outputType);
    }
    
    /**
     * 智能推荐处理器
     * 根据请求特征推荐最合适的处理器
     * 
     * @param request 执行请求
     * @param httpRequest HTTP请求
     * @return 推荐的处理器和推荐理由
     */
    public ProcessorRecommendation recommendProcessor(WorkflowExecutionRequest request, 
                                                    HttpServletRequest httpRequest) {
        
        WorkflowExecutionProcessor processor = getBestProcessor(request, httpRequest);
        String reason = buildRecommendationReason(request, httpRequest, processor);
        
        return new ProcessorRecommendation(processor, reason);
    }
    
    /**
     * 构建推荐理由
     */
    private String buildRecommendationReason(WorkflowExecutionRequest request, 
                                           HttpServletRequest httpRequest,
                                           WorkflowExecutionProcessor processor) {
        StringBuilder reason = new StringBuilder();
        
        if (request.getStreamOutputType() != null) {
            reason.append("明确指定输出类型: ").append(request.getStreamOutputType().getCode());
        } else {
            String accept = httpRequest.getHeader("Accept");
            if (accept != null) {
                if (accept.contains("text/event-stream")) {
                    reason.append("Accept头包含SSE类型");
                } else if (accept.contains("application/stream+json")) {
                    reason.append("Accept头包含Flux类型");
                } else {
                    reason.append("默认同步执行");
                }
            } else {
                reason.append("无特殊要求，使用默认处理器");
            }
        }
        
        reason.append("，选择处理器: ").append(processor.getClass().getSimpleName());
        
        return reason.toString();
    }
    
    /**
     * 处理器推荐结果
     */
    public static class ProcessorRecommendation {
        private final WorkflowExecutionProcessor processor;
        private final String reason;
        
        public ProcessorRecommendation(WorkflowExecutionProcessor processor, String reason) {
            this.processor = processor;
            this.reason = reason;
        }
        
        public WorkflowExecutionProcessor getProcessor() {
            return processor;
        }
        
        public String getReason() {
            return reason;
        }
    }
}
