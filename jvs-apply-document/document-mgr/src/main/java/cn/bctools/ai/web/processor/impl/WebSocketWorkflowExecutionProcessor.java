package cn.bctools.ai.web.processor.impl;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.dto.WorkflowExecutionResponse;
import cn.bctools.ai.web.processor.AbstractWorkflowExecutionProcessor;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.stream.enums.StreamOutputType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * WebSocket工作流执行处理器
 * WebSocket需要通过专门的端点连接，这里返回连接信息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WebSocketWorkflowExecutionProcessor extends AbstractWorkflowExecutionProcessor {

    @Override
    public StreamOutputType getSupportedOutputType() {
        return StreamOutputType.WEBSOCKET;
    }

    @Override
    public boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        // 检查请求参数中的流式类型
        if (request.getStreamOutputType() == StreamOutputType.WEBSOCKET) {
            return true;
        }

        // 检查自定义头
        String streamType = httpRequest.getHeader(StreamConstants.Headers.X_STREAM_TYPE);
        if (StreamConstants.StreamTypeCodes.WEBSOCKET.equalsIgnoreCase(streamType)) {
            return true;
        }

        // 检查请求路径
        String requestUri = httpRequest.getRequestURI();
        return requestUri != null && (requestUri.contains(StreamConstants.Paths.WEBSOCKET_PREFIX) ||
                requestUri.contains(StreamConstants.StreamTypeCodes.WEBSOCKET));
    }

    @Override
    public Object processExecution(AppDetail appDetail,
                                   UserDto user,
                                   WorkflowExecutionRequest request,
                                   HttpServletRequest httpRequest,
                                   HttpServletResponse httpResponse) {

        log.info("🔌 WebSocket工作流执行请求 - 应用ID: {}", appDetail.getId());

        try {
            // 准备执行上下文
            StreamExecutionContext context = prepareExecution(appDetail, user, request, httpRequest, httpResponse);

            // 构建WebSocket连接信息响应
            WorkflowExecutionResponse response = new WorkflowExecutionResponse()
                    .setExecutionId(context.getExecutionId())
                    .setAppId(appDetail.getId())
                    .setStatus(StreamConstants.ExecutionStatus.PENDING)
                    .setOutputType(StreamOutputType.WEBSOCKET)
                    .setStartTime(context.getStartTime())
                    .setWebsocketUrl(StreamConstants.Paths.WEBSOCKET_PREFIX + StreamConstants.Paths.WEBSOCKET_WORKFLOW + "/" + context.getExecutionId())
                    .setSseUrl(StreamConstants.Paths.API_PREFIX + StreamConstants.Paths.WORKFLOWS_STREAM + "/" + context.getExecutionId());

            // 设置连接指引信息
            response.getMetadata().put(StreamConstants.AttributeNames.CONNECTION_GUIDE, StreamConstants.Messages.WEBSOCKET_CONNECTION_GUIDE);
            response.getMetadata().put(StreamConstants.AttributeNames.PROTOCOL, "WebSocket");
            response.getMetadata().put(StreamConstants.AttributeNames.MESSAGE_FORMAT, "JSON");

            log.info("✅ WebSocket连接信息已生成 - 执行ID: {}, WebSocket URL: {}",
                    context.getExecutionId(), response.getWebsocketUrl());

            return R.ok(response, StreamConstants.Messages.WEBSOCKET_ENDPOINT_MESSAGE + ": " + response.getWebsocketUrl());

        } catch (Exception e) {
            log.error("❌ WebSocket处理失败 - 应用ID: {}, 错误: {}", appDetail.getId(), e.getMessage(), e);
            return R.failed("WebSocket处理失败: " + e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        // 中等优先级
        return 30;
    }

    @Override
    protected void onBeforeExecution(StreamExecutionContext context) {
        super.onBeforeExecution(context);
        log.info("🔌 WebSocket执行上下文已准备 - 执行ID: {}", context.getExecutionId());
    }
}