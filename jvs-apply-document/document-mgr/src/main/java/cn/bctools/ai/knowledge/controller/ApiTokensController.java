package cn.bctools.ai.knowledge.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;

/**
 * <p>
 * api token 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api-tokens")
@Api(tags = "api token")
public class ApiTokensController {

}