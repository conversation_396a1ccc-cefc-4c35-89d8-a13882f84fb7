package cn.bctools.ai.web.utils;

import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.enums.StreamOutputType;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 工作流执行工具类
 * 提供通用的工具方法
 *
 * <AUTHOR>
 */
public class WorkflowExecutionUtils {

    /**
     * 移动端用户代理正则模式
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile(StreamConstants.Patterns.MOBILE_USER_AGENT);

    /**
     * API路径正则模式
     */
    private static final Pattern API_PATH_PATTERN = Pattern.compile(StreamConstants.Patterns.API_PATH);

    /**
     * 从HTTP请求中智能推断流式输出类型
     *
     * @param request HTTP请求
     * @return 推断的输出类型
     */
    public static StreamOutputType inferOutputTypeFromRequest(HttpServletRequest request) {
        // 1. 检查自定义头
        String streamType = request.getHeader(StreamConstants.Headers.X_STREAM_TYPE);
        if (streamType != null) {
            try {
                return StreamOutputType.fromCode(streamType);
            } catch (IllegalArgumentException e) {
                // 忽略无效的类型
            }
        }

        // 2. 检查Accept头
        String accept = request.getHeader(StreamConstants.Headers.ACCEPT);
        if (accept != null) {
            if (accept.contains(StreamConstants.MediaTypes.TEXT_EVENT_STREAM)) {
                return StreamOutputType.SSE;
            }
            if (accept.contains(StreamConstants.MediaTypes.APPLICATION_STREAM_JSON)) {
                return StreamOutputType.FLUX;
            }
        }

        // 3. 检查Content-Type
        String contentType = request.getContentType();
        if (contentType != null) {
            if (contentType.contains(StreamConstants.MediaTypes.APPLICATION_STREAM_JSON)) {
                return StreamOutputType.FLUX;
            }
        }

        // 4. 检查请求路径
        String requestUri = request.getRequestURI();
        if (requestUri != null) {
            if (requestUri.contains(StreamConstants.StreamTypeCodes.FLUX)) {
                return StreamOutputType.FLUX;
            }
            if (requestUri.contains(StreamConstants.StreamTypeCodes.SSE) || requestUri.contains("stream")) {
                return StreamOutputType.SSE;
            }
            if (requestUri.contains(StreamConstants.Paths.WEBSOCKET_PREFIX) ||
                    requestUri.contains(StreamConstants.StreamTypeCodes.WEBSOCKET)) {
                return StreamOutputType.WEBSOCKET;
            }
        }

        // 5. 默认返回控制台输出
        return StreamOutputType.CONSOLE;
    }

    /**
     * 检查请求是否支持流式输出
     *
     * @param request HTTP请求
     * @return 是否支持流式输出
     */
    public static boolean isStreamingSupported(HttpServletRequest request) {
        StreamOutputType outputType = inferOutputTypeFromRequest(request);
        return outputType != StreamOutputType.CONSOLE;
    }

    /**
     * 从Map参数构建WorkflowExecutionRequest
     *
     * @param paramMap 参数映射
     * @param request  HTTP请求
     * @return 工作流执行请求
     */
    public static WorkflowExecutionRequest buildRequestFromMap(Map<String, Object> paramMap,
                                                               HttpServletRequest request) {
        WorkflowExecutionRequest executionRequest = new WorkflowExecutionRequest();
        executionRequest.setParams(paramMap);

        // 从HTTP请求推断流式类型
        StreamOutputType outputType = inferOutputTypeFromRequest(request);
        if (outputType != StreamOutputType.CONSOLE) {
            executionRequest.setStreamType(outputType.getCode());
            executionRequest.setStreaming(true);
        } else {
            executionRequest.setStreaming(false);
        }

        // 设置超时时间
        String timeoutHeader = request.getHeader(StreamConstants.Headers.X_TIMEOUT_SECONDS);
        if (timeoutHeader != null) {
            try {
                executionRequest.setTimeoutSeconds(Integer.parseInt(timeoutHeader));
            } catch (NumberFormatException e) {
                // 忽略无效的超时时间
            }
        }

        // 设置异步标志
        String asyncHeader = request.getHeader(StreamConstants.Headers.X_ASYNC);
        if ("true".equalsIgnoreCase(asyncHeader)) {
            executionRequest.setAsync(true);
        }

        // 设置回调URL
        String callbackUrl = request.getHeader(StreamConstants.Headers.X_CALLBACK_URL);
        if (callbackUrl != null) {
            executionRequest.setCallbackUrl(callbackUrl);
        }

        return executionRequest;
    }

    /**
     * 验证工作流执行请求
     *
     * @param request 执行请求
     * @throws IllegalArgumentException 如果请求无效
     */
    public static void validateExecutionRequest(WorkflowExecutionRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("执行请求不能为空");
        }

        if (request.getParams() == null || request.getParams().isEmpty()) {
            throw new IllegalArgumentException("执行参数不能为空");
        }

        if (request.getTimeoutSeconds() != null && request.getTimeoutSeconds() <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }

        if (request.getTimeoutSeconds() != null && request.getTimeoutSeconds() > StreamConstants.Defaults.MAX_TIMEOUT_SECONDS) {
            throw new IllegalArgumentException("超时时间不能超过" + (StreamConstants.Defaults.MAX_TIMEOUT_SECONDS / 60) + "分钟");
        }

        if (Boolean.TRUE.equals(request.getAsync()) && request.getCallbackUrl() == null) {
            throw new IllegalArgumentException("异步执行必须提供回调URL");
        }
    }

    /**
     * 生成执行ID
     *
     * @param prefix 前缀
     * @return 执行ID
     */
    public static String generateExecutionId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" +
                Integer.toHexString((int) (Math.random() * 0x10000));
    }

    /**
     * 生成执行ID（默认前缀）
     *
     * @return 执行ID
     */
    public static String generateExecutionId() {
        return generateExecutionId(StreamConstants.Patterns.EXECUTION_ID_PREFIX.replace("_", ""));
    }

    /**
     * 检查是否为API请求
     *
     * @param request HTTP请求
     * @return 是否为API请求
     */
    public static boolean isApiRequest(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        return requestUri != null && API_PATH_PATTERN.matcher(requestUri).matches();
    }

    /**
     * 检查是否需要包装结果为R对象
     *
     * @param request HTTP请求
     * @return 是否需要包装
     */
    public static boolean needWrapResult(HttpServletRequest request) {
        // API请求通常需要包装
        if (isApiRequest(request)) {
            return true;
        }

        // 检查Accept头
        String accept = request.getHeader(StreamConstants.Headers.ACCEPT);
        return accept != null && accept.contains(StreamConstants.MediaTypes.APPLICATION_JSON);
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader(StreamConstants.Headers.X_FORWARDED_FOR);
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !StreamConstants.Defaults.UNKNOWN.equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader(StreamConstants.Headers.X_REAL_IP);
        if (xRealIp != null && !xRealIp.isEmpty() && !StreamConstants.Defaults.UNKNOWN.equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 获取用户代理
     *
     * @param request HTTP请求
     * @return 用户代理字符串
     */
    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader(StreamConstants.Headers.USER_AGENT);
    }

    /**
     * 检查是否为移动端请求
     *
     * @param request HTTP请求
     * @return 是否为移动端
     */
    public static boolean isMobileRequest(HttpServletRequest request) {
        String userAgent = getUserAgent(request);
        if (userAgent == null) {
            return false;
        }

        return MOBILE_PATTERN.matcher(userAgent).matches();
    }
}