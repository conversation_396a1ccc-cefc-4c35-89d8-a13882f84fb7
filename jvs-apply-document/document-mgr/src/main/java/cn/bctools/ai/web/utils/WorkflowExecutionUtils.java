package cn.bctools.ai.web.utils;

import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 工作流执行工具类
 * 提供通用的工具方法
 * 
 * <AUTHOR>
 */
public class WorkflowExecutionUtils {
    
    /**
     * 从HTTP请求中智能推断流式输出类型
     * 
     * @param request HTTP请求
     * @return 推断的输出类型
     */
    public static StreamOutputType inferOutputTypeFromRequest(HttpServletRequest request) {
        // 1. 检查自定义头
        String streamType = request.getHeader("X-Stream-Type");
        if (streamType != null) {
            try {
                return StreamOutputType.fromCode(streamType);
            } catch (IllegalArgumentException e) {
                // 忽略无效的类型
            }
        }
        
        // 2. 检查Accept头
        String accept = request.getHeader("Accept");
        if (accept != null) {
            if (accept.contains(MediaType.TEXT_EVENT_STREAM_VALUE)) {
                return StreamOutputType.SSE;
            }
            if (accept.contains(MediaType.APPLICATION_STREAM_JSON_VALUE)) {
                return StreamOutputType.FLUX;
            }
        }
        
        // 3. 检查Content-Type
        String contentType = request.getContentType();
        if (contentType != null) {
            if (contentType.contains(MediaType.APPLICATION_STREAM_JSON_VALUE)) {
                return StreamOutputType.FLUX;
            }
        }
        
        // 4. 检查请求路径
        String requestURI = request.getRequestURI();
        if (requestURI != null) {
            if (requestURI.contains("/flux")) {
                return StreamOutputType.FLUX;
            }
            if (requestURI.contains("/sse") || requestURI.contains("/stream")) {
                return StreamOutputType.SSE;
            }
            if (requestURI.contains("/ws") || requestURI.contains("/websocket")) {
                return StreamOutputType.WEBSOCKET;
            }
        }
        
        // 5. 默认返回控制台输出
        return StreamOutputType.CONSOLE;
    }
    
    /**
     * 检查请求是否支持流式输出
     * 
     * @param request HTTP请求
     * @return 是否支持流式输出
     */
    public static boolean isStreamingSupported(HttpServletRequest request) {
        StreamOutputType outputType = inferOutputTypeFromRequest(request);
        return outputType != StreamOutputType.CONSOLE;
    }
    
    /**
     * 从Map参数构建WorkflowExecutionRequest
     * 
     * @param paramMap 参数映射
     * @param request HTTP请求
     * @return 工作流执行请求
     */
    public static WorkflowExecutionRequest buildRequestFromMap(Map<String, Object> paramMap, 
                                                              HttpServletRequest request) {
        WorkflowExecutionRequest executionRequest = new WorkflowExecutionRequest();
        executionRequest.setParams(paramMap);
        
        // 从HTTP请求推断流式类型
        StreamOutputType outputType = inferOutputTypeFromRequest(request);
        if (outputType != StreamOutputType.CONSOLE) {
            executionRequest.setStreamType(outputType.getCode());
            executionRequest.setStreaming(true);
        } else {
            executionRequest.setStreaming(false);
        }
        
        // 设置超时时间
        String timeoutHeader = request.getHeader("X-Timeout-Seconds");
        if (timeoutHeader != null) {
            try {
                executionRequest.setTimeoutSeconds(Integer.parseInt(timeoutHeader));
            } catch (NumberFormatException e) {
                // 忽略无效的超时时间
            }
        }
        
        // 设置异步标志
        String asyncHeader = request.getHeader("X-Async");
        if ("true".equalsIgnoreCase(asyncHeader)) {
            executionRequest.setAsync(true);
        }
        
        // 设置回调URL
        String callbackUrl = request.getHeader("X-Callback-Url");
        if (callbackUrl != null) {
            executionRequest.setCallbackUrl(callbackUrl);
        }
        
        return executionRequest;
    }
    
    /**
     * 验证工作流执行请求
     * 
     * @param request 执行请求
     * @throws IllegalArgumentException 如果请求无效
     */
    public static void validateExecutionRequest(WorkflowExecutionRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("执行请求不能为空");
        }
        
        if (request.getParams() == null || request.getParams().isEmpty()) {
            throw new IllegalArgumentException("执行参数不能为空");
        }
        
        if (request.getTimeoutSeconds() != null && request.getTimeoutSeconds() <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }
        
        if (request.getTimeoutSeconds() != null && request.getTimeoutSeconds() > 3600) {
            throw new IllegalArgumentException("超时时间不能超过1小时");
        }
        
        if (Boolean.TRUE.equals(request.getAsync()) && request.getCallbackUrl() == null) {
            throw new IllegalArgumentException("异步执行必须提供回调URL");
        }
    }
    
    /**
     * 生成执行ID
     * 
     * @param prefix 前缀
     * @return 执行ID
     */
    public static String generateExecutionId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int)(Math.random() * 0x10000));
    }
    
    /**
     * 生成执行ID（默认前缀）
     * 
     * @return 执行ID
     */
    public static String generateExecutionId() {
        return generateExecutionId("exec");
    }
    
    /**
     * 检查是否为API请求
     * 
     * @param request HTTP请求
     * @return 是否为API请求
     */
    public static boolean isApiRequest(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        return requestURI != null && requestURI.startsWith("/api/");
    }
    
    /**
     * 检查是否需要包装结果为R对象
     * 
     * @param request HTTP请求
     * @return 是否需要包装
     */
    public static boolean needWrapResult(HttpServletRequest request) {
        // API请求通常需要包装
        if (isApiRequest(request)) {
            return true;
        }
        
        // 检查Accept头
        String accept = request.getHeader("Accept");
        if (accept != null && accept.contains("application/json")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 获取用户代理
     * 
     * @param request HTTP请求
     * @return 用户代理字符串
     */
    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }
    
    /**
     * 检查是否为移动端请求
     * 
     * @param request HTTP请求
     * @return 是否为移动端
     */
    public static boolean isMobileRequest(HttpServletRequest request) {
        String userAgent = getUserAgent(request);
        if (userAgent == null) {
            return false;
        }
        
        userAgent = userAgent.toLowerCase();
        return userAgent.contains("mobile") || 
               userAgent.contains("android") || 
               userAgent.contains("iphone") || 
               userAgent.contains("ipad");
    }
}
