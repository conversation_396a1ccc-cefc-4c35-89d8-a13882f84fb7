package cn.bctools.ai.app.controller;


import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.entity.data.AppModelConfigs;
import cn.bctools.ai.app.entity.data.Apps;
import cn.bctools.ai.app.entity.data.Sites;
import cn.bctools.ai.app.entity.dto.ModelConfigDTO;
import cn.bctools.ai.app.entity.dto.SiteDTO;
import cn.bctools.ai.app.entity.vo.*;
import cn.bctools.ai.app.interfaces.GetAppInfo;
import cn.bctools.ai.app.service.AppsService;
import cn.bctools.common.utils.R;
import cn.bctools.model.entity.vo.VoiceVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import lombok.AllArgsConstructor;
import io.swagger.annotations.Api;

import java.util.List;

/**
 * <p>
 * 应用 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/apps")
@Api(tags = "应用")
public class AppsController {

    AppsService appsService;


    @GetMapping
    @ApiOperation("获取应用列表")
    public R<IPage<AppListDetailVO>> getAppList(Page<AppListDetailVO> page, AppListVO params) {
        IPage<AppListDetailVO> appContext = appsService.getAppList(page, params);
        return R.ok(appContext);
    }


    @GetAppInfo(value = "appId")
    @GetMapping("/{appId}")
    @ApiOperation("获取应用信息")
    public R<AppDetail> getAppInfo() {
        return R.ok(AppDetailContext.getObject());
    }


    @PostMapping("/create")
    @ApiOperation("创建应用")
    public R<Apps> create(@RequestBody @Validated AppInfoVO app) {
        return R.ok(appsService.create(app));
    }

    @PutMapping("/{appId}")
    @ApiOperation("更新应用信息")
    public R<Apps> edit(@PathVariable("appId") String appId, @RequestBody @Validated AppInfoVO app) {
        return R.ok(appsService.edit(appId, app));
    }

    /**
     * 复制应用
     */
    @PostMapping("/copy/{appId}")
    @ApiOperation("复制应用")
    public R<Apps> copy(@PathVariable("appId") String appId, @RequestBody @Validated CopyAppVO newApp) {
        return R.ok(appsService.copy(appId, newApp));
    }


    /**
     * 批量删除
     */
    @DeleteMapping("/{appId}")
    @ApiOperation("删除应用")
    public R<Boolean> delete(@PathVariable("appId") String appId) {
        return R.ok(appsService.delete(appId));
    }


    @GetAppInfo(value = "appId")
    @PostMapping("/{appId}/model-config")
    @ApiOperation("更新配置")
    public R updateConfig(@PathVariable("appId") String appId, @RequestBody ModelConfigDTO param) {
        appsService.addNewConfigVersion(appId, param);
        return R.ok();
    }

    @GetAppInfo(value = "appId")
    @PostMapping("/{appId}/site")
    @ApiOperation("编辑网站设置")
    public R<Sites> updateSite(@PathVariable("appId") String appId, @RequestBody SiteDTO param) {
        return R.ok(appsService.updateSite(appId, param));
    }


    @GetAppInfo(value = "appId")
    @GetMapping("/{appId}/versions")
    @ApiOperation("版本列表")
    public R<IPage<VersionInfo>> versions(@PathVariable("appId") String appId, Page<AppModelConfigs> page) {
        return R.ok(appsService.getVersions(page, appId));
    }

    @GetAppInfo(value = "appId")
    @GetMapping("/{appId}/model-config/{versionId}")
    @ApiOperation("获取配置")
    public R<ModelConfigDTO> getConfig(@PathVariable("appId") String appId, @PathVariable("versionId") String versionId) {
        return R.ok(appsService.getVersion(appId, versionId));
    }

    @GetAppInfo(value = "appId")
    @PutMapping("/{appId}/model-config/{versionId}")
    @ApiOperation("恢复到指定版本")
    public R<Boolean> recover(@PathVariable("appId") String appId, @PathVariable("versionId") String versionId) {
        appsService.recoverConfig(appId, versionId);
        return R.ok();
    }


    @GetAppInfo(value = "appId")
    @PostMapping("/{appId}/site-enable")
    @ApiOperation("修改网站运行配置")
    public R<Boolean> updateSiteEnable(@PathVariable("appId") String appId, @RequestBody EnableVO param) {
        return R.ok(appsService.updateSiteEnable(appId, param.getEnable()));
    }

    @GetAppInfo(value = "appId")
    @PostMapping("/{appId}/api-enable")
    @ApiOperation("修改api运行配置")
    public R<Boolean> updateApiEnable(@PathVariable("appId") String appId, @RequestBody EnableVO param) {
        return R.ok(appsService.updateApiEnable(appId, param.getEnable()));
    }

    @GetAppInfo(value = "appId")
    @PostMapping("/{appId}/site/access-token-reset")
    @ApiOperation("重新生成访问地址")
    public R<Sites> resetAccessToken(@PathVariable("appId") String appId) {
        return R.ok(appsService.resetAccessToken(appId));
    }

    @GetAppInfo(value = "appId")
    @GetMapping("/{appId}/text-to-audio/voices")
    @ApiOperation("获取音色列表")
    public R<List<VoiceVO>> getVoices(@PathVariable("appId") String appId, @RequestParam(value = "language") String language) {
        return R.ok(appsService.getVoices(language));
    }


}
