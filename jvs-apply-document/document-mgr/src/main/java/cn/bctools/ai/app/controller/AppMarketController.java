package cn.bctools.ai.app.controller;

import cn.bctools.ai.app.entity.vo.CopyAppVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarketFilter;
import cn.bctools.ai.app.service.AppMarketService;
import cn.bctools.common.utils.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 应用市场表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/app-market")
@Api(tags = "应用市场表")
public class AppMarketController {

    AppMarketService appMarketService;

    @GetMapping
    @ApiOperation("应用列表")
    public R<IPage<AppMarkerListVO>> list(Page<AppMarkerListVO> page, AppMarketFilter params) {
        params.setStatus(true);
        return R.ok(appMarketService.getList(page, params));
    }

    @PostMapping("/quote")
    @ApiOperation("引用")
    public R quote(@RequestBody CopyAppVO params) {
        appMarketService.quote(params);
        return R.ok();
    }

}
