package cn.bctools.ai.web.dto;

import cn.bctools.stream.enums.StreamOutputType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 工作流执行请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("工作流执行请求")
public class WorkflowExecutionRequest {
    
    @ApiModelProperty(value = "输入参数", required = true)
    @NotNull(message = "输入参数不能为空")
    private Map<String, Object> params;
    
    @ApiModelProperty(value = "流式输出类型", example = "flux")
    private String streamType;
    
    @ApiModelProperty(value = "是否启用流式输出", example = "true")
    private Boolean streaming = true;
    
    @ApiModelProperty(value = "超时时间（秒）", example = "300")
    private Integer timeoutSeconds = 300;
    
    @ApiModelProperty(value = "是否异步执行", example = "false")
    private Boolean async = false;
    
    @ApiModelProperty(value = "回调URL（异步执行时使用）")
    private String callbackUrl;
    
    @ApiModelProperty(value = "扩展属性")
    private Map<String, Object> metadata;
    
    /**
     * 获取流式输出类型枚举
     */
    public StreamOutputType getStreamOutputType() {
        if (streamType == null) {
            return null;
        }
        try {
            return StreamOutputType.fromCode(streamType);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}