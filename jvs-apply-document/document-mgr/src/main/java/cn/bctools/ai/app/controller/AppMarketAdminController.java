package cn.bctools.ai.app.controller;

import cn.bctools.ai.app.entity.vo.app_market.AppMarkerEditVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO;
import cn.bctools.ai.app.entity.vo.app_market.AppMarketFilter;
import cn.bctools.ai.app.entity.vo.app_market.DownAppVO;
import cn.bctools.ai.app.service.AppMarketService;
import cn.bctools.common.utils.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 应用市场管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/app-market-admin")
@Api(tags = "应用市场管理")
public class AppMarketAdminController {

    AppMarketService appMarketService;

    @GetMapping
    @ApiOperation("应用列表")
    public R<IPage<AppMarkerListVO>> list(Page<AppMarkerListVO> page, AppMarketFilter params) {
        return R.ok(appMarketService.getList(page, params));
    }

    @PostMapping("/edit-app/{appId}/{publicId}")
    @ApiOperation("编辑应用")
    public R editApp(@PathVariable String appId, @PathVariable String publicId, @RequestBody AppMarkerEditVO params) {
        appMarketService.editApp(appId, publicId, params);
        return R.ok();
    }

    @PostMapping("/up-app/{appId}/{publicId}")
    @ApiOperation("上架应用")
    public R upApp(@PathVariable String appId, @PathVariable String publicId, @RequestBody AppMarkerEditVO params) {
        appMarketService.upApp(appId, publicId, params);
        return R.ok();
    }

    @PostMapping("/down-app")
    @ApiOperation("下架应用")
    public R updateStatus(@RequestBody DownAppVO params) {
        appMarketService.downApp(params);
        return R.ok();
    }

    @PutMapping("/update-allow-guest/{appId}/{publicId}/{allowGuest}")
    @ApiOperation("更新应用是否允许游客访问")
    public R updateAllowGuest(@PathVariable String appId, @PathVariable String publicId, @PathVariable Boolean allowGuest) {
        appMarketService.updateAllowGuest(appId, publicId, allowGuest);
        return R.ok();
    }


}
