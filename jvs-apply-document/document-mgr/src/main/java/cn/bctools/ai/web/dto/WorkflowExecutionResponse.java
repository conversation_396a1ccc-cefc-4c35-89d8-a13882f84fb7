package cn.bctools.ai.web.dto;

import cn.bctools.ai.stream.enums.StreamOutputType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 工作流执行响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("工作流执行响应")
public class WorkflowExecutionResponse {
    
    @ApiModelProperty("执行ID")
    private String executionId;
    
    @ApiModelProperty("应用ID")
    private String appId;
    
    @ApiModelProperty("执行状态")
    private String status;
    
    @ApiModelProperty("流式输出类型")
    private StreamOutputType outputType;
    
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;
    
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;
    
    @ApiModelProperty("执行结果")
    private Object result;
    
    @ApiModelProperty("错误信息")
    private String errorMessage;
    
    @ApiModelProperty("WebSocket连接URL")
    private String websocketUrl;
    
    @ApiModelProperty("SSE连接URL")
    private String sseUrl;
    
    @ApiModelProperty("执行统计")
    private ExecutionStats stats;
    
    @ApiModelProperty("扩展信息")
    private Map<String, Object> metadata;
    
    /**
     * 执行统计信息
     */
    @Data
    @Accessors(chain = true)
    @ApiModel("执行统计")
    public static class ExecutionStats {
        @ApiModelProperty("总节点数")
        private Integer totalNodes;
        
        @ApiModelProperty("已执行节点数")
        private Integer executedNodes;
        
        @ApiModelProperty("失败节点数")
        private Integer failedNodes;
        
        @ApiModelProperty("执行进度百分比")
        private Integer progress;
        
        @ApiModelProperty("总执行时间（毫秒）")
        private Long totalDuration;
        
        @ApiModelProperty("平均节点执行时间（毫秒）")
        private Long averageNodeDuration;
    }
}
