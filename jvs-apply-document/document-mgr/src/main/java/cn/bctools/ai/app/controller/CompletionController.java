package cn.bctools.ai.app.controller;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.entity.vo.ChatMessageBodyVO;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.interfaces.GetAppInfo;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.app.service.AppModelConfigsService;
import cn.bctools.ai.app.service.CompletionService;
import cn.bctools.ai.app.service.ConversationMessagesService;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/apps/{appId}")
@Api(tags = "应用")
public class CompletionController {

    AppModelConfigsService appModelConfigsService;

    CompletionService completionService;

    ConversationMessagesService conversationMessagesService;

    AppGenerateService appGenerateService;


    @GetAppInfo(mode = {AppMode.CHAT, AppMode.COMPLETION})
    @PostMapping(value = "/chat-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("chat聊天助手-调试与预览")
    public Object chatMessage(@RequestBody ChatMessageBodyVO param) {

        if (param.getResponse_mode().equals("streaming")) {
            return completionService.generate(param, InvokeFrom.DEBUGGER);
        } else {
            throw new RuntimeException("请选择流式响应");
//            return completionService.completion(param);
        }

    }

    @GetAppInfo(mode = {AppMode.COMPLETION})
    @PostMapping(value = "/complete-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("completion文本生成应用-调试与预览")
    public Object completeMessage(HttpServletRequest request, HttpServletResponse response, @RequestBody ChatMessageBodyVO param) {
        AppDetail appDetail = AppDetailContext.getObject();
        UserDto userDto = UserCurrentUtils.getCurrentUser();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("query", param.getQuery());
        paramMap.put("inputs", param.getInputs());
        paramMap.put("model_config", param.getModel_config());
        return appGenerateService.generate(request, response, appDetail, userDto, paramMap, InvokeFrom.DEBUGGER, true);
//        if (param.getResponse_mode().equals("streaming")) {
//            return completionService.generate(param, InvokeFrom.DEBUGGER);
//        } else {
//            throw new RuntimeException("请选择流式响应");
////            return completionService.completion(param);
//        }

    }

}
