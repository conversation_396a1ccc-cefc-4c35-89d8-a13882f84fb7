package cn.bctools.ai.app.controller;


import cn.bctools.ai.app.entity.vo.app_public.AppApplyVO;
import cn.bctools.ai.app.entity.vo.app_public.AppPublicApplyDetailVO;
import cn.bctools.ai.app.entity.vo.app_public.AppPublicApplyListVO;
import cn.bctools.ai.app.entity.vo.app_public.AppPublicVO;
import cn.bctools.ai.app.service.AppPublicService;
import cn.bctools.common.utils.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 应用公开表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/app-public")
@Api(tags = "应用公开表")
public class AppPublicController {

    AppPublicService appPublicService;


    @GetMapping("/{appId}")
    @ApiOperation("查询上架应用市场版本")
    public R<AppPublicVO> appPublic(@PathVariable String appId) {
        return R.ok(appPublicService.appPublic(appId));
    }

    @GetMapping("/apply-list")
    @ApiOperation("我的申请")
    public R<IPage<AppPublicApplyDetailVO>> applyList(Page<AppPublicApplyDetailVO> page, AppPublicApplyListVO params) {
        return R.ok(appPublicService.getMyApplyList(page, params));
    }


    @PostMapping
    @Transactional
    @ApiOperation("申请上架")
    public R apply(@RequestBody AppApplyVO params) {
        appPublicService.apply(params);
        return R.ok(null, "发布成功");
    }

    @PostMapping("/down/{appId}")
    @Transactional
    @ApiOperation("申请下架")
    public R down(@PathVariable String appId) {
        appPublicService.applyDown(appId);
        return R.ok(null, "申请成功");

    }


    @PutMapping("/withdraw/{appPublicId}")
    @Transactional
    public R withdraw(@PathVariable String appPublicId) {
        appPublicService.withdraw(appPublicId);
        return R.ok(null, "撤回成功");

    }

    @DeleteMapping("/delete/{appPublicId}")
    @Transactional
    public R delete(@PathVariable String appPublicId) {
        appPublicService.deleteApply(appPublicId);
        return R.ok(null, "删除成功");

    }


}
