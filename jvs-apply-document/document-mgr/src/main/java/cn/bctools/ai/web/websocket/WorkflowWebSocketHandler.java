package cn.bctools.ai.web.websocket;

import cn.bctools.stream.constants.StreamConstants;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工作流WebSocket处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class WorkflowWebSocketHandler implements WebSocketHandler {
    
    /**
     * WebSocket会话缓存
     */
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    /**
     * 执行ID与会话的映射
     */
    private final Map<String, String> executionSessionMap = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        
        log.info("🔗 WebSocket连接已建立 - 会话ID: {}", sessionId);
        
        // 发送连接成功消息
        sendMessage(session, StreamConstants.EventTypeCodes.CONNECTED, 
                Map.of("message", "WebSocket连接已建立", "sessionId", sessionId));
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload().toString();
        
        log.debug("📨 收到WebSocket消息 - 会话ID: {}, 消息: {}", sessionId, payload);
        
        try {
            Map<String, Object> messageData = JSON.parseObject(payload, Map.class);
            String action = (String) messageData.get("action");
            
            switch (action) {
                case "subscribe":
                    handleSubscribe(session, messageData);
                    break;
                case "unsubscribe":
                    handleUnsubscribe(session, messageData);
                    break;
                case "ping":
                    handlePing(session);
                    break;
                default:
                    sendError(session, "未知的操作: " + action);
            }
            
        } catch (Exception e) {
            log.error("处理WebSocket消息失败 - 会话ID: {}, 错误: {}", sessionId, e.getMessage());
            sendError(session, "消息处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        log.error("❌ WebSocket传输错误 - 会话ID: {}, 错误: {}", sessionId, exception.getMessage());
        
        // 清理会话
        cleanupSession(sessionId);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        log.info("🚫 WebSocket连接已关闭 - 会话ID: {}, 状态: {}", sessionId, closeStatus);
        
        // 清理会话
        cleanupSession(sessionId);
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 处理订阅消息
     */
    private void handleSubscribe(WebSocketSession session, Map<String, Object> messageData) throws IOException {
        String executionId = (String) messageData.get("executionId");
        if (executionId == null || executionId.trim().isEmpty()) {
            sendError(session, "执行ID不能为空");
            return;
        }
        
        String sessionId = session.getId();
        executionSessionMap.put(executionId, sessionId);
        
        log.info("📝 WebSocket订阅成功 - 会话ID: {}, 执行ID: {}", sessionId, executionId);
        
        sendMessage(session, "subscribed", Map.of(
                "executionId", executionId,
                "message", "订阅成功"
        ));
    }
    
    /**
     * 处理取消订阅消息
     */
    private void handleUnsubscribe(WebSocketSession session, Map<String, Object> messageData) throws IOException {
        String executionId = (String) messageData.get("executionId");
        if (executionId != null) {
            executionSessionMap.remove(executionId);
            log.info("📝 WebSocket取消订阅 - 会话ID: {}, 执行ID: {}", session.getId(), executionId);
        }
        
        sendMessage(session, "unsubscribed", Map.of(
                "executionId", executionId != null ? executionId : "",
                "message", "取消订阅成功"
        ));
    }
    
    /**
     * 处理心跳消息
     */
    private void handlePing(WebSocketSession session) throws IOException {
        sendMessage(session, "pong", Map.of(
                "timestamp", System.currentTimeMillis(),
                "message", "pong"
        ));
    }
    
    /**
     * 发送消息到指定执行ID的WebSocket连接
     */
    public void sendToExecution(String executionId, String eventType, Object data) {
        String sessionId = executionSessionMap.get(executionId);
        if (sessionId != null) {
            WebSocketSession session = sessions.get(sessionId);
            if (session != null && session.isOpen()) {
                try {
                    sendMessage(session, eventType, data);
                } catch (IOException e) {
                    log.error("发送WebSocket消息失败 - 执行ID: {}, 会话ID: {}, 错误: {}", 
                            executionId, sessionId, e.getMessage());
                    // 清理失效的连接
                    cleanupSession(sessionId);
                }
            } else {
                // 清理失效的映射
                executionSessionMap.remove(executionId);
                if (sessionId != null) {
                    sessions.remove(sessionId);
                }
            }
        }
    }
    
    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, String eventType, Object data) throws IOException {
        if (session.isOpen()) {
            Map<String, Object> message = Map.of(
                    "event", eventType,
                    "data", data,
                    "timestamp", System.currentTimeMillis()
            );
            
            String jsonMessage = JSON.toJSONString(message);
            session.sendMessage(new TextMessage(jsonMessage));
        }
    }
    
    /**
     * 发送错误消息
     */
    private void sendError(WebSocketSession session, String errorMessage) throws IOException {
        sendMessage(session, StreamConstants.EventTypeCodes.ERROR, Map.of(
                "error", errorMessage,
                "timestamp", System.currentTimeMillis()
        ));
    }
    
    /**
     * 清理会话
     */
    private void cleanupSession(String sessionId) {
        sessions.remove(sessionId);
        
        // 清理执行ID映射
        executionSessionMap.entrySet().removeIf(entry -> sessionId.equals(entry.getValue()));
        
        log.debug("🧹 清理WebSocket会话 - 会话ID: {}", sessionId);
    }
    
    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return sessions.size();
    }
    
    /**
     * 检查是否有指定执行ID的活跃连接
     */
    public boolean hasActiveConnection(String executionId) {
        String sessionId = executionSessionMap.get(executionId);
        if (sessionId != null) {
            WebSocketSession session = sessions.get(sessionId);
            return session != null && session.isOpen();
        }
        return false;
    }
}
