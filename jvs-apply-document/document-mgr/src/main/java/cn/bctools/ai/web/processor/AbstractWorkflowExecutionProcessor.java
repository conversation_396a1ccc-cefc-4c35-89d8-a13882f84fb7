package cn.bctools.ai.web.processor;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.stream.constants.StreamConstants;
import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.manager.StreamExecutionManager;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.utils.WorkflowExecutionUtils;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.web.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.CompletableFuture;

/**
 * 抽象工作流执行处理器
 * 提供通用的处理逻辑和模板方法
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractWorkflowExecutionProcessor implements WorkflowExecutionProcessor {
    
    @Autowired
    protected StreamExecutionManager streamExecutionManager;
    
    @Autowired
    protected AppGenerateService appGenerateService;
    
    /**
     * 模板方法：处理执行前的准备工作
     */
    protected StreamExecutionContext prepareExecution(AppDetail appDetail, 
                                                     UserDto user, 
                                                     WorkflowExecutionRequest request,
                                                     HttpServletRequest httpRequest, 
                                                     HttpServletResponse httpResponse) {
        
        // 验证请求
        WorkflowExecutionUtils.validateExecutionRequest(request);
        
        // 创建流式执行上下文
        StreamExecutionContext context = streamExecutionManager.createContext(
                appDetail.getId(),
                appDetail.getWorkflowId(),
                request.getParams(),
                getSupportedOutputType()
        );
        
        // 设置上下文信息
        context.setUser(user)
               .setRequest(httpRequest)
               .setResponse(httpResponse);
        
        // 设置超时时间
        if (request.getTimeoutSeconds() != null) {
            context.setAttribute(StreamConstants.AttributeNames.TIMEOUT_SECONDS, request.getTimeoutSeconds());
        }

        // 设置扩展属性
        if (request.getMetadata() != null) {
            request.getMetadata().forEach(context::setAttribute);
        }

        // 设置客户端信息
        context.setAttribute(StreamConstants.AttributeNames.CLIENT_IP, WorkflowExecutionUtils.getClientIpAddress(httpRequest));
        context.setAttribute(StreamConstants.AttributeNames.USER_AGENT, WorkflowExecutionUtils.getUserAgent(httpRequest));
        context.setAttribute(StreamConstants.AttributeNames.IS_MOBILE, WorkflowExecutionUtils.isMobileRequest(httpRequest));
        
        log.info("🎯 执行上下文准备完成 - 执行ID: {}, 处理器: {}", 
                context.getExecutionId(), this.getClass().getSimpleName());
        
        return context;
    }
    
    /**
     * 模板方法：异步执行工作流
     */
    protected void executeWorkflowAsync(AppDetail appDetail, 
                                      UserDto user, 
                                      WorkflowExecutionRequest request,
                                      StreamExecutionContext context) {
        
        CompletableFuture.runAsync(() -> {
            try {
                log.info("🚀 开始异步执行工作流 - 执行ID: {}", context.getExecutionId());
                
                // 执行前回调
                onBeforeExecution(context);
                
                // 调用原有的工作流执行逻辑
                Object result = appGenerateService.generate(
                        context.getRequest(), 
                        context.getResponse(), 
                        appDetail, 
                        user, 
                        request.getParams(), 
                        InvokeFrom.WEB_APP, 
                        true
                );
                
                // 执行后回调
                onAfterExecution(context, result);
                
                // 如果执行成功，通知完成
                if (result != null) {
                    streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), result);
                    log.info("✅ 工作流执行完成 - 执行ID: {}", context.getExecutionId());
                } else {
                    streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), "执行完成，无返回结果");
                }
                
            } catch (Exception e) {
                log.error("❌ 工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
                
                // 执行失败回调
                onExecutionFailed(context, e);
                
                streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            }
        }).exceptionally(throwable -> {
            log.error("❌ 异步执行异常 - 执行ID: {}", context.getExecutionId(), throwable);
            
            // 异常回调
            onExecutionException(context, throwable);
            
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), throwable.getMessage());
            return null;
        });
    }
    
    /**
     * 钩子方法：执行前回调
     */
    protected void onBeforeExecution(StreamExecutionContext context) {
        log.debug("🔄 执行前回调 - 执行ID: {}", context.getExecutionId());
    }
    
    /**
     * 钩子方法：执行后回调
     */
    protected void onAfterExecution(StreamExecutionContext context, Object result) {
        log.debug("🔄 执行后回调 - 执行ID: {}, 结果类型: {}", 
                context.getExecutionId(), 
                result != null ? result.getClass().getSimpleName() : "null");
    }
    
    /**
     * 钩子方法：执行失败回调
     */
    protected void onExecutionFailed(StreamExecutionContext context, Exception exception) {
        log.debug("🔄 执行失败回调 - 执行ID: {}, 异常: {}", 
                context.getExecutionId(), exception.getClass().getSimpleName());
    }
    
    /**
     * 钩子方法：执行异常回调
     */
    protected void onExecutionException(StreamExecutionContext context, Throwable throwable) {
        log.debug("🔄 执行异常回调 - 执行ID: {}, 异常: {}", 
                context.getExecutionId(), throwable.getClass().getSimpleName());
    }
    
    /**
     * 工具方法：生成执行ID
     */
    protected String generateExecutionId() {
        return WorkflowExecutionUtils.generateExecutionId(getSupportedOutputType().getCode());
    }
    
    /**
     * 工具方法：记录处理器选择日志
     */
    protected void logProcessorSelection(WorkflowExecutionRequest request, 
                                       HttpServletRequest httpRequest, 
                                       String reason) {
        log.info("🎯 选择处理器: {} - 原因: {}, 流式类型: {}, Accept: {}", 
                this.getClass().getSimpleName(),
                reason,
                request.getStreamType(),
                httpRequest.getHeader("Accept"));
    }
}