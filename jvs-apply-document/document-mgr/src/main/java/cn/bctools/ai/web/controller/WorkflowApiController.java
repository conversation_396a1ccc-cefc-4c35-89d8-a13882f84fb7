package cn.bctools.ai.web.controller;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.interfaces.GetAppInfo;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.stream.manager.StreamExecutionManager;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Api(tags = "工作流")
public class WorkflowApiController {

    @Autowired
    private AppGenerateService appGenerateService;

    @Autowired
    private StreamExecutionManager streamExecutionManager;

    @PostMapping(value = "/workflows/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("运行工作流（默认SSE流式输出）")
    public Object workflowsRun(HttpServletRequest request, HttpServletResponse response,
                              @RequestBody Map<String, Object> paramMap) {
        AppDetail appDetail = AppDetailContext.getObject();
        if (!appDetail.getMode().equals(AppMode.WORKFLOW.value)) {
            return R.failed("该应用不是工作流应用");
        }
        UserDto currentUser = UserCurrentUtils.getCurrentUser();

        // 检查是否需要流式输出
        String streamType = request.getHeader("X-Stream-Type");
        if (streamType != null) {
            try {
                StreamOutputType outputType = StreamOutputType.fromCode(streamType);
                return handleStreamOutput(request, response, appDetail, currentUser, paramMap, outputType);
            } catch (IllegalArgumentException e) {
                log.warn("无效的流式输出类型: {}", streamType);
            }
        }

        // 默认使用原有的生成方式
        return appGenerateService.generate(request, response, appDetail, currentUser, paramMap, InvokeFrom.WEB_APP, true);
    }

    @PostMapping(value = "/workflows/run/flux", produces = MediaType.APPLICATION_STREAM_JSON_VALUE)
    @ApiOperation("运行工作流（Flux流式输出）")
    public Flux<Object> workflowsRunFlux(HttpServletRequest request, HttpServletResponse response,
                                        @RequestBody Map<String, Object> paramMap) {
        AppDetail appDetail = AppDetailContext.getObject();
        if (!appDetail.getMode().equals(AppMode.WORKFLOW.value)) {
            return Flux.error(new IllegalArgumentException("该应用不是工作流应用"));
        }
        UserDto currentUser = UserCurrentUtils.getCurrentUser();

        return handleFluxOutput(request, response, appDetail, currentUser, paramMap);
    }

    @GetMapping(value = "/workflows/stream/{executionId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("通过SSE获取工作流执行进度")
    public SseEmitter getExecutionStream(@PathVariable String executionId) {
        StreamExecutionContext context = streamExecutionManager.getContext(executionId);
        if (context == null) {
            SseEmitter emitter = new SseEmitter(1000L);
            try {
                emitter.send(SseEmitter.event().name("error").data("执行上下文不存在"));
                emitter.complete();
            } catch (Exception e) {
                log.error("发送错误消息失败", e);
            }
            return emitter;
        }

        return context.getSseEmitter();
    }

    @GetMapping("/workflows/execution/{executionId}/status")
    @ApiOperation("获取工作流执行状态")
    public R<Object> getExecutionStatus(@PathVariable String executionId) {
        StreamExecutionContext context = streamExecutionManager.getContext(executionId);
        if (context == null) {
            return R.failed("执行上下文不存在");
        }

        return R.ok(context.getStats());
    }

    @PostMapping("/workflows/execution/{executionId}/cancel")
    @ApiOperation("取消工作流执行")
    public R<String> cancelExecution(@PathVariable String executionId) {
        streamExecutionManager.cancelExecution(executionId);
        return R.ok("执行已取消");
    }

    /**
     * 处理流式输出
     */
    private Object handleStreamOutput(HttpServletRequest request, HttpServletResponse response,
                                    AppDetail appDetail, UserDto currentUser, Map<String, Object> paramMap,
                                    StreamOutputType outputType) {
        switch (outputType) {
            case FLUX:
                return handleFluxOutput(request, response, appDetail, currentUser, paramMap);
            case SSE:
                return handleSseOutput(request, response, appDetail, currentUser, paramMap);
            case WEBSOCKET:
                // WebSocket需要通过WebSocket端点连接，这里返回连接信息
                return R.ok("请通过WebSocket端点连接: /ws/workflow-execution");
            default:
                // 其他类型使用默认方式
                return appGenerateService.generate(request, response, appDetail, currentUser, paramMap, InvokeFrom.WEB_APP, true);
        }
    }

    /**
     * 处理Flux流式输出
     */
    private Flux<Object> handleFluxOutput(HttpServletRequest request, HttpServletResponse response,
                                         AppDetail appDetail, UserDto currentUser, Map<String, Object> paramMap) {
        // 创建流式执行上下文
        StreamExecutionContext context = streamExecutionManager.createContext(
                appDetail.getId(),
                appDetail.getWorkflowId(),
                paramMap,
                StreamOutputType.FLUX
        );

        context.setUser(currentUser)
               .setRequest(request)
               .setResponse(response);

        // 获取Flux流
        Flux<Object> flux = streamExecutionManager.getFlux(context);

        // 异步执行工作流
        executeWorkflowAsync(appDetail, currentUser, paramMap, context);

        return flux;
    }

    /**
     * 处理SSE流式输出
     */
    private SseEmitter handleSseOutput(HttpServletRequest request, HttpServletResponse response,
                                      AppDetail appDetail, UserDto currentUser, Map<String, Object> paramMap) {
        // 创建流式执行上下文
        StreamExecutionContext context = streamExecutionManager.createContext(
                appDetail.getId(),
                appDetail.getWorkflowId(),
                paramMap,
                StreamOutputType.SSE
        );

        context.setUser(currentUser)
               .setRequest(request)
               .setResponse(response);

        // 初始化SSE
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        context.setSseEmitter(emitter);

        // 异步执行工作流
        executeWorkflowAsync(appDetail, currentUser, paramMap, context);

        return emitter;
    }

    /**
     * 异步执行工作流
     */
    private void executeWorkflowAsync(AppDetail appDetail, UserDto currentUser, Map<String, Object> paramMap,
                                     StreamExecutionContext context) {
        // 在新线程中执行工作流，避免阻塞HTTP请求
        CompletableFuture.runAsync(() -> {
            try {
                // 调用原有的工作流执行逻辑
                Object result = appGenerateService.generate(
                        context.getRequest(),
                        context.getResponse(),
                        appDetail,
                        currentUser,
                        paramMap,
                        InvokeFrom.WEB_APP,
                        true
                );

                // 如果执行成功，通知完成
                if (result != null) {
                    streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), result);
                }

            } catch (Exception e) {
                log.error("工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
                streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            }
        });
    }
}