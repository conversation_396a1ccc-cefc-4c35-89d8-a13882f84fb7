package cn.bctools.ai.web.controller;

import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.manager.StreamExecutionManager;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.dto.WorkflowExecutionResponse;
import cn.bctools.ai.web.factory.WorkflowExecutionProcessorFactory;
import cn.bctools.ai.web.service.WorkflowExecutionService;
import cn.bctools.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Api(tags = "工作流API")
public class WorkflowApiController {

    @Autowired
    private WorkflowExecutionService workflowExecutionService;

    @Autowired
    private StreamExecutionManager streamExecutionManager;

    @PostMapping("/workflows/run")
    @ApiOperation("运行工作流（统一接口，支持多种输出方式）")
    public Object runWorkflow(@Valid @RequestBody WorkflowExecutionRequest request,
                             HttpServletRequest httpRequest,
                             HttpServletResponse httpResponse) {

        log.info("📥 收到工作流执行请求 - 流式类型: {}, 参数: {}",
                request.getStreamType(), request.getParams().keySet());

        return workflowExecutionService.executeWorkflow(request, httpRequest, httpResponse);
    }

    @GetMapping(value = "/workflows/stream/{executionId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("通过SSE获取工作流执行进度")
    public Object getExecutionStream(@ApiParam("执行ID") @PathVariable String executionId) {
        StreamExecutionContext context = streamExecutionManager.getContext(executionId);
        if (context == null) {
            return R.failed("执行上下文不存在");
        }

        if (context.getSseEmitter() != null) {
            return context.getSseEmitter();
        }

        return R.failed("该执行不支持SSE流式输出");
    }

    @GetMapping("/workflows/execution/{executionId}/status")
    @ApiOperation("获取工作流执行状态")
    public R<WorkflowExecutionResponse> getExecutionStatus(@ApiParam("执行ID") @PathVariable String executionId) {
        return workflowExecutionService.getExecutionStatus(executionId);
    }

    @PostMapping("/workflows/execution/{executionId}/cancel")
    @ApiOperation("取消工作流执行")
    public R<String> cancelExecution(@ApiParam("执行ID") @PathVariable String executionId) {
        return workflowExecutionService.cancelExecution(executionId);
    }

    @GetMapping("/workflows/executions/active")
    @ApiOperation("获取所有活跃执行")
    public R<Map<String, StreamExecutionContext>> getActiveExecutions() {
        return workflowExecutionService.getActiveExecutions();
    }

    @GetMapping("/workflows/stats")
    @ApiOperation("获取执行统计信息")
    public R<StreamExecutionManager.ExecutionStats> getExecutionStats() {
        return workflowExecutionService.getExecutionStats();
    }

    @GetMapping("/workflows/processors/info")
    @ApiOperation("获取处理器信息")
    public R<Map<String, Object>> getProcessorInfo() {
        return workflowExecutionService.getProcessorInfo();
    }

    @PostMapping("/workflows/processors/recommend")
    @ApiOperation("推荐处理器")
    public R<WorkflowExecutionProcessorFactory.ProcessorRecommendation> recommendProcessor(
            @RequestBody WorkflowExecutionRequest request,
            HttpServletRequest httpRequest) {
        return workflowExecutionService.recommendProcessor(request, httpRequest);
    }
}