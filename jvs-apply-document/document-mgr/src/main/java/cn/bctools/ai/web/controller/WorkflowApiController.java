package cn.bctools.ai.web.controller;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.interfaces.GetAppInfo;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.stream.manager.StreamExecutionManager;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api")
@Api(tags = "工作流")
public class WorkflowApiController {
    AppGenerateService appGenerateService;

    @PostMapping(value = "/workflows/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Object workflowsRun(HttpServletRequest request, HttpServletResponse response, @RequestBody Map<String, Object> paramMap) {
        AppDetail appDetail = AppDetailContext.getObject();
        if (!appDetail.getMode().equals(AppMode.WORKFLOW.value)) {
            return R.failed("该应用不是工作流应用");
        }
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        //todo 运行
        return appGenerateService.generate(request, response, appDetail, currentUser, paramMap, InvokeFrom.WEB_APP, true);

    }
}