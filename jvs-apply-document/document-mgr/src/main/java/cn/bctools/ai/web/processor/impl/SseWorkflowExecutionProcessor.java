package cn.bctools.ai.web.processor.impl;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.stream.manager.StreamExecutionManager;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.processor.WorkflowExecutionProcessor;
import cn.bctools.common.entity.dto.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * SSE工作流执行处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SseWorkflowExecutionProcessor implements WorkflowExecutionProcessor {
    
    @Autowired
    private StreamExecutionManager streamExecutionManager;
    
    @Autowired
    private AppGenerateService appGenerateService;
    
    @Override
    public StreamOutputType getSupportedOutputType() {
        return StreamOutputType.SSE;
    }
    
    @Override
    public boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        // 检查请求参数中的流式类型
        if (request.getStreamOutputType() == StreamOutputType.SSE) {
            return true;
        }
        
        // 检查Accept头
        String accept = httpRequest.getHeader("Accept");
        if (accept != null && accept.contains(MediaType.TEXT_EVENT_STREAM_VALUE)) {
            return true;
        }
        
        return false;
    }
    
    @Override
    public Object processExecution(AppDetail appDetail, 
                                 UserDto user, 
                                 WorkflowExecutionRequest request,
                                 HttpServletRequest httpRequest, 
                                 HttpServletResponse httpResponse) {
        
        log.info("📡 开始SSE流式执行 - 应用ID: {}", appDetail.getId());
        
        try {
            // 创建流式执行上下文
            StreamExecutionContext context = streamExecutionManager.createContext(
                    appDetail.getId(),
                    appDetail.getWorkflowId(),
                    request.getParams(),
                    StreamOutputType.SSE
            );
            
            // 设置上下文信息
            context.setUser(user)
                   .setRequest(httpRequest)
                   .setResponse(httpResponse);
            
            // 创建SSE发射器
            long timeoutMs = request.getTimeoutSeconds() != null ? 
                    request.getTimeoutSeconds() * 1000L : 300000L; // 默认5分钟
            
            SseEmitter emitter = new SseEmitter(timeoutMs);
            context.setSseEmitter(emitter);
            
            // 设置SSE回调
            setupSseCallbacks(emitter, context);
            
            // 设置扩展属性
            if (request.getMetadata() != null) {
                request.getMetadata().forEach(context::setAttribute);
            }
            
            // 发送连接成功消息
            sendConnectionMessage(emitter, context.getExecutionId());
            
            // 异步执行工作流
            executeWorkflowAsync(appDetail, user, request, context);
            
            log.info("✅ SSE连接已建立 - 执行ID: {}", context.getExecutionId());
            return emitter;
            
        } catch (Exception e) {
            log.error("❌ SSE流式执行失败 - 应用ID: {}, 错误: {}", appDetail.getId(), e.getMessage(), e);
            
            // 创建错误SSE响应
            SseEmitter errorEmitter = new SseEmitter(1000L);
            try {
                errorEmitter.send(SseEmitter.event()
                        .name("error")
                        .data("SSE流式执行失败: " + e.getMessage()));
                errorEmitter.complete();
            } catch (IOException ioException) {
                log.error("发送SSE错误消息失败", ioException);
            }
            return errorEmitter;
        }
    }
    
    @Override
    public int getPriority() {
        return 20; // 中等优先级
    }
    
    /**
     * 设置SSE回调
     */
    private void setupSseCallbacks(SseEmitter emitter, StreamExecutionContext context) {
        emitter.onCompletion(() -> {
            log.info("📡 SSE连接完成 - 执行ID: {}", context.getExecutionId());
            streamExecutionManager.completeExecution(context.getExecutionId());
        });
        
        emitter.onTimeout(() -> {
            log.warn("⏰ SSE连接超时 - 执行ID: {}", context.getExecutionId());
            streamExecutionManager.cancelExecution(context.getExecutionId());
        });
        
        emitter.onError((ex) -> {
            log.error("❌ SSE连接错误 - 执行ID: {}, 错误: {}", context.getExecutionId(), ex.getMessage());
            streamExecutionManager.cancelExecution(context.getExecutionId());
        });
    }
    
    /**
     * 发送连接成功消息
     */
    private void sendConnectionMessage(SseEmitter emitter, String executionId) {
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("SSE连接已建立 - 执行ID: " + executionId));
        } catch (IOException e) {
            log.error("发送SSE连接成功消息失败 - 执行ID: {}", executionId, e);
        }
    }
    
    /**
     * 异步执行工作流
     */
    private void executeWorkflowAsync(AppDetail appDetail, 
                                    UserDto user, 
                                    WorkflowExecutionRequest request,
                                    StreamExecutionContext context) {
        
        CompletableFuture.runAsync(() -> {
            try {
                log.info("🚀 开始异步执行工作流 - 执行ID: {}", context.getExecutionId());
                
                // 调用原有的工作流执行逻辑
                Object result = appGenerateService.generate(
                        context.getRequest(), 
                        context.getResponse(), 
                        appDetail, 
                        user, 
                        request.getParams(), 
                        InvokeFrom.WEB_APP, 
                        true
                );
                
                // 如果执行成功，通知完成
                if (result != null) {
                    streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), result);
                    log.info("✅ 工作流执行完成 - 执行ID: {}", context.getExecutionId());
                } else {
                    streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), "执行完成，无返回结果");
                }
                
            } catch (Exception e) {
                log.error("❌ 工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
                streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            }
        }).exceptionally(throwable -> {
            log.error("❌ 异步执行异常 - 执行ID: {}", context.getExecutionId(), throwable);
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), throwable.getMessage());
            return null;
        });
    }
}
