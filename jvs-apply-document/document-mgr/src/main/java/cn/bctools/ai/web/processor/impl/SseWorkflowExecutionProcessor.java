package cn.bctools.ai.web.processor.impl;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.processor.AbstractWorkflowExecutionProcessor;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.stream.enums.StreamOutputType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * SSE工作流执行处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SseWorkflowExecutionProcessor extends AbstractWorkflowExecutionProcessor {

    @Override
    public StreamOutputType getSupportedOutputType() {
        return StreamOutputType.SSE;
    }

    @Override
    public boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        // 检查请求参数中的流式类型
        if (request.getStreamOutputType() == StreamOutputType.SSE) {
            return true;
        }

        // 检查Accept头
        String accept = httpRequest.getHeader(StreamConstants.Headers.ACCEPT);
        return accept != null && accept.contains(StreamConstants.MediaTypes.TEXT_EVENT_STREAM);
    }

    @Override
    public Object processExecution(AppDetail appDetail,
                                   UserDto user,
                                   WorkflowExecutionRequest request,
                                   HttpServletRequest httpRequest,
                                   HttpServletResponse httpResponse) {

        log.info("📡 开始SSE流式执行 - 应用ID: {}", appDetail.getId());

        try {
            // 使用模板方法准备执行上下文
            StreamExecutionContext context = prepareExecution(appDetail, user, request, httpRequest, httpResponse);

            // 创建SSE发射器
            // 默认5分钟
            long timeoutMs = request.getTimeoutSeconds() != null ?
                    request.getTimeoutSeconds() * 1000L : 300000L;

            SseEmitter emitter = new SseEmitter(timeoutMs);
            context.setSseEmitter(emitter);

            // 设置SSE回调
            setupSseCallbacks(emitter, context);

            // 发送连接成功消息
            sendConnectionMessage(emitter, context.getExecutionId());

            // 使用模板方法异步执行工作流
            executeWorkflowAsync(appDetail, user, request, context);

            log.info("✅ SSE连接已建立 - 执行ID: {}", context.getExecutionId());
            return emitter;

        } catch (Exception e) {
            log.error("❌ SSE流式执行失败 - 应用ID: {}, 错误: {}", appDetail.getId(), e.getMessage(), e);

            // 创建错误SSE响应
            SseEmitter errorEmitter = new SseEmitter(1000L);
            try {
                errorEmitter.send(SseEmitter.event()
                        .name("error")
                        .data("SSE流式执行失败: " + e.getMessage()));
                errorEmitter.complete();
            } catch (IOException ioException) {
                log.error("发送SSE错误消息失败", ioException);
            }
            return errorEmitter;
        }
    }

    @Override
    public int getPriority() {
        // 中等优先级
        return 20;
    }

    /**
     * 设置SSE回调
     */
    private void setupSseCallbacks(SseEmitter emitter, StreamExecutionContext context) {
        emitter.onCompletion(() -> {
            log.info("📡 SSE连接完成 - 执行ID: {}", context.getExecutionId());
            streamExecutionManager.completeExecution(context.getExecutionId());
        });

        emitter.onTimeout(() -> {
            log.warn("⏰ SSE连接超时 - 执行ID: {}", context.getExecutionId());
            streamExecutionManager.cancelExecution(context.getExecutionId());
        });

        emitter.onError((ex) -> {
            log.error("❌ SSE连接错误 - 执行ID: {}, 错误: {}", context.getExecutionId(), ex.getMessage());
            streamExecutionManager.cancelExecution(context.getExecutionId());
        });
    }

    /**
     * 发送连接成功消息
     */
    private void sendConnectionMessage(SseEmitter emitter, String executionId) {
        try {
            emitter.send(SseEmitter.event()
                    .name(StreamConstants.EventTypeCodes.CONNECTED)
                    .data(StreamConstants.Messages.CONNECTION_ESTABLISHED + " - 执行ID: " + executionId));
        } catch (IOException e) {
            log.error("发送SSE连接成功消息失败 - 执行ID: {}", executionId, e);
        }
    }


}