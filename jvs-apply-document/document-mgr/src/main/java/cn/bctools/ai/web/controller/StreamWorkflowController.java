package cn.bctools.ai.web.controller;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.manager.StreamOutputManager;
import cn.bctools.stream.utils.StreamExecutionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 流式工作流控制器
 * 提供真正的SSE和Flux流式输出端点
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/stream")
@Api(tags = "流式工作流")
@RequiredArgsConstructor
public class StreamWorkflowController {
    
    private final AppGenerateService appGenerateService;
    
    private final StreamOutputManager streamOutputManager;
    
    @PostMapping(value = "/workflows/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("SSE流式执行工作流")
    public SseEmitter executeWorkflowWithSse(@RequestBody Map<String, Object> paramMap,
                                           HttpServletRequest request,
                                           HttpServletResponse response) {
        
        AppDetail appDetail = AppDetailContext.getObject();
        if (!appDetail.getMode().equals(AppMode.WORKFLOW.getValue())) {
            SseEmitter errorEmitter = new SseEmitter(1000L);
            try {
                errorEmitter.send(SseEmitter.event().name("error").data(StreamConstants.Messages.INVALID_APP_TYPE));
                errorEmitter.complete();
            } catch (Exception e) {
                log.error("发送SSE错误消息失败", e);
            }
            return errorEmitter;
        }
        
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        String executionId = StreamExecutionUtils.generateExecutionId();
        
        log.info("🚀 开始SSE流式执行工作流 - 执行ID: {}", executionId);
        
        // 创建SSE连接
        long timeoutMs = getTimeoutFromRequest(request, StreamConstants.Defaults.DEFAULT_SSE_TIMEOUT_MS);
        SseEmitter emitter = streamOutputManager.createSseConnection(executionId, timeoutMs);
        
        // 异步执行工作流
        executeWorkflowAsync(appDetail, currentUser, paramMap, executionId, request, response);
        
        return emitter;
    }
    
    @PostMapping(value = "/workflows/flux", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("Flux流式执行工作流")
    public Flux<Object> executeWorkflowWithFlux(@RequestBody Map<String, Object> paramMap,
                                              HttpServletRequest request,
                                              HttpServletResponse response) {
        
        AppDetail appDetail = AppDetailContext.getObject();
        if (!appDetail.getMode().equals(AppMode.WORKFLOW.value)) {
            return Flux.error(new IllegalArgumentException(StreamConstants.Messages.INVALID_APP_TYPE));
        }
        
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        String executionId = StreamExecutionUtils.generateExecutionId();
        
        log.info("🌊 开始Flux流式执行工作流 - 执行ID: {}", executionId);
        
        // 创建Flux连接
        Flux<Object> flux = streamOutputManager.createFluxConnection(executionId);
        
        // 异步执行工作流
        executeWorkflowAsync(appDetail, currentUser, paramMap, executionId, request, response);
        
        return flux;
    }
    
    @GetMapping(value = "/workflows/{executionId}/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("通过执行ID连接SSE流")
    public SseEmitter connectToSse(@ApiParam("执行ID") @PathVariable String executionId,
                                 HttpServletRequest request) {
        
        log.info("🔗 连接到SSE流 - 执行ID: {}", executionId);
        
        // 检查是否已有活跃连接
        if (streamOutputManager.hasActiveConnection(executionId)) {
            log.warn("⚠️ 执行ID {} 已有活跃连接", executionId);
        }
        
        // 创建新的SSE连接
        long timeoutMs = getTimeoutFromRequest(request, StreamConstants.Defaults.DEFAULT_SSE_TIMEOUT_MS);
        return streamOutputManager.createSseConnection(executionId, timeoutMs);
    }
    
    @GetMapping(value = "/workflows/{executionId}/flux", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("通过执行ID连接Flux流")
    public Flux<Object> connectToFlux(@ApiParam("执行ID") @PathVariable String executionId) {
        
        log.info("🌊 连接到Flux流 - 执行ID: {}", executionId);
        
        // 检查是否已有活跃连接
        if (streamOutputManager.hasActiveConnection(executionId)) {
            log.warn("⚠️ 执行ID {} 已有活跃连接", executionId);
        }
        
        // 创建新的Flux连接
        return streamOutputManager.createFluxConnection(executionId);
    }
    
    @GetMapping("/workflows/connections/count")
    @ApiOperation("获取活跃连接数")
    public R<Integer> getActiveConnectionCount() {
        int count = streamOutputManager.getActiveConnectionCount();
        return R.ok(count);
    }
    
    @GetMapping("/workflows/{executionId}/status")
    @ApiOperation("检查执行状态")
    public R<Map<String, Object>> checkExecutionStatus(@ApiParam("执行ID") @PathVariable String executionId) {
        boolean hasConnection = streamOutputManager.hasActiveConnection(executionId);
        
        Map<String, Object> status = Map.of(
                "executionId", executionId,
                "hasActiveConnection", hasConnection,
                "timestamp", System.currentTimeMillis()
        );
        
        return R.ok(status);
    }
    
    /**
     * 异步执行工作流
     */
    private void executeWorkflowAsync(AppDetail appDetail, 
                                    UserDto currentUser, 
                                    Map<String, Object> paramMap,
                                    String executionId,
                                    HttpServletRequest request, 
                                    HttpServletResponse response) {
        
        CompletableFuture.runAsync(() -> {
            try {
                log.info("🚀 开始异步执行工作流 - 执行ID: {}", executionId);
                
                // 通知工作流开始（这会触发流式输出）
                // 假设只有1个节点，实际应该计算
                StreamExecutionUtils.notifyWorkflowStarted(executionId, 1);
                
                // 执行工作流
                Object result = appGenerateService.generate(
                        request, response, appDetail, currentUser, paramMap, InvokeFrom.WEB_APP, true
                );
                
                // 通知工作流完成
                StreamExecutionUtils.notifyWorkflowCompleted(executionId, result);
                
                log.info("✅ 工作流执行完成 - 执行ID: {}", executionId);
                
            } catch (Exception e) {
                log.error("❌ 工作流执行失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage(), e);
                
                // 通知工作流失败
                StreamExecutionUtils.notifyWorkflowFailed(executionId, e.getMessage());
            }
        });
    }
    
    /**
     * 从请求中获取超时时间
     */
    private long getTimeoutFromRequest(HttpServletRequest request, long defaultTimeout) {
        String timeoutHeader = request.getHeader(StreamConstants.Headers.X_TIMEOUT_SECONDS);
        if (timeoutHeader != null) {
            try {
                // 转换为毫秒
                return Long.parseLong(timeoutHeader) * 1000L;
            } catch (NumberFormatException e) {
                log.warn("无效的超时时间: {}", timeoutHeader);
            }
        }
        return defaultTimeout;
    }
}