package cn.bctools.ai.web.processor.impl;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.stream.dto.StreamExecutionContext;
import cn.bctools.ai.stream.enums.StreamOutputType;
import cn.bctools.ai.stream.manager.StreamExecutionManager;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.processor.WorkflowExecutionProcessor;
import cn.bctools.common.entity.dto.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.CompletableFuture;

/**
 * Flux工作流执行处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class FluxWorkflowExecutionProcessor implements WorkflowExecutionProcessor {
    
    @Autowired
    private StreamExecutionManager streamExecutionManager;
    
    @Autowired
    private AppGenerateService appGenerateService;
    
    @Override
    public StreamOutputType getSupportedOutputType() {
        return StreamOutputType.FLUX;
    }
    
    @Override
    public boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        // 检查请求参数中的流式类型
        if (request.getStreamOutputType() == StreamOutputType.FLUX) {
            return true;
        }
        
        // 检查Accept头
        String accept = httpRequest.getHeader("Accept");
        if (accept != null && accept.contains(MediaType.APPLICATION_STREAM_JSON_VALUE)) {
            return true;
        }
        
        // 检查Content-Type
        String contentType = httpRequest.getContentType();
        if (contentType != null && contentType.contains(MediaType.APPLICATION_STREAM_JSON_VALUE)) {
            return true;
        }
        
        return false;
    }
    
    @Override
    public Object processExecution(AppDetail appDetail, 
                                 UserDto user, 
                                 WorkflowExecutionRequest request,
                                 HttpServletRequest httpRequest, 
                                 HttpServletResponse httpResponse) {
        
        log.info("🌊 开始Flux流式执行 - 应用ID: {}", appDetail.getId());
        
        try {
            // 创建流式执行上下文
            StreamExecutionContext context = streamExecutionManager.createContext(
                    appDetail.getId(),
                    appDetail.getWorkflowId(),
                    request.getParams(),
                    StreamOutputType.FLUX
            );
            
            // 设置上下文信息
            context.setUser(user)
                   .setRequest(httpRequest)
                   .setResponse(httpResponse);
            
            // 设置超时时间
            if (request.getTimeoutSeconds() != null) {
                context.setAttribute("timeoutSeconds", request.getTimeoutSeconds());
            }
            
            // 设置扩展属性
            if (request.getMetadata() != null) {
                request.getMetadata().forEach(context::setAttribute);
            }
            
            // 获取Flux流
            Flux<Object> flux = streamExecutionManager.getFlux(context);
            
            // 异步执行工作流
            executeWorkflowAsync(appDetail, user, request, context);
            
            log.info("✅ Flux流已创建 - 执行ID: {}", context.getExecutionId());
            return flux;
            
        } catch (Exception e) {
            log.error("❌ Flux流式执行失败 - 应用ID: {}, 错误: {}", appDetail.getId(), e.getMessage(), e);
            return Flux.error(new RuntimeException("Flux流式执行失败: " + e.getMessage(), e));
        }
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级
    }
    
    /**
     * 异步执行工作流
     */
    private void executeWorkflowAsync(AppDetail appDetail, 
                                    UserDto user, 
                                    WorkflowExecutionRequest request,
                                    StreamExecutionContext context) {
        
        CompletableFuture.runAsync(() -> {
            try {
                log.info("🚀 开始异步执行工作流 - 执行ID: {}", context.getExecutionId());
                
                // 调用原有的工作流执行逻辑
                Object result = appGenerateService.generate(
                        context.getRequest(), 
                        context.getResponse(), 
                        appDetail, 
                        user, 
                        request.getParams(), 
                        InvokeFrom.WEB_APP, 
                        true
                );
                
                // 如果执行成功，通知完成
                if (result != null) {
                    streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), result);
                    log.info("✅ 工作流执行完成 - 执行ID: {}", context.getExecutionId());
                } else {
                    streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), "执行完成，无返回结果");
                }
                
            } catch (Exception e) {
                log.error("❌ 工作流执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
                streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            }
        }).exceptionally(throwable -> {
            log.error("❌ 异步执行异常 - 执行ID: {}", context.getExecutionId(), throwable);
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), throwable.getMessage());
            return null;
        });
    }
}
