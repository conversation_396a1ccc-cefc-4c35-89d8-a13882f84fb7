package cn.bctools.ai.web.processor.impl;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.service.AppGenerateService;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.ai.web.processor.WorkflowExecutionProcessor;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.enums.StreamOutputType;
import cn.bctools.stream.manager.StreamOutputManager;
import cn.bctools.stream.utils.StreamExecutionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.CompletableFuture;

/**
 * Flux工作流执行处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class FluxWorkflowExecutionProcessor extends AbstractWorkflowExecutionProcessor {
    
    @Override
    public StreamOutputType getSupportedOutputType() {
        return StreamOutputType.FLUX;
    }
    
    @Override
    public boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest) {
        // 检查请求参数中的流式类型
        if (request.getStreamOutputType() == StreamOutputType.FLUX) {
            return true;
        }
        
        // 检查Accept头
        String accept = httpRequest.getHeader(StreamConstants.Headers.ACCEPT);
        if (accept != null && accept.contains(StreamConstants.MediaTypes.APPLICATION_STREAM_JSON)) {
            return true;
        }

        // 检查Content-Type
        String contentType = httpRequest.getContentType();
        return contentType != null && contentType.contains(StreamConstants.MediaTypes.APPLICATION_STREAM_JSON);
    }
    
    @Override
    public Object processExecution(AppDetail appDetail, 
                                 UserDto user, 
                                 WorkflowExecutionRequest request,
                                 HttpServletRequest httpRequest, 
                                 HttpServletResponse httpResponse) {
        
        log.info("🌊 开始Flux流式执行 - 应用ID: {}", appDetail.getId());
        
        try {
            // 使用模板方法准备执行上下文
            StreamExecutionContext context = prepareExecution(appDetail, user, request, httpRequest, httpResponse);
            
            // 获取Flux流
            Flux<Object> flux = streamExecutionManager.getFlux(context);
            
            // 使用模板方法异步执行工作流
            executeWorkflowAsync(appDetail, user, request, context);
            
            log.info("✅ Flux流已创建 - 执行ID: {}", context.getExecutionId());
            return flux;
            
        } catch (Exception e) {
            log.error("❌ Flux流式执行失败 - 应用ID: {}, 错误: {}", appDetail.getId(), e.getMessage(), e);
            return Flux.error(new RuntimeException("Flux流式执行失败: " + e.getMessage(), e));
        }
    }
    
    @Override
    public int getPriority() {
        // 最高优先级
        return 10;
    }
    

}