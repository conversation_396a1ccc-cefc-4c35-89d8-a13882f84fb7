package cn.bctools.ai.web.config;

import cn.bctools.ai.web.websocket.WorkflowWebSocketHandler;
import cn.bctools.stream.constants.StreamConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Autowired
    private WorkflowWebSocketHandler workflowWebSocketHandler;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(workflowWebSocketHandler, StreamConstants.Paths.WEBSOCKET_PREFIX + StreamConstants.Paths.WEBSOCKET_WORKFLOW)
                .setAllowedOrigins("*"); // 生产环境应该限制域名
    }
}
