package cn.bctools.ai.web.exception;

import cn.bctools.common.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 工作流执行异常处理器
 * 统一处理工作流执行过程中的异常
 * 
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = "cn.bctools.ai.web")
public class WorkflowExecutionExceptionHandler {
    
    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<R<String>> handleValidationException(MethodArgumentNotValidException e) {
        List<String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        
        String errorMessage = "参数验证失败: " + String.join(", ", errors);
        log.warn("参数验证异常: {}", errorMessage);
        
        return ResponseEntity.badRequest()
                .body(R.failed(errorMessage, HttpStatus.BAD_REQUEST.value(), "参数验证失败"));
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<R<String>> handleBindException(BindException e) {
        List<String> errors = e.getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        
        String errorMessage = "参数绑定失败: " + String.join(", ", errors);
        log.warn("参数绑定异常: {}", errorMessage);
        
        return ResponseEntity.badRequest()
                .body(R.failed(errorMessage, HttpStatus.BAD_REQUEST.value(), "参数绑定失败"));
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<R<String>> handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        List<String> errors = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.toList());
        
        String errorMessage = "约束验证失败: " + String.join(", ", errors);
        log.warn("约束验证异常: {}", errorMessage);
        
        return ResponseEntity.badRequest()
                .body(R.failed(errorMessage, HttpStatus.BAD_REQUEST.value(), "约束验证失败"));
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<R<String>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        
        return ResponseEntity.badRequest()
                .body(R.failed(e.getMessage(), HttpStatus.BAD_REQUEST.value(), "参数错误"));
    }
    
    /**
     * 处理非法状态异常
     */
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<R<String>> handleIllegalStateException(IllegalStateException e) {
        log.warn("非法状态异常: {}", e.getMessage());
        
        return ResponseEntity.badRequest()
                .body(R.failed(e.getMessage(), HttpStatus.BAD_REQUEST.value(), "状态错误"));
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<R<String>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(R.failed(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value(), "执行失败"));
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<R<String>> handleGenericException(Exception e) {
        log.error("未知异常: {}", e.getMessage(), e);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(R.failed("系统内部错误", HttpStatus.INTERNAL_SERVER_ERROR.value(), "系统异常"));
    }
    
    /**
     * 处理工作流执行超时异常
     */
    @ExceptionHandler(java.util.concurrent.TimeoutException.class)
    public ResponseEntity<R<String>> handleTimeoutException(java.util.concurrent.TimeoutException e) {
        log.warn("工作流执行超时: {}", e.getMessage());
        
        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                .body(R.failed("工作流执行超时", HttpStatus.REQUEST_TIMEOUT.value(), "执行超时"));
    }
    
    /**
     * 处理并发执行异常
     */
    @ExceptionHandler(java.util.concurrent.ExecutionException.class)
    public ResponseEntity<R<String>> handleExecutionException(java.util.concurrent.ExecutionException e) {
        log.error("并发执行异常: {}", e.getMessage(), e);
        
        Throwable cause = e.getCause();
        String message = cause != null ? cause.getMessage() : e.getMessage();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(R.failed("执行异常: " + message, HttpStatus.INTERNAL_SERVER_ERROR.value(), "并发执行失败"));
    }
}
