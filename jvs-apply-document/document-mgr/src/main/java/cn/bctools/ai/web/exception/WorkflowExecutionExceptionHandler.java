package cn.bctools.ai.web.exception;

import cn.bctools.common.utils.R;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 工作流执行异常处理器
 * 统一处理工作流执行过程中的异常
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = "cn.bctools.ai.web")
public class WorkflowExecutionExceptionHandler {

    /**
     * 处理非法参数异常
     */
    @RequestBody
    @ExceptionHandler(IllegalArgumentException.class)
    public R<String> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());

        return R.failed(e.getMessage(), HttpStatus.BAD_REQUEST.value(), "参数错误");
    }

    /**
     * 处理非法状态异常
     */
    @RequestBody
    @ExceptionHandler(IllegalStateException.class)
    public R<String> handleIllegalStateException(IllegalStateException e) {
        log.warn("非法状态异常: {}", e.getMessage());

        return R.failed(e.getMessage(), HttpStatus.BAD_REQUEST.value(), "状态错误");
    }


    /**
     * 处理工作流执行超时异常
     */
    @RequestBody
    @ExceptionHandler(TimeoutException.class)
    public R<String> handleTimeoutException(java.util.concurrent.TimeoutException e) {
        log.warn("执行超时: {}", e.getMessage());
        return R.failed("执行超时", HttpStatus.REQUEST_TIMEOUT.value(), "执行超时");
    }

    /**
     * 处理并发执行异常
     */
    @RequestBody
    @ExceptionHandler(ExecutionException.class)
    public R<String> handleExecutionException(java.util.concurrent.ExecutionException e) {
        log.error("并发执行异常: {}", e.getMessage(), e);

        Throwable cause = e.getCause();
        String message = cause != null ? cause.getMessage() : e.getMessage();

        return R.failed("执行异常: " + message, HttpStatus.INTERNAL_SERVER_ERROR.value(), "并发执行失败");
    }
}