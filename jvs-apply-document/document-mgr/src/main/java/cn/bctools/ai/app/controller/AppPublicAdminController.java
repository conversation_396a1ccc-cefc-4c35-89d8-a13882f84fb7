package cn.bctools.ai.app.controller;


import cn.bctools.ai.app.entity.vo.app_public.AppPublicApplyDetailVO;
import cn.bctools.ai.app.entity.vo.app_public.AppPublicApplyListVO;
import cn.bctools.ai.app.entity.vo.app_public.AppPublicExamineVO;
import cn.bctools.ai.app.service.AppPublicService;
import cn.bctools.common.utils.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 应用信息审核 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/app-public-admin")
@Api(tags = "应用信息审核")
public class AppPublicAdminController {


    AppPublicService appPublicService;


    @GetMapping("/apply-list")
    @ApiOperation("申请列表")
    public R<IPage<AppPublicApplyDetailVO>> applyList(Page<AppPublicApplyDetailVO> page, AppPublicApplyListVO params) {
        return R.ok(appPublicService.getApplyList(page, params));
    }

    @PostMapping("/examine")
    @Transactional
    @ApiOperation("审核")
    public R examine(@RequestBody AppPublicExamineVO params) {
        appPublicService.examine(params);
        return R.ok(null, "操作成功");
    }



}
