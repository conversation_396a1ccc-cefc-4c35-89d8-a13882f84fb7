package cn.bctools.ai.web.processor;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.web.dto.WorkflowExecutionRequest;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.stream.enums.StreamOutputType;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 工作流执行处理器接口
 * 使用策略模式处理不同类型的工作流执行
 * 
 * <AUTHOR>
 */
public interface WorkflowExecutionProcessor {
    
    /**
     * 获取支持的流式输出类型
     */
    StreamOutputType getSupportedOutputType();
    
    /**
     * 检查是否支持指定的执行请求
     * 
     * @param request 执行请求
     * @param httpRequest HTTP请求
     * @return 是否支持
     */
    boolean supports(WorkflowExecutionRequest request, HttpServletRequest httpRequest);
    
    /**
     * 处理工作流执行
     * 
     * @param appDetail 应用详情
     * @param user 用户信息
     * @param request 执行请求
     * @param httpRequest HTTP请求
     * @param httpResponse HTTP响应
     * @return 执行响应
     */
    Object processExecution(AppDetail appDetail, 
                           UserDto user, 
                           WorkflowExecutionRequest request,
                           HttpServletRequest httpRequest, 
                           HttpServletResponse httpResponse);
    
    /**
     * 获取处理器优先级（数值越小优先级越高）
     */
    default int getPriority() {
        return 100;
    }
}