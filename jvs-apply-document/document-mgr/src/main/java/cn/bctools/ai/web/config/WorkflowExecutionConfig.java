package cn.bctools.ai.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 工作流执行配置
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "workflow.execution")
public class WorkflowExecutionConfig {
    
    /**
     * 默认超时时间（秒）
     */
    private Integer defaultTimeoutSeconds = 300;
    
    /**
     * 最大超时时间（秒）
     */
    private Integer maxTimeoutSeconds = 3600;
    
    /**
     * 是否启用流式输出
     */
    private Boolean streamingEnabled = true;
    
    /**
     * 默认流式输出类型
     */
    private String defaultStreamType = "console";
    
    /**
     * 异步执行线程池配置
     */
    private AsyncConfig async = new AsyncConfig();
    
    /**
     * SSE配置
     */
    private SseConfig sse = new SseConfig();
    
    /**
     * WebSocket配置
     */
    private WebSocketConfig websocket = new WebSocketConfig();
    
    /**
     * Flux配置
     */
    private FluxConfig flux = new FluxConfig();
    
    @Data
    public static class AsyncConfig {
        /**
         * 核心线程数
         */
        private Integer corePoolSize = 5;
        
        /**
         * 最大线程数
         */
        private Integer maxPoolSize = 20;
        
        /**
         * 队列容量
         */
        private Integer queueCapacity = 100;
        
        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "workflow-async-";
        
        /**
         * 线程空闲时间（秒）
         */
        private Integer keepAliveSeconds = 60;
    }
    
    @Data
    public static class SseConfig {
        /**
         * 默认超时时间（毫秒）
         */
        private Long defaultTimeoutMs = 300000L;
        
        /**
         * 最大超时时间（毫秒）
         */
        private Long maxTimeoutMs = 1800000L;
        
        /**
         * 是否启用心跳
         */
        private Boolean heartbeatEnabled = true;
        
        /**
         * 心跳间隔（毫秒）
         */
        private Long heartbeatIntervalMs = 30000L;
    }
    
    @Data
    public static class WebSocketConfig {
        /**
         * WebSocket路径
         */
        private String path = "/ws/workflow-execution";
        
        /**
         * 允许的源
         */
        private String[] allowedOrigins = {"*"};
        
        /**
         * 消息缓冲区大小
         */
        private Integer messageBufferSize = 8192;
        
        /**
         * 连接超时时间（毫秒）
         */
        private Long connectionTimeoutMs = 300000L;
    }
    
    @Data
    public static class FluxConfig {
        /**
         * 缓冲区大小
         */
        private Integer bufferSize = 1000;
        
        /**
         * 背压策略
         */
        private String backpressureStrategy = "BUFFER";
        
        /**
         * 超时时间（秒）
         */
        private Integer timeoutSeconds = 600;
        
        /**
         * 是否启用错误恢复
         */
        private Boolean errorRecoveryEnabled = true;
    }
}
