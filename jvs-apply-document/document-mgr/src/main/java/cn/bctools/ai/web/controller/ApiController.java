package cn.bctools.ai.web.controller;

import cn.bctools.ai.app.component.MessageComponent;
import cn.bctools.ai.app.component.PersistentChatMemoryStore;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.data.*;
import cn.bctools.ai.app.entity.dto.ModelConfigDTO;
import cn.bctools.ai.app.entity.dto.SiteDTO;
import cn.bctools.ai.app.entity.dto.TextToSpeechBean;
import cn.bctools.ai.app.entity.vo.ChatMessageBodyVO;
import cn.bctools.ai.app.entity.vo.api.*;
import cn.bctools.ai.app.entity.vo.app_market.AppMarkerListVO;
import cn.bctools.ai.app.enums.InvokeFrom;
import cn.bctools.ai.app.interfaces.GetAppInfo;
import cn.bctools.ai.app.service.*;
import cn.bctools.ai.app.utils.AppModelConfigUtil;
import cn.bctools.ai.app.utils.JwtUtil;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.model.ModelInstance;
import cn.bctools.model.model_runtime.enums.ModelType;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.dto.BaseFile;
import cn.bctools.oss.template.OssTemplate;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.UploadFileException;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import dev.langchain4j.community.model.dashscope.QwenTtsModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import cn.bctools.ai.app.service.CompletionService;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.QueryParam;
import java.util.*;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api")
@Api(tags = "应用")
public class ApiController {

    AppsService appsService;

    AppModelConfigsService appModelConfigsService;

    SitesService sitesService;

    EndUsersService endUsersService;

    ConversationService conversationService;

    ConversationMessagesService conversationMessagesService;

    ConversationMessageFeedbacksService conversationMessageFeedbacksService;

    PinnedConversionsService pinnedConversionsService;

    PersistentChatMemoryStore persistentChatMemoryStore;

    MessageComponent messageComponent;

    CompletionService completionService;

    OssTemplate ossTemplate;

    AppMarketService appMarketService;

    @GetMapping("/passport")
    public R<String> passport(HttpServletRequest request) {

        // 从 header 中获取 code
        String code = request.getHeader("X-App-Code");
        if (code == null || code.isEmpty()) {
            throw new RuntimeException("X-App-Code header is missing.");
        }

        Sites site = sitesService.getOne(new QueryWrapper<Sites>().eq("code", code));
        if (site == null) {
            throw new RuntimeException("X-App-Code header is invalid.");
        }

        Apps app = appsService.getById(site.getAppId());
        if (app == null) {
            throw new RuntimeException("应用不存在");
        }

        String userId = UserCurrentUtils.getUserId();
        EndUsers endUser = endUsersService.getUser(app.getId(), userId);

        Map<String, String> payload = new HashMap<>();
        payload.put("iss", app.getId());
        payload.put("Web API Passport", site.getId());
        payload.put("app_code", code);
        payload.put("end_user_id", endUser.getId());

        String token = JwtUtil.generateToken(payload);

        return R.ok(token);
    }

    @GetMapping("/market-passport")
    public R<String> marketPassport(HttpServletRequest request) {
        String code = request.getHeader("X-App-Code");
        if (code == null || code.isEmpty()) {
            throw new RuntimeException("X-App-Code header is missing.");
        }

        AppMarkerListVO appMarket = appMarketService.getAppInfo(code);
        if (appMarket == null) {
            throw new RuntimeException("X-App-Code header is invalid.");
        }
        if (!appMarket.getStatus()) {
            throw new BusinessException("应用已下架");
        }

        String userId = UserCurrentUtils.getUserId();
        if (userId == null && !appMarket.getAllowGuest()) {
            throw new BusinessException("请先登录");
        }
        if (userId == null) {
            userId = "guest";
        }

        Apps app = appsService.getById(appMarket.getAppId());
        if (app == null) {
            throw new RuntimeException("应用不存在");
        }

        String configId = "";
        if (app.getMode().equals("chat")) {
            configId = appMarket.getAppConfigId();
        }

        EndUsers endUser = endUsersService.getUser(app.getId(), userId);

        Map<String, String> payload = new HashMap<>();
        payload.put("iss", app.getId());
        payload.put("Web API Passport", appMarket.getId());
        payload.put("app_code", code);
        payload.put("end_user_id", endUser.getId());
        payload.put("config_id", configId);

        String token = JwtUtil.generateToken(payload);

        return R.ok(token);
    }


    @GetMapping("/site")
    public R<SiteVO> site() {

        AppDetail appDetail = AppDetailContext.getObject();
        String appId = AppDetailContext.getObject().getId();
        String endUserId = AppDetailContext.getEndUserId();

        Sites site = sitesService.getOne(new QueryWrapper<Sites>().eq("app_id", appId));

        SiteVO siteVO = new SiteVO();
        siteVO.setAppId(appId);
        siteVO.setEndUserId(endUserId);

        SiteDTO siteDTO = BeanCopyUtil.copy(site, SiteDTO.class);

        String icon = site.getIcon();
        if (StringUtils.isBlank(icon)) {
            icon = appDetail.getIcon();
        }
        String iconUrl = ossTemplate.fileLink(icon, "edf-ai");
        siteDTO.setIconUrl(iconUrl);
        siteVO.setSite(siteDTO);

        return R.ok(siteVO);
    }

    @GetMapping("/parameters")
    public R<ModelConfigDTO> parameters() {
        return R.ok(AppDetailContext.getObject().getModel_config());
    }

    @PatchMapping("/conversations/{conversationId}/pin")
    public R pinConversation(@PathVariable("conversationId") String conversationId) {

        String role = "account";

        PinnedConversions pinnedConversions = pinnedConversionsService
                .getOne(new QueryWrapper<PinnedConversions>()
                        .eq("conversation_id", conversationId)
                        .eq("app_id", AppDetailContext.getObject().getId())
                        .eq("create_by_id", AppDetailContext.getEndUserId())
                        .eq("create_by_role", role)
                );

        if (pinnedConversions != null) {
            return R.ok();
        }

        String fromSource = "api";

        Conversation conversation = conversationService.lambdaQuery()
                .eq(Conversation::getId, conversationId)
                .eq(Conversation::getAppId, AppDetailContext.getObject().getId())
                .eq(Conversation::getFromSource, fromSource)
                .eq(Conversation::getFromEndUserId, AppDetailContext.getEndUserId())
                .one();

        if (conversation == null) {
            return R.failed("会话不存在");
        }

        pinnedConversionsService.save(new PinnedConversions()
                .setConversationId(conversationId)
                .setAppId(AppDetailContext.getObject().getId())
                .setCreateById(AppDetailContext.getEndUserId())
                .setCreateByRole(role)
        );

        return R.ok();
    }

    @PatchMapping("/conversations/{conversationId}/unpin")
    public R unpinConversation(@PathVariable("conversationId") String conversationId) {

        pinnedConversionsService.remove(new QueryWrapper<PinnedConversions>()
                .eq("conversation_id", conversationId)
                .eq("app_id", AppDetailContext.getObject().getId())
                .eq("create_by_id", AppDetailContext.getEndUserId())
        );

        return R.ok();
    }

    @GetMapping("/conversations")
    public R<IPage<ConversationListVO>> getConversations(Page<Conversation> page, @QueryParam("pinned") Boolean pinned) {

        String appId = AppDetailContext.getObject().getId();
        String endUserId = AppDetailContext.getEndUserId();

        List<String> conversationIds = pinnedConversionsService.lambdaQuery()
                .eq(PinnedConversions::getAppId, appId)
                .eq(PinnedConversions::getCreateById, endUserId)
                .list()
                .stream()
                .map(PinnedConversions::getConversationId)
                .toList();

        if (pinned &&  conversationIds.isEmpty()) {
            return R.ok(new Page<>());
        }

        IPage<ConversationListVO> conversations = conversationService.lambdaQuery()
                .eq(Conversation::getAppId, appId)
                .eq(Conversation::getFromEndUserId, endUserId)
                .eq(Conversation::getFromSource, "api")
                .in(pinned, Conversation::getId, conversationIds)
                .notIn(!conversationIds.isEmpty() && !pinned, Conversation::getId, conversationIds)
                .orderByDesc(Conversation::getCreateTime)
                .page(page)
                .convert(item -> {
                    ConversationListVO conversationListVO = new ConversationListVO();
                    conversationListVO.setId(item.getId());
                    conversationListVO.setName(item.getName());
                    conversationListVO.setInputs(JSON.parseObject(item.getInputs(), Map.class));
                    conversationListVO.setIntroduction(item.getIntroduction());
                    conversationListVO.setCreateTime(item.getCreateTime());
                    return conversationListVO;
                });


        return R.ok(conversations);

    }

    @DeleteMapping("/conversation")
    @ApiOperation("删除会话")
    public R deleteConversation(@QueryParam("conversation_id") String conversation_id) {

        Conversation conversation = conversationService.lambdaQuery()
                .eq(Conversation::getId, conversation_id)
                .eq(Conversation::getFromSource, "api")
                .eq(Conversation::getFromEndUserId, AppDetailContext.getEndUserId())
                .one();

        if (conversation == null) {
            return R.failed("会话不存在");
        }

        conversationService.removeById(conversation_id);

        return R.ok();
    }

    @PostMapping("rename")
    public R rename(@RequestBody ConversationRenameVO param) {

        Conversation conversation = conversationService.lambdaQuery()
                .eq(Conversation::getId, param.getConversation_id())
                .eq(Conversation::getFromSource, "api")
                .eq(Conversation::getFromEndUserId, AppDetailContext.getEndUserId())
                .one();

        if (conversation == null) {
            return R.failed("会话不存在");
        }

        conversationService.lambdaUpdate()
                .eq(Conversation::getId, param.getConversation_id())
                .eq(Conversation::getFromSource, "api")
                .eq(Conversation::getFromEndUserId, AppDetailContext.getEndUserId())
                .set(Conversation::getName, param.getName())
                .update();

        return R.ok();
    }

    @GetMapping("/messages")
    public R<IPage<ConversationMessageVO>> getMessages(Page<ConversationMessages> page, @QueryParam("conversation_id") String conversation_id) {

        Conversation conversation = conversationService.lambdaQuery()
                .eq(Conversation::getId, conversation_id)
                .eq(Conversation::getFromSource, "api")
                .eq(Conversation::getFromEndUserId, AppDetailContext.getEndUserId())
                .one();

        if (conversation == null) {
            return R.failed("会话不存在");
        }

        IPage<ConversationMessages> messages = conversationMessagesService.lambdaQuery()
                .eq(ConversationMessages::getConversationId, conversation_id)
                .orderByAsc(ConversationMessages::getCreateTime)
                .page(page);


        List<String> messageIds = messages.getRecords().stream().map(ConversationMessages::getId).toList();
        Map<String, String> ratingMap = new HashMap<>();
        if (!messageIds.isEmpty()) {
            List<ConversationMessageFeedbacks> ms = conversationMessageFeedbacksService.lambdaQuery()
                    .in(ConversationMessageFeedbacks::getMessageId, messageIds)
                    .eq(ConversationMessageFeedbacks::getFromSource, "user")
                    .list();
            ms.forEach(item -> {
                ratingMap.put(item.getMessageId(), item.getRating());
            });
        }

        IPage<ConversationMessageVO> rtn = messages.convert(item -> {
            ConversationMessageVO conversationMessageVO = new ConversationMessageVO();
            conversationMessageVO.setId(item.getId());
//            conversationMessageVO.setAgent_thoughts(item.getAgentThoughts());
            conversationMessageVO.setAnswer(item.getAnswer());
            conversationMessageVO.setConversation_id(item.getConversationId());
            conversationMessageVO.setInputs(JSON.parseObject(item.getInputs(), Map.class));
            conversationMessageVO.setParent_message_id(item.getParentMessageId());
            conversationMessageVO.setQuery(item.getQuery());

            // 反馈
            ConversationMessageVO.feedbackVO feedback = new ConversationMessageVO.feedbackVO();
            feedback.setRating(ratingMap.get(item.getId()));
            conversationMessageVO.setFeedback(feedback);
            return conversationMessageVO;
        });

        return R.ok(rtn);

    }


    @PostMapping(value = "/chat-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Object chatMessages(@RequestBody ChatMessageBodyVO param) {

        // 获取模型配置
        ModelConfigDTO configDTO = AppDetailContext.getObject().getModel_config();
        param.setModel_config(configDTO);

        try {
            return completionService.generate(param, InvokeFrom.WEB_APP);
        } catch (Exception e) {
            log.error("", e);
            return R.failed("请求失败");
        }

    }

    @GetMapping("/chat-messages/{messageId}/suggested-questions")
    @ApiOperation("获取下一步建议问题")
    public R<List<String>> getSuggestedQuestions(@PathVariable("messageId") String messageId) {
        Apps app = AppDetailContext.getObject();
        String appId = app.getId();
        return R.ok(conversationService.getSuggestedQuestions(appId, messageId));
    }

    @PostMapping("/{messageId}/feedbacks")
    public R feedback(@PathVariable("messageId") String messageId, @RequestBody FeedbackVO feedback) {
        ConversationMessages message = conversationMessagesService.getById(messageId);
        if (message == null) {
            return R.failed("消息不存在");
        }

        ConversationMessageFeedbacks feedbacks = conversationMessageFeedbacksService.lambdaQuery()
                .eq(ConversationMessageFeedbacks::getMessageId, messageId)
                .eq(ConversationMessageFeedbacks::getFromEndUserId, AppDetailContext.getEndUserId())
                .eq(ConversationMessageFeedbacks::getFromSource, "user")
                .one();
        if (feedbacks != null) {
            if (feedback.getRating() == null) {
                conversationMessageFeedbacksService.removeById(feedbacks.getId());
                return R.ok("取消反馈成功");
            }
            throw new BusinessException("请勿重复反馈");
        }

        List<String> allowType = Arrays.asList("like", "dislike");
        if (!allowType.contains(feedback.getRating())) {
            throw new BusinessException("反馈类型错误");
        }

        ConversationMessageFeedbacks fb = new ConversationMessageFeedbacks();
        fb.setAppId(message.getAppId());
        fb.setConversationId(message.getConversationId());
        fb.setMessageId(messageId);
        fb.setRating(feedback.getRating());
        fb.setFromSource("user");
        fb.setFromEndUserId(AppDetailContext.getEndUserId());
        conversationMessageFeedbacksService.save(fb);

        return R.ok("反馈成功");
    }


    @PostMapping("/text-to-audio")
    public void textToAudio(@RequestBody TextToAudioVO param)  {


        AppDetail app = AppDetailContext.getObject();

        String voice = "";

        // 获取模型配置
        if (app.getMode().equals("chat")) {
            ModelConfigDTO modelConfigs = app.getModel_config();
            TextToSpeechBean textToSpeech = modelConfigs.getText_to_speech();
            if (textToSpeech == null || !textToSpeech.isEnabled()) {
                throw new BusinessException("TTS is not support");
            }
            if (textToSpeech.getVoice() != null) {
                voice = textToSpeech.getVoice();
            }
        }

        ModelInstance instance = new ModelInstance(ModelType.TTS);
        instance.invoke_tts(voice, param.getText());

    }

    @PostMapping("/audio-to-text")
    public R audioToText(@RequestBody BaseFile file) {

        if (!ossTemplate.checkExists(file.getBucketName(), file.getFileName())) {
            return R.failed("文件不存在");
        }

        String fileLink = ossTemplate.fileLink(file.getFileName(), file.getBucketName());

        ModelInstance instance = new ModelInstance(ModelType.SPEECH2TEXT);
        String content = instance.invoke_speech2text(fileLink);

        ossTemplate.delete(file.getBucketName(), file.getFileName());

        return R.ok(content);

    }






}
