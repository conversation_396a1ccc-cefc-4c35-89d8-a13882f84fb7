<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流执行演示 - 统一接口</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header p { color: #7f8c8d; }
        
        .demo-section { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .demo-section h2 { color: #34495e; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
        
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; 
        }
        .form-group textarea { height: 100px; resize: vertical; }
        
        .button-group { display: flex; gap: 10px; flex-wrap: wrap; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; transition: all 0.3s; }
        .btn-primary { background: #3498db; color: white; }
        .btn-primary:hover { background: #2980b9; }
        .btn-success { background: #27ae60; color: white; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-warning:hover { background: #e67e22; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-danger:hover { background: #c0392b; }
        
        .status-panel { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .status-card { padding: 15px; border-radius: 6px; text-align: center; }
        .status-card h3 { margin-bottom: 5px; }
        .status-idle { background: #ecf0f1; color: #2c3e50; }
        .status-running { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        
        .progress-container { margin-bottom: 20px; }
        .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #3498db, #27ae60); transition: width 0.3s ease; width: 0%; }
        .progress-text { text-align: center; margin-top: 5px; font-weight: bold; }
        
        .output-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .output-panel { border: 1px solid #ddd; border-radius: 4px; }
        .output-header { background: #34495e; color: white; padding: 10px; font-weight: bold; }
        .output-content { height: 400px; overflow-y: auto; padding: 10px; font-family: 'Courier New', monospace; font-size: 12px; }
        
        .log-entry { margin-bottom: 5px; padding: 5px; border-radius: 3px; }
        .log-info { background: #d1ecf1; }
        .log-success { background: #d4edda; }
        .log-warning { background: #fff3cd; }
        .log-error { background: #f8d7da; }
        
        .node-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
        .node-card { border: 1px solid #ddd; border-radius: 6px; padding: 15px; background: white; }
        .node-card.running { border-left: 4px solid #f39c12; }
        .node-card.completed { border-left: 4px solid #27ae60; }
        .node-card.failed { border-left: 4px solid #e74c3c; }
        .node-title { font-weight: bold; margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center; }
        .node-status { padding: 2px 8px; border-radius: 12px; font-size: 11px; color: white; }
        .node-status.running { background: #f39c12; }
        .node-status.completed { background: #27ae60; }
        .node-status.failed { background: #e74c3c; }
        .node-details { font-size: 12px; color: #666; }
        .json-content { background: #f8f9fa; padding: 8px; border-radius: 4px; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 100px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 工作流执行演示 - 统一接口</h1>
            <p>支持Flux、SSE、WebSocket、同步执行多种方式</p>
        </div>
        
        <!-- 配置面板 -->
        <div class="demo-section">
            <h2>📋 执行配置</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <div class="form-group">
                        <label>流式输出类型</label>
                        <select id="streamType">
                            <option value="">自动检测</option>
                            <option value="flux">Flux流式输出</option>
                            <option value="sse">SSE流式输出</option>
                            <option value="websocket">WebSocket流式输出</option>
                            <option value="console">同步执行</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>超时时间（秒）</label>
                        <input type="number" id="timeoutSeconds" value="300" min="1" max="3600">
                    </div>
                    <div class="form-group">
                        <label>是否异步执行</label>
                        <select id="asyncExecution">
                            <option value="false">否</option>
                            <option value="true">是</option>
                        </select>
                    </div>
                </div>
                <div>
                    <div class="form-group">
                        <label>输入参数（JSON格式）</label>
                        <textarea id="inputParams">{"input": "测试数据", "param1": "value1", "param2": "value2"}</textarea>
                    </div>
                </div>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="executeWorkflow()">🚀 执行工作流</button>
                <button class="btn btn-warning" onclick="recommendProcessor()">🎯 推荐处理器</button>
                <button class="btn btn-success" onclick="getProcessorInfo()">ℹ️ 处理器信息</button>
                <button class="btn btn-danger" onclick="clearOutput()">🧹 清空输出</button>
            </div>
        </div>
        
        <!-- 状态面板 -->
        <div class="demo-section">
            <h2>📊 执行状态</h2>
            <div class="status-panel">
                <div class="status-card status-idle" id="connectionStatus">
                    <h3>连接状态</h3>
                    <p>未连接</p>
                </div>
                <div class="status-card status-idle" id="executionStatus">
                    <h3>执行状态</h3>
                    <p>等待中</p>
                </div>
                <div class="status-card status-idle" id="nodeCount">
                    <h3>节点统计</h3>
                    <p>0/0</p>
                </div>
                <div class="status-card status-idle" id="executionTime">
                    <h3>执行时间</h3>
                    <p>0ms</p>
                </div>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">0%</div>
            </div>
        </div>
        
        <!-- 输出面板 -->
        <div class="demo-section">
            <h2>📤 执行输出</h2>
            <div class="output-container">
                <div class="output-panel">
                    <div class="output-header">📋 执行日志</div>
                    <div class="output-content" id="executionLogs"></div>
                </div>
                <div class="output-panel">
                    <div class="output-header">📊 原始数据</div>
                    <div class="output-content" id="rawData"></div>
                </div>
            </div>
        </div>
        
        <!-- 节点详情 -->
        <div class="demo-section">
            <h2>🔧 节点执行详情</h2>
            <div class="node-grid" id="nodeDetails"></div>
        </div>
    </div>

    <script>
        let currentExecutionId = null;
        let eventSource = null;
        let webSocket = null;
        let startTime = null;
        let nodes = new Map();

        // 执行工作流
        async function executeWorkflow() {
            const streamType = document.getElementById('streamType').value;
            const timeoutSeconds = parseInt(document.getElementById('timeoutSeconds').value);
            const asyncExecution = document.getElementById('asyncExecution').value === 'true';
            const inputParams = document.getElementById('inputParams').value;

            try {
                const params = JSON.parse(inputParams);
                const request = {
                    params: params,
                    streamType: streamType || undefined,
                    streaming: streamType !== 'console',
                    timeoutSeconds: timeoutSeconds,
                    async: asyncExecution
                };

                addLog('🚀 开始执行工作流...', 'log-info');
                updateConnectionStatus('连接中', 'status-running');
                startTime = Date.now();

                // 根据流式类型设置请求头
                const headers = {
                    'Content-Type': 'application/json'
                };

                if (streamType === 'sse') {
                    headers['Accept'] = 'text/event-stream';
                } else if (streamType === 'flux') {
                    headers['Accept'] = 'application/stream+json';
                } else if (streamType === 'websocket') {
                    headers['X-Stream-Type'] = 'websocket';
                }

                const response = await fetch('/api/workflows/run', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(request)
                });

                if (streamType === 'sse' && response.headers.get('content-type')?.includes('text/event-stream')) {
                    handleSSEResponse(response);
                } else if (streamType === 'flux' && response.headers.get('content-type')?.includes('application/stream+json')) {
                    handleFluxResponse(response);
                } else {
                    handleSyncResponse(response);
                }

            } catch (error) {
                addLog('❌ 执行失败: ' + error.message, 'log-error');
                updateConnectionStatus('连接失败', 'status-failed');
            }
        }

        // 处理SSE响应
        function handleSSEResponse(response) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            function readStream() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        addLog('📡 SSE流结束', 'log-info');
                        updateConnectionStatus('已完成', 'status-completed');
                        return;
                    }

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    lines.forEach(line => {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.substring(6));
                                handleStreamEvent(data);
                            } catch (e) {
                                console.error('解析SSE数据失败:', e);
                            }
                        }
                    });

                    return readStream();
                });
            }

            updateConnectionStatus('SSE已连接', 'status-running');
            readStream().catch(error => {
                addLog('❌ SSE流错误: ' + error.message, 'log-error');
                updateConnectionStatus('连接错误', 'status-failed');
            });
        }

        // 处理Flux响应
        function handleFluxResponse(response) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            function readStream() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        addLog('🌊 Flux流结束', 'log-info');
                        updateConnectionStatus('已完成', 'status-completed');
                        return;
                    }

                    const chunk = decoder.decode(value);
                    try {
                        const data = JSON.parse(chunk);
                        handleStreamEvent(data);
                    } catch (e) {
                        // 可能是部分数据，继续读取
                        console.debug('部分Flux数据:', chunk);
                    }

                    return readStream();
                });
            }

            updateConnectionStatus('Flux已连接', 'status-running');
            readStream().catch(error => {
                addLog('❌ Flux流错误: ' + error.message, 'log-error');
                updateConnectionStatus('连接错误', 'status-failed');
            });
        }

        // 处理同步响应
        async function handleSyncResponse(response) {
            try {
                const result = await response.json();
                addLog('✅ 同步执行完成', 'log-success');
                updateConnectionStatus('已完成', 'status-completed');
                
                document.getElementById('rawData').textContent = JSON.stringify(result, null, 2);
                
                if (result.data && result.data.executionId) {
                    currentExecutionId = result.data.executionId;
                }
                
            } catch (error) {
                addLog('❌ 解析响应失败: ' + error.message, 'log-error');
                updateConnectionStatus('解析失败', 'status-failed');
            }
        }

        // 处理流式事件
        function handleStreamEvent(data) {
            document.getElementById('rawData').textContent = JSON.stringify(data, null, 2);

            if (data.event) {
                switch (data.event) {
                    case 'workflow_started':
                        handleWorkflowStarted(data.data);
                        break;
                    case 'node_started':
                        handleNodeStarted(data.data);
                        break;
                    case 'node_completed':
                        handleNodeCompleted(data.data);
                        break;
                    case 'node_failed':
                        handleNodeFailed(data.data);
                        break;
                    case 'workflow_completed':
                        handleWorkflowCompleted(data.data);
                        break;
                    case 'workflow_failed':
                        handleWorkflowFailed(data.data);
                        break;
                    case 'progress_update':
                        handleProgressUpdate(data.data);
                        break;
                }
            }
        }

        // 事件处理函数
        function handleWorkflowStarted(data) {
            currentExecutionId = data.executionId;
            updateExecutionStatus('执行中', 'status-running');
            updateNodeCount(0, data.totalNodes);
            addLog(`🚀 工作流开始执行 - ID: ${data.executionId}, 总节点: ${data.totalNodes}`, 'log-info');
        }

        function handleNodeStarted(data) {
            nodes.set(data.nodeId, data);
            updateNodeCard(data);
            addLog(`▶️ 节点开始: ${data.nodeName} (${data.functionName})`, 'log-info');
        }

        function handleNodeCompleted(data) {
            nodes.set(data.nodeId, data);
            updateNodeCard(data);
            addLog(`✅ 节点完成: ${data.nodeName}, 耗时: ${data.duration}ms`, 'log-success');
        }

        function handleNodeFailed(data) {
            nodes.set(data.nodeId, data);
            updateNodeCard(data);
            addLog(`❌ 节点失败: ${data.nodeName}, 错误: ${data.errorMessage}`, 'log-error');
        }

        function handleWorkflowCompleted(data) {
            updateExecutionStatus('已完成', 'status-completed');
            updateExecutionTime();
            addLog('🎉 工作流执行完成', 'log-success');
        }

        function handleWorkflowFailed(data) {
            updateExecutionStatus('执行失败', 'status-failed');
            updateExecutionTime();
            addLog('💥 工作流执行失败', 'log-error');
        }

        function handleProgressUpdate(data) {
            updateProgress(data.progress);
            updateNodeCount(data.executedNodes, data.totalNodes);
        }

        // UI更新函数
        function updateConnectionStatus(status, className) {
            const element = document.getElementById('connectionStatus');
            element.className = 'status-card ' + className;
            element.querySelector('p').textContent = status;
        }

        function updateExecutionStatus(status, className) {
            const element = document.getElementById('executionStatus');
            element.className = 'status-card ' + className;
            element.querySelector('p').textContent = status;
        }

        function updateNodeCount(executed, total) {
            document.getElementById('nodeCount').querySelector('p').textContent = `${executed}/${total}`;
        }

        function updateExecutionTime() {
            if (startTime) {
                const duration = Date.now() - startTime;
                document.getElementById('executionTime').querySelector('p').textContent = duration + 'ms';
            }
        }

        function updateProgress(progress) {
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = progress + '%';
        }

        function addLog(message, className) {
            const logs = document.getElementById('executionLogs');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + className;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        function updateNodeCard(nodeData) {
            const nodeDetails = document.getElementById('nodeDetails');
            let card = document.getElementById('node-' + nodeData.nodeId);

            if (!card) {
                card = document.createElement('div');
                card.id = 'node-' + nodeData.nodeId;
                card.className = 'node-card';
                nodeDetails.appendChild(card);
            }

            card.className = 'node-card ' + nodeData.status;

            const statusText = {
                'running': '执行中',
                'completed': '已完成',
                'failed': '失败'
            }[nodeData.status] || nodeData.status;

            card.innerHTML = `
                <div class="node-title">
                    <span>${nodeData.nodeName}</span>
                    <span class="node-status ${nodeData.status}">${statusText}</span>
                </div>
                <div class="node-details">
                    <p><strong>功能:</strong> ${nodeData.functionName}</p>
                    <p><strong>类型:</strong> ${nodeData.nodeType}</p>
                    ${nodeData.duration ? `<p><strong>耗时:</strong> ${nodeData.duration}ms</p>` : ''}
                    ${nodeData.inputs ? `<p><strong>输入:</strong></p><div class="json-content">${JSON.stringify(nodeData.inputs, null, 2)}</div>` : ''}
                    ${nodeData.outputs ? `<p><strong>输出:</strong></p><div class="json-content">${JSON.stringify(nodeData.outputs, null, 2)}</div>` : ''}
                    ${nodeData.errorMessage ? `<p><strong>错误:</strong> ${nodeData.errorMessage}</p>` : ''}
                </div>
            `;
        }

        // 推荐处理器
        async function recommendProcessor() {
            try {
                const streamType = document.getElementById('streamType').value;
                const inputParams = document.getElementById('inputParams').value;
                const params = JSON.parse(inputParams);

                const request = {
                    params: params,
                    streamType: streamType || undefined,
                    streaming: streamType !== 'console'
                };

                const response = await fetch('/api/workflows/processors/recommend', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(request)
                });

                const result = await response.json();
                if (result.success) {
                    addLog(`🎯 推荐处理器: ${result.data.processor.class.simpleName}`, 'log-info');
                    addLog(`📝 推荐理由: ${result.data.reason}`, 'log-info');
                } else {
                    addLog(`❌ 获取推荐失败: ${result.msg}`, 'log-error');
                }
            } catch (error) {
                addLog(`❌ 推荐处理器失败: ${error.message}`, 'log-error');
            }
        }

        // 获取处理器信息
        async function getProcessorInfo() {
            try {
                const response = await fetch('/api/workflows/processors/info');
                const result = await response.json();
                
                if (result.success) {
                    addLog(`ℹ️ 处理器信息: 共${result.data.totalProcessors}个处理器`, 'log-info');
                    document.getElementById('rawData').textContent = JSON.stringify(result.data, null, 2);
                } else {
                    addLog(`❌ 获取处理器信息失败: ${result.msg}`, 'log-error');
                }
            } catch (error) {
                addLog(`❌ 获取处理器信息失败: ${error.message}`, 'log-error');
            }
        }

        // 清空输出
        function clearOutput() {
            document.getElementById('executionLogs').innerHTML = '';
            document.getElementById('rawData').textContent = '';
            document.getElementById('nodeDetails').innerHTML = '';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '0%';
            
            updateConnectionStatus('未连接', 'status-idle');
            updateExecutionStatus('等待中', 'status-idle');
            updateNodeCount(0, 0);
            document.getElementById('executionTime').querySelector('p').textContent = '0ms';
            
            nodes.clear();
            currentExecutionId = null;
            startTime = null;
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            addLog('📋 页面加载完成，请配置参数后执行工作流', 'log-info');
        };
    </script>
</body>
</html>
