# for more details, please refer to https://help.aliyun.com/zh/model-studio/getting-started/models
model: qwen-vl-max-0201
label:
  en_US: qwen-vl-max-0201
model_type: llm
features:
  - vision
  - agent-thought
model_properties:
  mode: chat
  context_size: 8192
parameter_rules:
  - name: top_p
    use_template: top_p
    type: float
    defaultValue: 0.8
    min: 0.1
    max: 0.9
    help:
      zh_<PERSON>: 生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。
      en_US: The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated.
  - name: top_k
    type: int
    min: 0
    max: 99
    label:
      zh_Hans: 取样数量
      en_US: Top k
    help:
      zh_Hans: 生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。
      en_US: The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated.
  - name: seed
    required: false
    type: int
    defaultValue: 1234
    label:
      zh_Hans: 随机种子
      en_US: Random seed
    help:
      zh_Hans: 生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。
      en_US: The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time.
  - name: response_format
    use_template: response_format
pricing:
  input: '0.02'
  output: '0.02'
  unit: '0.001'
  currency: RMB
deprecated: true
